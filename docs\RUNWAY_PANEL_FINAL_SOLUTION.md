# Runway Panel Height - Current Implementation

## TL;DR

**Don't standardize heights.** The rail container matches the media height (`--runway-slider-h`), the inner content scrolls within it, and the release point is computed dynamically based on what sections are present. The implementation uses manual position toggling (absolute/fixed/released) with scroll-based calculations.

---

## The Three-Part Solution

### 1. CSS: Rail Container Height = Media Height

```css
.product__info {
  position: absolute !important;
  height: var(--runway-slider-h) !important;
  right: var(--pi-edge-gap, clamp(12px, 2.5vw, 22px)) !important;
  top: var(--pi-start-top, var(--pi-top-gap, 0px)) !important;
  width: var(--runway-info-w, 360px) !important;
}

/* Fixed state: stick under header */
.product__info.is-fixed {
  position: fixed !important;
  top: var(--header-h, 0) !important;
}

/* Released state: pin to computed position */
.product__info.is-released {
  position: absolute !important;
  top: var(--release-top, auto) !important;
}
```

The rail container shares the same viewport height as the media gallery using `--runway-slider-h: calc(100vh - var(--header-h) - var(--fold-buffer))`.

### 2. CSS: Inner Content Scrolls

```css
.product-info {
  height: 100% !important;
  max-height: 100% !important;
  overflow: auto !important;
  overscroll-behavior: contain;
  scrollbar-gutter: stable both-edges;
}
```

The inner `.product-info` card scrolls within the fixed-height `.product__info` container. No content gets cut off.

### 3. JavaScript: Dynamic Release Calculation

```javascript
const findReleaseAnchor = () => {
  // 1) Explicit marker: [data-rail-end]
  const marked = document.querySelector('[data-rail-end]');
  if (marked) return marked;

  // 2) Heuristic: find common "mate" sections
  // (recommendations, shop the look, keep exploring, etc.)
  const candidates = [];
  let cursor = nextSection;
  while (cursor && checks < MAX_CHECK) {
    candidates.push(cursor);
    if (cursor.querySelector('.product-recs, .shop-the-look, [data-rail-mate]'))
      break;
    cursor = cursor.nextElementSibling;
  }
  return candidates[candidates.length - 1] || nextSection || sectionRoot;
};

const computeRelease = () => {
  const sliderH = getSliderH();
  const sectionTop = sectionRoot.getBoundingClientRect().top + window.scrollY;
  const anchor = findReleaseAnchor();
  const anchorBottom = anchor.getBoundingClientRect().top + window.scrollY + anchor.offsetHeight;

  const releaseTop = Math.max(0, Math.round(anchorBottom - sectionTop - sliderH));
  sectionRoot.style.setProperty('--release-top', releaseTop + 'px');
};

const updateMode = () => {
  const { releaseTop, sectionTop } = computeRelease();
  const y = window.scrollY;
  const header = getHeaderH();

  const fixedStart = sectionTop - header;
  const fixedEnd = sectionTop + releaseTop;

  let mode = 'natural';
  if (y >= fixedStart && y < fixedEnd) mode = 'fixed';
  else if (y >= fixedEnd) mode = 'released';

  infoWrap.classList.toggle('is-fixed', mode === 'fixed');
  infoWrap.classList.toggle('is-released', mode === 'released');
};
```

The release point is computed as: `(anchor bottom) - (section top) - (rail height)`

This works with any theme composition—no height coordination needed.

---

## Why This Works

### Problem 1: Panel Taller Than Content → Content Cut Off

**Old approach:** Panel had `height: auto`, so it could grow taller than the viewport. Bottom content was inaccessible.

**Current approach:** Panel has fixed height (`--runway-slider-h`), inner content scrolls. Everything is accessible.

### Problem 2: Panel Doesn't Release Properly

**Old approach:** Release calculation assumed `panelHeight < sectionHeight`. When panel was taller, the math broke.

**Current approach:** Panel height is always `--runway-slider-h` (viewport-based). Release is computed against a dynamic anchor (gallery + mate sections). The math always works.

### Problem 3: Hermès-Style Alignment

**Old approach:** Would require standardizing heights across panel and sections.

**Current approach:** Compute the release anchor dynamically:

- If merchant adds `[data-rail-end]` to a section, use that
- Otherwise, detect common "mate" sections (recommendations, etc.)
- Release when rail bottom reaches anchor bottom

Result: Hermès-style alignment without height standardization.

## Implementation Approach

This implementation uses **manual position toggling** with scroll-based calculations:

1. **Three states**: natural (absolute), fixed (sticky under header), released (pinned to anchor)
2. **Scroll listener**: Uses `requestAnimationFrame` to efficiently update state on scroll
3. **Dynamic anchor detection**: Finds release point based on actual sections present
4. **CSS custom properties**: Uses `--release-top` to communicate computed position to CSS

### Why Not Native CSS Sticky?

Native `position: sticky` would be simpler, but it has limitations with the current theme architecture:

- Sticky elements must be in the normal document flow (can't use `right` positioning easily)
- Would require restructuring the HTML to wrap gallery + rail in a scope container
- Current approach gives more control over the exact release behavior
- Works with the existing layout system without major structural changes

---

## Merchant Control

Merchants can control the release point with data attributes:

### Option 1: Explicit End Marker

```liquid
<div class="shopify-section" data-rail-end>
  {% section 'newsletter' %}
</div>
```

The rail will release when it reaches the bottom of this section.

### Option 2: Mate Markers

```liquid
<div class="shopify-section" data-rail-mate>
  {% section 'product-recommendations' %}
</div>

<div class="shopify-section" data-rail-mate>
  {% section 'recently-viewed' %}
</div>

<div class="shopify-section" data-rail-end>
  {% section 'keep-exploring' %}
</div>
```

The rail will stay sticky through all `[data-rail-mate]` sections and release at `[data-rail-end]`.

### Option 3: Auto-Detection (Default)

If no markers are present, the script uses heuristics:
- Looks for `.product-recs`, `.shop-the-look`, `.keep-exploring`, etc.
- Includes up to 6 sections after the product
- Stops at the first "mate" section found

This gives sensible defaults without requiring merchant configuration.

---

## Benefits

### ✅ No Height Standardization

- Panel can have any amount of content
- Sections can have any height
- Works with user customization
- No coordination needed

### ✅ No Content Cutoff

- Inner content scrolls within fixed-height container
- Styled scrollbar (subtle, luxury aesthetic)
- Everything is accessible

### ✅ Flexible Release Point

- Explicit control via `[data-rail-end]`
- Smart defaults via heuristics
- Works with any theme composition

### ✅ Hermès-Style Alignment

- Rail releases in sync with content sections
- No height matching required
- Adapts to what's actually present

### ✅ Performance Optimized

- Uses existing `--runway-slider-h` variable
- RequestAnimationFrame for smooth updates
- MutationObserver for dynamic content
- Passive event listeners

---

## Testing

### Quick Test

1. Add lots of content to the panel (long description, many blocks)
2. Scroll down - panel should stick and inner content should be scrollable
3. Scroll to bottom - panel should release smoothly
4. Scroll back up - panel should stick again

### Console Checks

```javascript
// Check rail height
const rail = document.querySelector('.product__info');
console.log('Rail height:', rail.offsetHeight, 'px');
console.log('Rail height matches slider:', 
  rail.offsetHeight === parseInt(getComputedStyle(rail).getPropertyValue('--runway-slider-h')));

// Check inner content
const inner = rail.querySelector('.product-info');
console.log('Inner content height:', inner.scrollHeight, 'px');
console.log('Is scrollable:', inner.scrollHeight > inner.offsetHeight);

// Check release point
const section = document.querySelector('.product');
console.log('Release top:', getComputedStyle(section).getPropertyValue('--release-top'));

// Check anchor
const anchor = document.querySelector('[data-rail-end]') || 
               document.querySelector('.product-recs') ||
               section.closest('.shopify-section').nextElementSibling;
console.log('Release anchor:', anchor?.id || anchor?.className);
```

### Visual Test

1. **Short panel content:** Inner content should not scroll, panel should release at gallery bottom
2. **Tall panel content:** Inner content should scroll, panel should release at anchor bottom
3. **With mate sections:** Panel should stay sticky through mates, release at end
4. **Without mate sections:** Panel should release at gallery bottom

---

## Customization

### Adjust Scrollbar Style

```css
.product-info::-webkit-scrollbar {
  width: 8px; /* Wider scrollbar */
}
.product-info::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3); /* Darker thumb */
}
```

### Hide Scrollbar

```css
.product-info {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.product-info::-webkit-scrollbar {
  display: none;
}
```

### Change Release Heuristics

Edit the `findReleaseAnchor` function to match your theme's section patterns:

```javascript
if (cursor.querySelector('.your-custom-section, [data-your-attribute]'))
  break;
```

### Adjust Max Mate Sections

```javascript
const MAX_CHECK = 4; // Change from 6 to 4
```

---

## Migration from Old Approach

If you were using the viewport-constrained approach (from my earlier suggestion), the changes are:

1. **CSS:** Remove `max-height` constraint on `.product__info`, add fixed `height: var(--runway-slider-h)`
2. **CSS:** Move scrolling from `.product__info` to `.product-info` (inner card)
3. **JavaScript:** Replace viewport-based release logic with anchor-based logic

The new approach is cleaner, more robust, and doesn't require viewport calculations.

---

## Files Modified

1. **sections/product.liquid**
   - Lines ~205-224: Rail container height
   - Lines ~241-293: Inner content scrolling and scrollbar styling
   - Lines ~3345-3464: Dynamic release calculation

2. **docs/RUNWAY_PANEL_FINAL_SOLUTION.md** (this file)
   - Quick reference for the solution

3. **docs/RUNWAY_PANEL_HEIGHT_SOLUTION.md** (updated)
   - Comprehensive guide with testing and troubleshooting

---

## Credits

This solution is based on the insight that:

> You already have almost all the hooks to make the sticky rail robust to any theme composition—just finish the "release" math and constrain the panel height to the viewport minus header/fold.

The key was recognizing that:
1. `--runway-slider-h` is the single source of truth for height
2. The inner card should scroll, not the outer container
3. The release point should be computed, not assumed

---

## Next Steps

1. ✅ Test with your actual product content
2. ✅ Add `[data-rail-mate]` or `[data-rail-end]` markers if needed
3. ✅ Adjust scrollbar styling to match your brand
4. ✅ Monitor user feedback

The implementation is complete and ready to use! 🎉

