{% comment %}
  Slideshow (base scaffold)
{% endcomment %}

{%- liquid
  assign show_arrows = section.settings.show_arrows | default: true
  assign show_thumbnails = section.settings.show_thumbnails | default: false
  assign show_pagination = section.settings.show_pagination | default: true
  assign show_progress = section.settings.show_progress | default: false
  assign enable_loop = section.settings.enable_loop | default: true
  assign enable_autoplay = section.settings.enable_autoplay | default: false
  assign autoplay_speed = section.settings.autoplay_speed | default: 5000
  assign animation_speed = section.settings.animation_speed | default: 500
  assign animation_type = section.settings.animation_type | default: 'slide'

  assign container_width = section.settings.container_width | default: 'inherit'
  assign height_mode = section.settings.height_mode | default: 'auto'
  assign desktop_height = section.settings.desktop_height | default: 600
  assign mobile_height = section.settings.mobile_height | default: 420
  assign subtract_header = section.settings.subtract_header | default: false
  assign image_position = section.settings.image_position | default: 'center'

  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="slideshow slideshow--{{ animation_type }}{% if container_width == 'full_bleed' %} full-width{% endif %}" data-section="{{ section.id }}" data-height-mode="{{ height_mode }}"
  data-loop="{{ enable_loop }}" data-autoplay="{{ enable_autoplay }}" data-autoplay-speed="{{ autoplay_speed }}" data-animation-speed="{{ animation_speed }}">
  <div class="slideshow__inner{% if container_width != 'inherit' %} slideshow__inner--custom{% endif %} {% unless container_width == 'full_bleed' %}page-width{% endunless %}"
       {% if container_width == 'narrow' %}style="--container-max: 90rem;"{% endif %}
       {% if container_width == 'wide' %}style="--container-max: 110rem;"{% endif %}
  >
    <div class="slideshow__viewport" data-viewport>
      <div class="slideshow__track" data-track>
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'image' -%}
              {%- liquid
                assign img_d = block.settings.image_desktop
                assign img_m = block.settings.image_mobile
                assign heading = block.settings.heading
                assign text = block.settings.text
              -%}
              <div class="slide" role="group" aria-roledescription="slide" aria-label="{{ forloop.index }} / {{ section.blocks.size }}" data-aspect-desktop="{{ img_d.aspect_ratio | default: img_m.aspect_ratio | default: 1.777 | json }}" data-aspect-mobile="{{ img_m.aspect_ratio | default: img_d.aspect_ratio | default: 1.777 | json }}" {{ block.shopify_attributes }}>
                <div class="slide__media">
                  <picture>
                    {% if img_m %}<source media="(max-width: 749px)" srcset="{{ img_m | image_url: width: 1400 }} 1400w" sizes="100vw">{% endif %}
                    {% if img_d %}
                      {{ img_d | image_url: width: 2400 | image_tag: widths: '1200, 1600, 2000, 2400', sizes: '100vw', loading: 'lazy', decoding: 'async', alt: '' }}
                    {% else %}
                      {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder' }}
                    {% endif %}
                  </picture>
                </div>
                {% if heading or text %}
                  <div class="slide__content">
                    {% if heading %}<h3 class="slide__heading">{{ heading }}</h3>{% endif %}
                    {% if text %}<div class="slide__text rte">{{ text }}</div>{% endif %}
                  </div>
                {% endif %}
              </div>
            {%- when 'product' -%}
              {%- liquid
                assign p = block.settings.product
                assign heading = block.settings.heading
                assign text = block.settings.text
                assign img_d = block.settings.image_desktop
                assign img_m = block.settings.image_mobile
                assign position = block.settings.position | default: 'left'
                assign overlay = block.settings.overlay | default: 'transparent'
              -%}
              <div class="slide slide--product" role="group" aria-roledescription="slide" aria-label="{{ forloop.index }} / {{ section.blocks.size }}" data-aspect-desktop="{{ img_d.aspect_ratio | default: img_m.aspect_ratio | default: 1.777 | json }}" data-aspect-mobile="{{ img_m.aspect_ratio | default: img_d.aspect_ratio | default: 1.777 | json }}" {{ block.shopify_attributes }}>
                <div class="slide__media">
                  <div class="slide__overlay" aria-hidden="true" style="background: {{ overlay }}"></div>
                  <picture>
                    {% if img_m %}<source media="(max-width: 749px)" srcset="{{ img_m | image_url: width: 1400 }} 1400w" sizes="100vw">{% endif %}
                    {% if img_d %}
                      {{ img_d | image_url: width: 2400 | image_tag: widths: '1200, 1600, 2000, 2400', sizes: '100vw', loading: 'lazy', decoding: 'async', alt: '' }}
                    {% endif %}
                  </picture>
                </div>
                <div class="slide__content slide__content--product slide__content--{{ position }}">
                  {% if heading %}<h3 class="slide__heading">{{ heading }}</h3>{% endif %}
                  {% if text %}<div class="slide__text rte">{{ text }}</div>{% endif %}
                  {% if p %}
                    <div class="slide__product">{%- render 'card-product', card_product: p, columns: 4, show_swatches: false -%}</div>
                  {% endif %}
                </div>
              </div>
          {%- endcase -%}
        {%- endfor -%}
      </div>

      {% if show_arrows and show_thumbnails == false %}
        <button class="slideshow__arrow slideshow__arrow--prev" type="button" data-prev>‹</button>
        <button class="slideshow__arrow slideshow__arrow--next" type="button" data-next>›</button>
      {% endif %}

      {% if show_pagination %}
        <div class="slideshow__dots" data-dots></div>
      {% endif %}

      {% if show_progress and enable_autoplay %}
        <div class="slideshow__progress"><span></span></div>
      {% endif %}
    </div>

    {% if show_thumbnails %}
      <div class="slideshow__thumbs" data-thumbs>
        <div class="slideshow__thumbs-track">
          {%- assign slide_i = 0 -%}
          {%- for block in section.blocks -%}
            <button class="slideshow__thumb" data-index="{{ slide_i }}">
              {%- if block.type == 'image' and block.settings.image_desktop -%}
                {{ block.settings.image_desktop | image_url: width: 200 | image_tag: widths: '120, 160, 200', sizes: '120px', alt: '' }}
              {%- elsif block.type == 'product' and block.settings.image_desktop -%}
                {{ block.settings.image_desktop | image_url: width: 200 | image_tag: widths: '120, 160, 200', sizes: '120px', alt: '' }}
              {%- else -%}
                <span class="slideshow__thumb-dot"></span>
              {%- endif -%}
            </button>
            {%- assign slide_i = slide_i | plus: 1 -%}
          {%- endfor -%}
        </div>
      </div>
    {% endif %}
  </div>
</section>

<script>
(() => {
  const root = document.getElementById('shopify-section-{{ section.id }}');
  if (!root) return;
  const viewport = root.querySelector('[data-viewport]');
  const track = root.querySelector('[data-track]');
  const slides = Array.from(track?.children || []);
  if (!track || !slides.length) return;
  const prevBtn = root.querySelector('[data-prev]');
  const nextBtn = root.querySelector('[data-next]');
  const dotsWrap = root.querySelector('[data-dots]');
  const thumbsWrap = root.querySelector('[data-thumbs]');
  const thumbs = Array.from(thumbsWrap?.querySelectorAll('.slideshow__thumb') || []);

  const loop = root.querySelector('.slideshow')?.getAttribute('data-loop') === 'true';
  const autoplay = root.querySelector('.slideshow')?.getAttribute('data-autoplay') === 'true';
  const autoplaySpeed = parseInt(root.querySelector('.slideshow')?.getAttribute('data-autoplay-speed') || '5000', 10);
  const heightMode = root.querySelector('.slideshow')?.getAttribute('data-height-mode') || 'auto';
  let index = 0; let timer = null; let raf = null;

  // Build dots
  if (dotsWrap) {
    dotsWrap.innerHTML = slides.map((_, i) => `<button class="slideshow__dot${i===0?' is-active':''}" data-index="${i}" aria-label="Slide ${i+1}"></button>`).join('');
    dotsWrap.addEventListener('click', (e) => {
      const btn = e.target.closest('button[data-index]'); if (!btn) return;
      goTo(parseInt(btn.getAttribute('data-index')));
    });
  }

  const setHeights = () => {
    if (heightMode === 'fixed') {
      const desktop = {{ desktop_height | json }};
      const mobile = {{ mobile_height | json }};
      const subtract = {{ subtract_header | json }};
      let header = 0; if (subtract) header = document.getElementById('SiteHeader')?.offsetHeight || 0;
      const w = window.innerWidth || document.documentElement.clientWidth;
      const h = (w >= 990 ? desktop : mobile) - header;
      viewport.style.setProperty('--slide-height', Math.max(h, 200) + 'px');
      viewport.style.removeProperty('--slide-aspect');
      return;
    }
    // Auto mode: match the active slide's aspect ratio
    const w = window.innerWidth || document.documentElement.clientWidth;
    const active = slides[index] || slides[0];
    if (active) {
      const ar = parseFloat(w >= 750 ? active.dataset.aspectDesktop : active.dataset.aspectMobile) || 1.777;
      viewport.style.removeProperty('--slide-height');
      viewport.style.setProperty('--slide-aspect', ar);
    }
  };
  setHeights(); window.addEventListener('resize', setHeights, { passive: true });

  function updateNav(){
    const dots = dotsWrap?.querySelectorAll('.slideshow__dot') || [];
    dots.forEach((d,i)=>d.classList.toggle('is-active', i===index));
    thumbs.forEach((t,i)=>t.classList.toggle('is-active', i===index));
  }
  function goTo(i){
    if (i===index) return;
    if (!loop) i = Math.max(0, Math.min(slides.length-1, i));
    else if (i<0) i = slides.length-1; else if (i>=slides.length) i = 0;
    slides[index]?.classList.remove('is-active');
    slides[i]?.classList.add('is-active');
    index = i; updateNav(); setHeights(); resetAutoplay();
  }
  function next(){ goTo(index+1); }
  function prev(){ goTo(index-1); }

  slides.forEach((s,i)=>s.classList.toggle('is-active', i===0));
  updateNav();
  prevBtn?.addEventListener('click', prev);
  nextBtn?.addEventListener('click', next);
  thumbsWrap?.addEventListener('click', (e)=>{ const b=e.target.closest('.slideshow__thumb'); if(!b) return; goTo(parseInt(b.getAttribute('data-index'))); });

  const progress = root.querySelector('.slideshow__progress span');
  function resetAutoplay(){
    if (!autoplay) return;
    if (timer) clearTimeout(timer);
    if (raf) cancelAnimationFrame(raf);
    if (progress) progress.style.transform = 'scaleX(0)';
    const start = performance.now(); const dur = autoplaySpeed;
    const step = (t)=>{ const p=Math.min(1,(t-start)/dur); if (progress) progress.style.transform = `scaleX(${p})`; if(p<1) raf=requestAnimationFrame(step); };
    raf = requestAnimationFrame(step); timer = setTimeout(next, autoplaySpeed);
  }
  if (autoplay) resetAutoplay();
})();
</script>

{% style %}
  /* Only override spacing when offsets are set.
     Leaving margins unset lets the global section spacing apply. */
  #shopify-section-{{ section.id }} {
    position: relative;
    z-index: 1;
    {% if mobile_offset_top != 0 %}margin-top: {{ mobile_offset_top }}px;{% endif %}
    {% if mobile_offset_bottom != 0 %}margin-bottom: {{ mobile_offset_bottom }}px;{% endif %}
  }
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} {
      {% if desktop_offset_top != 0 %}margin-top: {{ desktop_offset_top }}px;{% endif %}
      {% if desktop_offset_bottom != 0 %}margin-bottom: {{ desktop_offset_bottom }}px;{% endif %}
    }
  }
  .slideshow__inner.page-width { max-width: var(--container-max, var(--page-width)); margin: 0 auto; padding: 0; }
  /* Variables derived from settings */
  #shopify-section-{{ section.id }} .slideshow {
    --progress: {{ section.settings.color_progress | default: '#111111' }};
    --dot: {{ section.settings.color_pagination | default: '#111111' }};
    --dot-active: {{ section.settings.color_pagination_active | default: '#111111' }};
    --dot-op: {{ section.settings.opacity_pagination | default: 0.6 }};
    --arrow-bg: rgba({{ section.settings.color_arrows_bg | default: '#ffffff' | color_extract: 'red' }}, {{ section.settings.color_arrows_bg | default: '#ffffff' | color_extract: 'green' }}, {{ section.settings.color_arrows_bg | default: '#ffffff' | color_extract: 'blue' }}, {{ section.settings.opacity_arrows_bg | default: 0.9 }});
    --arrow-bg-h: rgba({{ section.settings.color_arrows_bg_hover | default: '#ffffff' | color_extract: 'red' }}, {{ section.settings.color_arrows_bg_hover | default: '#ffffff' | color_extract: 'green' }}, {{ section.settings.color_arrows_bg_hover | default: '#ffffff' | color_extract: 'blue' }}, {{ section.settings.opacity_arrows_bg_hover | default: 1 }});
    --arrow: {{ section.settings.color_arrows | default: '#111111' }};
    --arrow-h: {{ section.settings.color_arrows_hover | default: '#111111' }};
    --arrow-b: rgba({{ section.settings.color_arrows_border | default: '#111111' | color_extract: 'red' }}, {{ section.settings.color_arrows_border | default: '#111111' | color_extract: 'green' }}, {{ section.settings.color_arrows_border | default: '#111111' | color_extract: 'blue' }}, {{ section.settings.opacity_arrows_border | default: 0.2 }});
    --arrow-b-h: rgba({{ section.settings.color_arrows_border_hover | default: '#111111' | color_extract: 'red' }}, {{ section.settings.color_arrows_border_hover | default: '#111111' | color_extract: 'green' }}, {{ section.settings.color_arrows_border_hover | default: '#111111' | color_extract: 'blue' }}, {{ section.settings.opacity_arrows_border_hover | default: 0.4 }});
    --thumbs-bg: rgba({{ section.settings.color_thumbs_bg | default: '#000000' | color_extract: 'red' }}, {{ section.settings.color_thumbs_bg | default: '#000000' | color_extract: 'green' }}, {{ section.settings.color_thumbs_bg | default: '#000000' | color_extract: 'blue' }}, {{ section.settings.opacity_thumbs_bg | default: 0 }});
    --thumb-active-border: {{ section.settings.color_thumb_active_border | default: '#111111' }};
    {% case image_position %}
      {% when 'top' %}--slide-object-position: top center;
      {% when 'bottom' %}--slide-object-position: bottom center;
      {% else %}--slide-object-position: center;
    {% endcase %}
  }
  .slideshow__viewport { position: relative; height: var(--slide-height, auto); aspect-ratio: var(--slide-aspect); min-height: 200px; overflow: hidden; }
  .slideshow__track { position: relative; width: 100%; height: 100%; }
  .slide { position: absolute; inset: 0; opacity: 0; transition: opacity {{ animation_speed }}ms ease; display: grid; }
  .slide.is-active { opacity: 1; }
  .slide__media { position: absolute; inset: 0; }
  .slide__media picture, .slide__media img, .slide__media svg { width: 100%; height: 100%; object-fit: cover; object-position: var(--slide-object-position, center); }
  .slide__content { align-self: center; justify-self: center; padding: 1rem 1.25rem; border-radius: 10px; max-width: min(56rem, 90%); }
  .slide__heading { font-size: clamp(1.5rem, 4vw, 3rem); margin: 0 0 .5rem; }
  .slide__text { opacity: .9; margin: 0; }
  .slide__content--left { justify-self: start; }
  .slide__content--right { justify-self: end; }
  .slideshow__arrow { position: absolute; top: 50%; transform: translateY(-50%); z-index: 2; width: 40px; height: 40px; border-radius: 999px; border: 1px solid var(--arrow-b); display: grid; place-items: center; cursor: pointer; background: var(--arrow-bg); color: var(--arrow); }
  .slideshow__arrow--prev { left: .75rem; }
  .slideshow__arrow--next { right: .75rem; }
  .slideshow__arrow:hover { background: var(--arrow-bg-h); color: var(--arrow-h); border-color: var(--arrow-b-h); }
  .slideshow__dots { position: absolute; bottom: .75rem; left: 50%; transform: translateX(-50%); display: inline-flex; gap: .375rem; z-index: 2; }
  .slideshow__dot { width: 9px; height: 9px; border-radius: 50%; background: var(--dot); opacity: var(--dot-op); border: 0; padding: 0; display: inline-block; line-height: 0; aspect-ratio: 1 / 1; -webkit-appearance: none; appearance: none; }
  .slideshow__dot.is-active { background: var(--dot-active); opacity: 1; }
  .slideshow__progress { position: absolute; top: 0; left: 0; right: 0; height: 3px; background: rgba(0,0,0,.06); overflow: hidden; }
  .slideshow__progress span { display: block; width: 100%; height: 100%; background: var(--progress); transform-origin: left center; transform: scaleX(0); }
  .slideshow__thumbs { position: relative; padding: .25rem; margin-top: .5rem; display: flex; justify-content: center; }
  .slideshow__thumbs-track { display: grid; gap: .25rem; grid-auto-flow: column; overflow-x: auto; background: var(--thumbs-bg); border-radius: 8px; width: max-content; }
  .slideshow__thumb { background: transparent; border: 2px solid transparent; border-radius: 6px; padding: 0; cursor: pointer; line-height: 0; display: block; }
  .slideshow__thumb.is-active { border-color: var(--thumb-active-border); }
  .slideshow__thumb-dot { display: block; width: 14px; height: 14px; border-radius: 50%; background: var(--dot); opacity: var(--dot-op); }
  .slideshow__thumb img { width: 88px; height: 56px; object-fit: cover; border-radius: 4px; display: block; }
  @media (max-width: 749px) {
    .slideshow__thumb img { width: 72px; height: 46px; }
  }
{% endstyle %}

{% schema %}
{
  "name": "Slideshow",
  "settings": [
    { "type": "header", "content": "Slideshow settings" },
    { "type": "checkbox", "id": "show_arrows", "label": "Show arrows", "default": true },
    { "type": "checkbox", "id": "show_thumbnails", "label": "Show thumbnails (hides arrows)", "default": false },
    { "type": "checkbox", "id": "show_pagination", "label": "Show pagination", "default": true },
    { "type": "checkbox", "id": "show_progress", "label": "Show progress bar", "default": false },
    { "type": "checkbox", "id": "enable_loop", "label": "Enable loop", "default": true },
    { "type": "checkbox", "id": "enable_autoplay", "label": "Enable autoplay", "default": false },
    { "type": "range", "id": "autoplay_speed", "label": "Autoplay speed (ms)", "min": 1000, "max": 9000, "step": 500, "default": 5000 },
    { "type": "range", "id": "animation_speed", "label": "Animation speed (ms)", "min": 100, "max": 2000, "step": 50, "default": 500 },
    { "type": "select", "id": "animation_type", "label": "Animation type", "default": "slide", "options": [
      { "value": "fade", "label": "Fade" },
      { "value": "slide", "label": "Slide" },
      { "value": "flip", "label": "Flip" },
      { "value": "creative", "label": "Creative" },
      { "value": "parallax", "label": "Parallax" }
    ] },

    { "type": "header", "content": "Colors" },
    { "type": "color", "id": "color_progress", "label": "Progress bar", "default": "#111111" },
    { "type": "color", "id": "color_pagination", "label": "Pagination", "default": "#111111" },
    { "type": "range", "id": "opacity_pagination", "label": "Pagination opacity", "min": 0, "max": 1, "step": 0.1, "default": 0.6 },
    { "type": "color", "id": "color_pagination_active", "label": "Pagination (active)", "default": "#111111" },
    { "type": "color", "id": "color_arrows_bg", "label": "Arrows background", "default": "#ffffff" },
    { "type": "range", "id": "opacity_arrows_bg", "label": "Arrows background opacity", "min": 0, "max": 1, "step": 0.1, "default": 0.9 },
    { "type": "color", "id": "color_arrows", "label": "Arrows color", "default": "#111111" },
    { "type": "color", "id": "color_arrows_border", "label": "Arrows border color", "default": "#111111" },
    { "type": "range", "id": "opacity_arrows_border", "label": "Arrows border opacity", "min": 0, "max": 1, "step": 0.1, "default": 0.2 },
    { "type": "color", "id": "color_arrows_bg_hover", "label": "Arrows background (hover)", "default": "#ffffff" },
    { "type": "range", "id": "opacity_arrows_bg_hover", "label": "Arrows background opacity (hover)", "min": 0, "max": 1, "step": 0.1, "default": 1 },
    { "type": "color", "id": "color_arrows_hover", "label": "Arrows color (hover)", "default": "#111111" },
    { "type": "color", "id": "color_arrows_border_hover", "label": "Arrows border color (hover)", "default": "#111111" },
    { "type": "range", "id": "opacity_arrows_border_hover", "label": "Arrows border opacity (hover)", "min": 0, "max": 1, "step": 0.1, "default": 0.4 },
    { "type": "color", "id": "color_thumb_active_border", "label": "Thumbnail border color (active)", "default": "#111111" },
    { "type": "color", "id": "color_thumbs_bg", "label": "Thumbnails background", "default": "#000000" },
    { "type": "range", "id": "opacity_thumbs_bg", "label": "Thumbnails background opacity", "min": 0, "max": 1, "step": 0.1, "default": 0 },

    { "type": "header", "content": "Layout" },
    { "type": "select", "id": "container_width", "label": "Container width", "default": "inherit", "options": [
      { "value": "inherit", "label": "Inherit" },
      { "value": "narrow", "label": "Narrow" },
      { "value": "wide", "label": "Wide" },
      { "value": "full_bleed", "label": "Full bleed" }
    ] },
    { "type": "select", "id": "height_mode", "label": "Height mode", "default": "auto", "options": [
      { "value": "auto", "label": "Match image aspect" },
      { "value": "fixed", "label": "Fixed height (px)" }
    ] },
    { "type": "select", "id": "image_position", "label": "Image position", "default": "center", "options": [
      { "value": "top", "label": "Top" },
      { "value": "center", "label": "Center" },
      { "value": "bottom", "label": "Bottom" }
    ], "info": "Controls which part of the image remains visible to minimize subject cut-off (especially in full-bleed)." },
    { "type": "range", "id": "desktop_height", "label": "Desktop section height (px)", "min": 300, "max": 1000, "step": 10, "default": 600 },
    { "type": "range", "id": "mobile_height", "label": "Mobile section height (px)", "min": 240, "max": 900, "step": 10, "default": 420 },
    { "type": "checkbox", "id": "subtract_header", "label": "Subtract header height", "default": false },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image slide",
      "settings": [
        { "type": "image_picker", "id": "image_desktop", "label": "Image (desktop)" },
        { "type": "image_picker", "id": "image_mobile", "label": "Image (mobile)" },
        { "type": "text", "id": "heading", "label": "Heading" },
        { "type": "richtext", "id": "text", "label": "Text" }
      ]
    },
    {
      "type": "product",
      "name": "Product slide",
      "settings": [
        { "type": "product", "id": "product", "label": "Product" },
        { "type": "image_picker", "id": "image_desktop", "label": "Image (desktop)" },
        { "type": "image_picker", "id": "image_mobile", "label": "Image (mobile)" },
        { "type": "text", "id": "heading", "label": "Heading" },
        { "type": "richtext", "id": "text", "label": "Text" },
        { "type": "select", "id": "position", "label": "Content position", "default": "left", "options": [
          { "value": "left", "label": "Left" }, { "value": "right", "label": "Right" }
        ] },
        { "type": "color", "id": "overlay", "label": "Overlay" }
      ]
    }
  ],
  "presets": [ { "name": "Slideshow", "blocks": [ { "type": "image" }, { "type": "image" } ] } ]
}
{% endschema %}
