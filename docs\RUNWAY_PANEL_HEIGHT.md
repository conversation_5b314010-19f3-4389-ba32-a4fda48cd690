# Runway Layout: Panel Height & Scrolling Behavior

## Overview

The Runway layout features a sticky information panel that floats over the horizontal product gallery. This document explains how the panel height is managed and how it handles cases where the panel content is taller than the viewport or main content area.

## The Challenge

When implementing a sticky rail pattern like <PERSON><PERSON><PERSON>, <PERSON><PERSON>, or Givenchy, you face a common challenge:

**What happens when the panel is taller than the main content (gallery)?**

### <PERSON><PERSON><PERSON>' <PERSON><PERSON><PERSON>

<PERSON> solves this by making their panel height roughly equal to the combined height of their content sections:
- Product gallery
- "Shop the Look" section
- "Keep Exploring" section

When you scroll to the bottom, the panel and the last section are aligned, so they release together in sync.

### Our Solution

We've implemented a **flexible, viewport-constrained approach** that works regardless of content height:

1. **Max-height constraint**: Panel height is limited to `viewport height - header - gaps`
2. **Internal scrolling**: When content exceeds the viewport, the panel becomes scrollable
3. **Smart release logic**: Panel releases when its visible bottom reaches the section bottom
4. **Smooth UX**: Scroll position resets when returning to the top

## How It Works

### CSS Implementation

```css
.product__info {
  /* Constrain height to viewport minus header and gaps */
  max-height: calc(100vh - var(--header-h, 0px) - var(--pi-top-gap, 0px) - 24px) !important;
  
  /* Enable internal scrolling when content exceeds viewport */
  overflow-y: auto !important;
  overflow-x: visible !important;
  
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

/* Styled scrollbar (subtle, luxury aesthetic) */
.product__info::-webkit-scrollbar {
  width: 4px;
}
.product__info::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
}
```

### JavaScript Logic

The sticky rail script calculates three states:

1. **Start**: Panel is absolutely positioned at the section top
2. **Fixed**: Panel sticks to viewport top (below header) as you scroll
3. **Released**: Panel is absolutely positioned at section bottom

**Key calculation for release:**

```javascript
// Use effective panel height (constrained by viewport)
const viewportHeight = window.innerHeight;
const maxPanelHeight = viewportHeight - headerHeight - gaps;
const effectivePanelH = Math.min(actualPanelHeight, maxPanelHeight);

// Release when visible panel bottom reaches section bottom
const panelBottomIfFixed = scrollY + headerOffset + effectivePanelH;
const sectionBottom = sectionTop + sectionHeight;
const shouldRelease = panelBottomIfFixed >= sectionBottom;
```

This works even when `actualPanelHeight > sectionHeight` because we use the **visible** panel height (constrained by viewport).

## Customization Options

### Option 1: Current Flexible Approach (Recommended)

**Pros:**
- Works with any amount of content
- No need to coordinate heights between panel and sections
- Users can add/remove blocks without breaking the layout
- Handles different screen sizes gracefully

**Cons:**
- Panel may be scrollable (though this is common in luxury sites)
- Doesn't perfectly match Hermès' exact behavior

**When to use:** Default choice for maximum flexibility and user customization.

### Option 2: Hermès-Style Height Matching

If you want to exactly replicate Hermès' behavior where panel and content release together:

**Step 1:** Extend the sticky track to include sections below the product:

```liquid
<!-- In your theme, add data attributes to sections -->
<div class="shopify-section" data-runway-follow>
  <!-- Recommendations section -->
</div>

<div class="shopify-section" data-runway-follow>
  <!-- Recently viewed section -->
</div>

<div class="shopify-section" data-runway-stop>
  <!-- Footer or newsletter - stop here -->
</div>
```

**Step 2:** Ensure panel content height matches the combined sections:

```css
.product__info {
  /* Remove max-height constraint */
  max-height: none !important;
  overflow-y: visible !important;
}
```

**Step 3:** Adjust panel content to match:
- Use larger images
- Add more spacing
- Include additional content blocks
- Use the `data-runway-follow` system (see [RUNWAY_STICKY_TRACK.md](./RUNWAY_STICKY_TRACK.md))

**Pros:**
- Exact Hermès-style behavior
- Panel and content release in perfect sync
- No scrollbar in panel

**Cons:**
- Requires careful height coordination
- Less flexible for user customization
- May need adjustments for different screen sizes

### Option 3: Hybrid Approach

Combine both approaches:

```css
.product__info {
  /* Allow panel to grow, but cap at a reasonable max */
  max-height: min(
    calc(100vh - var(--header-h, 0px) - 48px),
    var(--runway-track-h, 100vh)
  ) !important;
  overflow-y: auto !important;
}
```

This allows the panel to grow with the track height (when using `data-runway-follow` sections) but still constrains it to the viewport if content is excessive.

## Scrollbar Styling

The panel uses a subtle scrollbar that matches luxury brand aesthetics:

```css
/* Webkit browsers (Chrome, Safari, Edge) */
.product__info::-webkit-scrollbar {
  width: 4px;
}
.product__info::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
}
.product__info::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25);
}

/* Firefox */
.product__info {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.15) transparent;
}
```

### Hiding the Scrollbar

If you prefer no visible scrollbar (like some luxury sites):

```css
.product__info {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}
.product__info::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
```

**Note:** Hiding scrollbars can hurt UX as users won't know the content is scrollable. Consider adding a subtle fade indicator instead.

## Troubleshooting

### Panel content is cut off at the bottom

**Cause:** Panel height exceeds viewport, but scrolling isn't working.

**Fix:**
1. Check that `overflow-y: auto` is applied to `.product__info`
2. Verify no ancestor has `overflow: hidden`
3. Inspect computed `max-height` in DevTools

### Panel releases too early

**Cause:** The effective panel height calculation is incorrect.

**Fix:**
1. Check that `--header-h` CSS variable is set correctly
2. Verify viewport height detection works on your device
3. Inspect the release calculation in browser console:

```javascript
const panel = document.querySelector('.product__info');
const section = document.querySelector('.product');
console.log('Panel height:', panel.offsetHeight);
console.log('Section height:', section.offsetHeight);
console.log('Viewport height:', window.innerHeight);
```

### Panel never releases

**Cause:** Section height is too short compared to panel.

**Fix:**
1. Use the `data-runway-follow` system to extend the track (see [RUNWAY_STICKY_TRACK.md](./RUNWAY_STICKY_TRACK.md))
2. Add more content sections below the product
3. Increase minimum section height:

```css
.product {
  min-height: 150vh; /* Ensure section is taller than viewport */
}
```

### Scrollbar looks wrong

**Cause:** Browser-specific styling issues.

**Fix:**
1. Test in multiple browsers (Chrome, Firefox, Safari)
2. Adjust scrollbar colors to match your theme
3. Consider using a custom scrollbar library for consistency

## Best Practices

1. **Test with real content**: Use actual product descriptions, images, and blocks
2. **Test different screen sizes**: Especially tall/short viewports
3. **Consider mobile**: This only affects desktop (≥990px)
4. **Monitor performance**: ResizeObserver and scroll listeners are optimized but test on slower devices
5. **Provide visual feedback**: Subtle scrollbar or fade indicator helps users know content is scrollable

## Related Documentation

- [Runway Layout Overview](./RUNWAY_LAYOUT.md)
- [Runway Sticky Track Configuration](./RUNWAY_STICKY_TRACK.md)
- [Product Section Configuration](./PRODUCT_SECTION.md)
- [Theme Customization Guide](../USER_GUIDE.md)

## Technical Details

### Browser Support

- **Modern browsers**: Full support with `dvh` units for better mobile behavior
- **Fallback**: Uses `vh` units for older browsers
- **Mobile**: Standard layout (no sticky behavior) below 990px

### Performance

- **RequestAnimationFrame**: Smooth, throttled updates
- **ResizeObserver**: Automatic recalculation when content changes
- **Passive listeners**: Optimized scroll performance
- **Cached measurements**: Reduces layout thrashing

### Accessibility

- **Keyboard navigation**: Panel is fully keyboard accessible
- **Screen readers**: Proper ARIA labels and semantic HTML
- **Reduced motion**: Respects `prefers-reduced-motion` setting
- **Focus management**: Focus stays within panel when scrolling

