/* Product Form AJAX Add
 * Intercepts product form submits, uses /cart/add.js,
 * then triggers cart drawer refresh + open without page reload.
 */
(function(){
  if (window.__PRODUCT_FORM_AJAX_BOUND__) return; // idempotent
  window.__PRODUCT_FORM_AJAX_BOUND__ = true;

  const LOG_PREFIX = '[product-form-ajax]';

  function isProductAddForm(form) {
    if (!form || form.tagName !== 'FORM') return false;
    // Shopify product forms post to /cart/add (could be relative)
    const action = (form.getAttribute('action') || '').toLowerCase();
    if (!action) return false;
    // Avoid intercepting non-add forms (e.g., cart note or checkout)
    return action.includes('/cart/add');
  }

  function setLoading(btn, on) {
    if (!btn) return;
    btn.toggleAttribute('aria-busy', !!on);
    btn.disabled = !!on;
    const overlay = btn.querySelector('.loading-overlay');
    const text = btn.querySelector('.product-form__submit-text, .add-to-cart-btn');
    try {
      if (overlay) overlay.classList.toggle('hidden', !on);
      if (text) text.style.opacity = on ? '0.5' : '';
    } catch(_) {}
  }

  async function ajaxAdd(id, quantity, properties) {
    const payload = {
      items: [{ id: Number(id), quantity: Number(quantity || 1), properties: properties || undefined }],
      sections: 'cart-drawer'
    };
    const res = await fetch('/cart/add.js', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
      body: JSON.stringify(payload),
      credentials: 'same-origin'
    });
    const json = await res.json().catch(() => ({}));
    if (!res.ok) {
      const msg = json && (json.description || json.message || json.status) || 'Add to cart failed';
      throw new Error(msg);
    }
    return json;
  }

  function collectLineProperties(form) {
    // Capture any properties[xyz] inputs
    const props = {};
    const nodes = form.querySelectorAll('[name^="properties["]');
    nodes.forEach((el) => {
      const name = el.getAttribute('name');
      const m = name && name.match(/^properties\[(.+)\]$/);
      if (!m) return;
      const key = m[1];
      const val = el.value;
      if (val != null && val !== '') props[key] = val;
    });
    return Object.keys(props).length ? props : undefined;
  }

  // Global delegated submit handler for product add forms
  document.addEventListener('submit', async (e) => {
    const form = e.target;
    if (!isProductAddForm(form)) return;
    // Allow opt-out with data-no-ajax
    if (form.hasAttribute('data-no-ajax')) return;

    const idInput = form.querySelector('[name="id"]');
    if (!idInput || !idInput.value) {
      // Let the normal submit happen if we cannot determine variant
      console.warn(LOG_PREFIX, 'no variant id found; falling back to default submit');
      return;
    }

    e.preventDefault();

    const submitBtn = form.querySelector('button[type="submit"][name="add"], button.add-to-cart-btn, [type="submit"]');
    setLoading(submitBtn, true);
    const qty = (form.querySelector('[name="quantity"]') || {}).value || 1;
    const props = collectLineProperties(form);

    try {
      const json = await ajaxAdd(idInput.value, qty, props);
      // Announce for a11y and trigger drawer logic
      if (window.cartDrawerAnnounce) {
        try { window.cartDrawerAnnounce('Added to cart'); } catch(_) {}
      }
      document.dispatchEvent(new CustomEvent('cart:updated', { detail: json }));
    } catch (err) {
      console.error(LOG_PREFIX, err);
      const msg = (err && err.message) || 'Could not add to cart';
      // Surfacing a simple alert for now; theme can style inline errors later
      try { alert(msg); } catch(_) {}
    } finally {
      setLoading(submitBtn, false);
    }
  }, true);
})();

