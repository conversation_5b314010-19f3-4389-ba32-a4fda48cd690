# Shopify Theme Development - Solution & Progress Documentation

## Gucci-Style Header Fade: Findings & Fix

Problem
- Header background faded in too late on pages with a top hero; it only reached visible opacity near the end of the first hero.

Root Cause
- The fade range used a stale default (200) because `computeHeroRange()` ran before the hero was fully measurable. The per-frame alpha used this stale value, so earlier tuning had no visible effect. We also had a stray brace that caused a runtime script error during iteration.

Changes Made
- Dynamic range per frame: compute the effective range each animation frame so it reflects the real-time hero height.
  - sections/header.liquid:1864
- Broadened hero range calculation so the fade starts earlier on tall heroes.
  - sections/header.liquid:1833
- Global non-hero fade made faster by default.
  - sections/header.liquid:62
- Added lightweight debug (data attributes, console, on-screen pill) and removed a stray closing brace causing a SyntaxError.
  - sections/header.liquid:1897, sections/header.liquid:1933, sections/header.liquid:1939

First-Paint White On Hero (FOUC) Fix
- Guard class before CSS parse: stamp `gucci-enabled` on `<html>`/`<body>` as early as possible so the "black-on-pages" fallback never overrides Gucci pages on first paint.
  - sections/header.liquid:8 (after Liquid prelude) adds a tiny inline script that sets `gucci-enabled`.
- Hero-only white via CSS :has: keep header content white only when a hero sits directly below the header.
  - sections/header.liquid:12–40 tighten selectors and add `!important` to out-prioritize any late overrides.

Why this is robust
- The pre-paint script runs synchronously during HTML parsing, before the header CSS rules are evaluated, eliminating the brief black state caused by `body.header--black-on-pages:not(.gucci-enabled)` overrides.
- The :has(#MainContent > .shopify-section:first-child .hero-*) gate ensures the initial white appears only when a top hero actually exists; non-hero pages keep normal colors.
- `!important` was added to the critical pre-paint color rules to win against competing rules that also use `!important`.

Edge Cases & Fallbacks
- Browsers without `:has()` support: modern Safari/Chromium support it; if needed, the JS `applyContext()` still flips classes quickly after parse. You can optionally add a server-side template hint/class if your layout guarantees a top hero on specific templates.

Exact Code Notes
- Set global range for non-hero pages: `data-gucci-range="150"`
  - sections/header.liquid:62
- Hero range function (earlier start on tall heroes):
  - sections/header.liquid:1833
    - `return Math.min(640, Math.max(120, h * 0.85));`
- Use live range each frame (prevents stale 200px behavior):
  - sections/header.liquid:1864
    - `const range = hasTopHero ? computeHeroRange() : genericRange;`
- Per-frame debug exposure and pill text (visible with `?debug_header`):
  - sections/header.liquid:1897, sections/header.liquid:1933
- Fixed stray brace that caused “Unexpected token ';'”:
  - sections/header.liquid:1939

How To Debug
- Enable debug: append `?debug_header` to the URL or toggle “Enable Gucci header debug” in Header settings.
- Watch the pill values and header data attributes:
  - `alpha` (current background alpha)
  - `heroR` (computed hero fade range)
  - `btm` (hero bottom position, px from viewport top)
  - `aH` (announcement bar height)
- Alpha starts increasing once `btm < aH + heroR`.

Tuning Knobs
- Start earlier over hero: increase multiplier or cap in `computeHeroRange()`.
  - sections/header.liquid:1833
- Start earlier on non-hero pages: reduce `data-gucci-range` value.
  - sections/header.liquid:62
- Text color flip timing: adjust `FLIP_THRESHOLD` (default 0.22) if needed.
  - sections/header.liquid:1852

Verification Steps
- Hard refresh with `?debug_header`.
- At page top, confirm `data-has-top-hero="true"` and `heroR` > 200 for tall heroes.
- Scroll slightly; alpha should increase earlier than before and text flips near the threshold.

Rollback
- Revert the multiplier/cap or `data-gucci-range` values if the fade is too aggressive.

## Recent Development: Gucci-Style Header Fade System with Glass Effects ✅

### Feature Overview

Successfully implemented a sophisticated header overlay system with three glass effect modes and smart contrast switching. The header starts with white text and dynamically transitions to black text when background opacity reaches readable thresholds, providing a premium shopping experience similar to luxury fashion brands.

### Technical Implementation

#### Files Modified

- `sections/header.liquid` - Complete header with fade system, glass effects, and theme switching
- Added CSS custom properties: `--header-bg-alpha`, `--header-blur` for dynamic styling
- Implemented RequestAnimationFrame-based scroll tracking with hero detection
- Schema settings for overlay behavior and glass effect selection

#### Key Features Implemented

1. **Three Glass Effect Modes**:
   - **None**: White text, no background blur/opacity
   - **Subtle**: White text, minimal background (10% opacity, light blur)
   - **Full**: White text → Black text transition at 30% background opacity

2. **Smart Hero Detection**:
   - Automatically detects hero sections with `data-hero="true"`
   - Robust fallback logic for various hero section types
   - Dynamic overlay behavior based on page content

3. **Theme Switching Logic**:
   - Always starts with white text (`theme--light-on-hero`)
   - Switches to black text (`theme--dark-over-content`) only for "full" mode with visible background
   - Prevents jarring initial state issues

4. **Scroll-Based Fade System**:
   - RequestAnimationFrame loop for smooth 60fps updates
   - Alpha calculation based on scroll position and hero height
   - Backdrop-filter support for glass morphism effects

5. **Browser Compatibility**:
   - Graceful fallback for browsers without backdrop-filter support
   - CSS custom properties with fallback values
   - Progressive enhancement approach

#### CSS Architecture

- **Glass Effect Implementation**: Uses backdrop-filter and background opacity
- **Theme Classes**: `.theme--light-on-hero` and `.theme--dark-over-content`
- **Custom Properties**: Dynamic CSS variables updated via JavaScript
- **Responsive Design**: Adapts to different screen sizes and orientations

#### JavaScript Architecture

- **Performance Optimized**: Uses requestAnimationFrame for smooth animations
- **Event-Driven**: Scroll and resize event handling with throttling
- **State Management**: Tracks hero detection and current glass mode
- **Error Handling**: Defensive coding for missing elements

### Debugging & Quality Assurance

- Console logging for hero detection and theme switching
- Visual debugging indicators (can be enabled for development)
- Cross-browser testing for backdrop-filter support
- Mobile responsiveness validation

### Performance Considerations

- Efficient scroll event handling with requestAnimationFrame
- Minimal DOM manipulation with cached element references
- CSS transforms for GPU acceleration
- Optimized hero detection algorithm

---

## Previous Development: Color Swatch Hover Overlay System ✅

### Feature Overview

Successfully implemented a sophisticated product card hover overlay system that displays color swatches, size options, and quick add functionality. This matches modern e-commerce UX patterns where hovering over product cards reveals variant selection options.

### Technical Implementation

#### Files Modified/Created

- `assets/variant-hover-clean.js` - Main overlay functionality and cart integration
- `assets/collection-grid-utilities.css` - Overlay styling and visual states
- `snippets/card-product.liquid` - Product card template with swatch rendering
- `sections/collection-product-grid.liquid` - Collection page integration with theme settings

#### Key Features Implemented

1. **Theme Setting Toggle**: Merchants can switch between "hover_overlay" and "below_card" display modes
2. **Dynamic Overlay Creation**: JavaScript creates overlays with actual product variant data
3. **Color Swatch Functionality**:
   - Displays actual product colors from variants
   - Supports special patterns (striped, etc.)
   - Click to select color and update product image
   - Visual active states with border highlights
4. **Size Selection**:
   - Dynamic size options from product variants
   - Fallback to common sizes (XS-XXL) when no variants available
   - Click to select with visual feedback
5. **Smart Variant Matching**:
   - Fetches product data via `/products/{handle}.js` API
   - Finds correct variant ID based on color + size combination
   - Prevents "wrong variant" cart errors
6. **Cart Integration**:
   - Uses Shopify's `/cart/add.js` API for seamless cart updates
   - Comprehensive error handling with user feedback
   - Success/error notifications
   - Cart drawer integration support

#### CSS Architecture

- **Overlay Positioning**: Bottom-aligned overlay covering only lower portion of product image
- **Clean White Design**: Matches modern e-commerce aesthetic
- **Responsive Behavior**: Adapts to different screen sizes
- **Active States**: Visual feedback for selected colors/sizes
- **Hover Effects**: Smooth transitions and scaling effects

#### JavaScript Architecture

- **Event-Driven**: Uses modern addEventListener patterns
- **Error Handling**: Comprehensive console logging and user feedback
- **API Integration**: Proper async/await patterns for Shopify APIs
- **Memory Management**: Prevents duplicate overlay creation
- **Accessibility**: Proper ARIA labels and keyboard support

### Debugging & Quality Assurance

- Extensive console logging for troubleshooting variant selection
- Multiple fallback strategies for variant detection
- Error boundary handling for network issues
- Visual debug indicators (removed in production)

### Performance Considerations

- Lazy loading of product variant data
- Efficient DOM manipulation with event delegation
- CSS transitions for smooth animations
- Minimal JavaScript footprint

---

## Shopify CLI Development Issues & Solutions

### Problem
When running `shopify theme dev`, local code changes are being reverted automatically. This happens because of conflicts between local files and remote theme files in the Shopify theme editor.

### Root Cause
The issue is related to the `--theme-editor-sync` flag behavior. When this flag is used (or was previously used), Shopify CLI creates a synchronization mechanism between the local theme files and the remote theme editor. This can cause conflicts when:
1. Changes are made in the theme editor while the development server is running
2. The theme was previously synced with `--theme-editor-sync` flag
3. There are multiple development processes running simultaneously

## Solution

### 1. Stop Any Existing Theme Development Processes
First, ensure no other Shopify theme development processes are running:
```bash
# Check for running processes
tasklist | findstr node

# If you find any Shopify-related processes, stop them
# You can use Task Manager or kill specific processes by PID
taskkill /PID [process_id] /F
```

### 2. Run Shopify Theme Development Without Theme Editor Sync
Use the following command to start theme development without the problematic sync feature:
```bash
shopify theme dev --store curlybrak.myshopify.com
```

### 3. Alternative Approach with Additional Flags
If you want more control over the development process, use these flags:
```bash
shopify theme dev --store curlybrak.myshopify.com --nodelete --live-reload hot-reload
```

### 4. If You Must Use Theme Editor Sync
If you specifically need to use theme editor sync, be aware that:
- You'll be prompted to choose between local and remote versions
- Choose "local" to preserve your local changes
- Run the command with explicit sync flag:
```bash
shopify theme dev --store curlybrak.myshopify.com --theme-editor-sync
```

## Best Practices

1. **Avoid concurrent processes**: Don't run multiple `shopify theme dev` commands simultaneously
2. **Use version control**: Keep your theme in Git to track changes and recover from conflicts
3. **Check for running processes**: Before starting development, verify no other theme dev processes are running
4. **Use --nodelete flag**: This prevents remote deletions from affecting your local files

## Additional Troubleshooting

If issues persist:
1. Clear any cached theme data in the `.shopify` directory
2. Restart your terminal/command prompt
3. Ensure you're in the correct theme directory
4. Check that your Shopify CLI is up to date:
```bash
shopify version
```

## Git Status Note
Your current repository has several modified files that should be committed:
- assets/critical.css
- sections/collection.liquid
- sections/header-group.json
- sections/header.liquid
- sections/product.liquid
- snippets/card-product-swatches.liquid
- templates/collection.json
- templates/index.json
- templates/product.json

Consider committing these changes:

```bash
git add .
git commit -m "Update theme files"
```

## Current Project Status & Next Steps

### Completed Features ✅

1. **Gucci-Style Header Fade System** 🆕
   - Three glass effect modes (none, subtle, full)
   - Smart contrast switching from white to black text
   - Scroll-based fade with RequestAnimationFrame optimization
   - Hero section detection with robust fallback logic
   - Theme switching logic for optimal readability
   - CSS custom properties for dynamic styling
   - Browser compatibility with graceful degradation

2. **Product Card Hover Overlay System**
   - Color swatch selection with image updates
   - Size option selection with visual feedback  
   - Smart variant ID matching using Shopify APIs
   - Cart integration with error handling
   - Theme setting toggle between hover/below display modes

3. **Responsive Design**
   - Mobile-optimized overlay sizing
   - Touch-friendly button dimensions
   - Adaptive layout for different screen sizes
   - Header fade system works across all breakpoints

4. **Error Handling & UX**
   - Console debugging for development
   - User-friendly error messages
   - Success notifications for cart actions
   - Fallback strategies for missing data

### Recent Fixes & Solutions 🔧

1. **Header Theme Switching - RESOLVED** ✅
   - **Problem**: "Full" glass mode was starting with black text instead of white→black transition
   - **Root Cause**: Theme initialization logic was inconsistent for different page types
   - **Solution**: Modified initialization to always start with white text (`theme--light-on-hero`) and only switch to black when background becomes visible (≥30% opacity)
   - **Result**: All glass modes now provide consistent white text start state with proper transitions

2. **CSS Selector Specificity Issue - RESOLVED** ✅
   - **Problem**: Overlay swatches were being hidden by overly broad CSS selector
   - **Root Cause**: `.collection[data-swatch-mode="hover_overlay"][data-overlays-ready] .card__swatches { display: none !important; }` was hiding ALL swatches, including overlay swatches
   - **Solution**: Made selector more specific to only target original below-card swatches:
     ```css
     /* Hide only original swatches below cards */
     .collection[data-swatch-mode="hover_overlay"][data-overlays-ready] .card__content .card__swatches {
       display: none !important;
     }
     
     /* Ensure overlay swatches remain visible */
     .collection .card-hover-overlay .card__swatches {
       display: flex !important;
     }
     ```
   - **Result**: Hover overlay now properly shows color swatches, sizes, and quick add button

### Known Issues & Improvements 🔧

1. **Variant Selection Logic**
   - Currently being refined to handle complex product option structures
   - Need to test with products having 3+ option types
   - May need optimization for products with many variants

2. **Performance Optimization**
   - Consider caching product variant data
   - Implement loading states for API calls
   - Optimize overlay creation for large product grids

### Development Workflow

**Current Deployment Method**: Manual push to Shopify store

```bash
shopify theme push --store=curlybrak.myshopify.com
```

**Recommended for Active Development**: Use theme development server

```bash
shopify theme dev --store=curlybrak.myshopify.com --nodelete
```

### Future Enhancement Ideas 💡

1. **Advanced Features**
   - Quick view modal integration
   - Wishlist functionality in overlay
   - Recently viewed products
   - Product comparison tools

2. **Analytics Integration**
   - Track swatch interaction events
   - Monitor conversion rates by color/size
   - A/B testing framework for different overlay designs

3. **Accessibility Improvements**
   - Keyboard navigation for overlay elements
   - Screen reader optimizations
   - High contrast mode support

### File Structure Overview

```text
skeleton/
├── assets/
│   ├── variant-hover-clean.js     # Main overlay functionality
│   └── collection-grid-utilities.css  # Overlay styling
├── sections/
│   ├── header.liquid              # Header with fade system & glass effects
│   └── collection-product-grid.liquid  # Collection page with settings
├── snippets/
│   └── card-product.liquid        # Enhanced product cards
└── templates/
    └── collection.json            # Template configuration
```

### Header Fade System Schema Settings

The header section now includes these customizable options:

```json
{
  "type": "checkbox",
  "id": "overlay_on_hero",
  "label": "Overlay header on hero sections",
  "default": true
},
{
  "type": "select",
  "id": "glass_effect",
  "label": "Glass effect",
  "options": [
    { "value": "none", "label": "None" },
    { "value": "subtle", "label": "Subtle" },
    { "value": "full", "label": "Full" }
  ],
  "default": "subtle"
}
```

---

## Summary

This documentation now reflects the complete implementation of both the **color swatch hover overlay system** and the **Gucci-style header fade system with glass effects**. Both features are production-ready with comprehensive error handling, responsive design, and theme editor integration.

---

## Recent Development: Image Zoom-On-Scroll Effect

Feature Overview
- Adds a premium zoom-in animation to hero and promo imagery as it enters the viewport (IntersectionObserver-based). The effect is attribute/setting-gated and keeps overlays/text unscaled.

Files Added/Modified
- assets/image-zoom.css — Transition styles with CSS variables (scale, duration, delay, easing). Defaults: from 1 → 1.2 over 1600ms with a luxe ease.
- assets/image-zoom.js — Observes elements with `data-zoom-on-scroll`, adds `is-zoomed` on first intersection, then unobserves. Applies optional data-driven overrides.
- layout/theme.liquid — Conditionally includes the CSS/JS when the global theme setting is enabled.
- config/settings_schema.json — New Theme setting group “Effects” with `enable_global_image_zoom` (default: true).
- Sections updated to support a toggle (`enable_image_zoom`) and wrapper markup with zoom attributes (applied only when enabled):
  - hero-banner.liquid, hero-split.liquid, callout-banner.liquid, editorial-split-v2.liquid
  - product-grid.liquid (promo blocks only), collection-product-grid.liquid (promo blocks), collection.liquid (promo blocks)
  - collection-grid-list.liquid (collection cards), instagram-simple.liquid, social-highlights.liquid
  - newsletter-popup.liquid (image layout), custom-section.liquid (background image)
  - article.liquid (hero image), blog.liquid (list card images)
  - hello-world.liquid: demo checkbox + scale control

How It Works
- Markup: wrap the media layer in a container with `class="zoom-on-scroll"` and `data-zoom-on-scroll`.
- JS: IntersectionObserver sets the class `is-zoomed` once; CSS transitions `transform: scale()` using variables.
- Accessibility: Honors `prefers-reduced-motion: reduce` by disabling transitions.

Settings & Gating
- Global: Theme settings → Effects → “Enable image zoom on scroll”. If off, assets are not loaded and no sections animate.
- Per-section: Each updated section exposes “Enable image zoom-on-scroll”. When off, the wrapper attributes are not rendered.

Per-Element Tuning (optional)
- Add data attributes to the zoom wrapper to override defaults without new classes:
  - `data-zoom-to="1.3"`, `data-zoom-from="1"`
  - `data-zoom-duration="1800ms"`
  - `data-zoom-delay="200ms"` (global default remains 0ms)
  - `data-zoom-ease="cubic-bezier(0.16,1,0.3,1)"`

Usage Example
```
<div class="zoom-on-scroll" data-zoom-on-scroll data-zoom-to="1.25" data-zoom-delay="120ms">
  {{ section.settings.image | image_url: width: 2000 | image_tag: class: 'hero__image', alt: final_alt }}
  <!-- overlays/text outside this wrapper remain unscaled -->
  <div class="hero__overlay">…</div>
</div>
```

Performance Notes
- Observer unobserves after first trigger to minimize work.
- Only wrapper transforms to keep text crisp and avoid layout thrash.
- Not applied to product cards/gallery by default to prevent UX noise; focused on hero/promotional media.

Testing & Verification
- Ensure the global toggle is ON. Preview templates with updated sections and scroll to confirm a smooth, single-fire zoom.
- Toggle section-level settings to validate gating.
- Verify reduced-motion: set OS to reduce motion — zoom should not animate.

Rollback
- Disable globally via Theme settings → Effects → “Enable image zoom on scroll”, or turn off per section. Removing the asset includes in `layout/theme.liquid` fully removes the feature.
