{% comment %}
  Editorial Split v2 (no dependencies)
  - Media (image/video) on one side
  - Content + horizontal product slider on the other
  - Addable on collection pages; reorder anywhere in the Editor
{% endcomment %}
<section class="editorial-split-v2 color-{{ section.settings.color_scheme }} {% if section.settings.flip_layout %}editorial-split-v2--flip{% endif %}" data-section-id="{{ section.id }}">
  <div class="page-width editorial-split-v2__inner">
    <div class="editorial-split-v2__media{% if section.settings.enable_image_zoom %} zoom-on-scroll{% endif %}"{% if section.settings.enable_image_zoom %} data-zoom-on-scroll{% endif %}{% if section.settings.enable_image_reveal %} data-reveal-on-scroll{% endif %}>
      {% if section.settings.media_type == 'image' and section.settings.image %}
        {{ section.settings.image | image_url: width: 2200 | image_tag: loading: 'lazy', decoding: 'async', widths: '800,1200,1600,2200', sizes: '(min-width: 1200px) 50vw, 100vw' }}
      {% elsif section.settings.media_type == 'video' and section.settings.video %}
        {{ section.settings.video | video_tag: controls: true, muted: true, playsinline: true, preload: 'metadata' }}
      {% endif %}
    </div>

    <div class="editorial-split-v2__content">
      {% if section.settings.kicker != blank %}<p class="kicker">{{ section.settings.kicker | escape }}</p>{% endif %}
      {% if section.settings.heading != blank %}<h2 class="h2">{{ section.settings.heading | escape }}</h2>{% endif %}
      {% if section.settings.subheading != blank %}<div class="rte">{{ section.settings.subheading }}</div>{% endif %}

      {% if section.settings.button_label != blank and section.settings.button_link != blank %}
        <a class="button" href="{{ section.settings.button_link }}">{{ section.settings.button_label | escape }}</a>
      {% endif %}

      {% if section.blocks.size > 0 %}
        <div class="split-slider js-snap-slider" role="region" aria-roledescription="carousel" aria-label="{{ section.settings.heading | default: 'Editorial slider' | escape }}">
          <div class="split-slider__track js-snap-track" tabindex="0" aria-live="polite">
            {% for block in section.blocks %}
              <div class="split-slider__slide" {{ block.shopify_attributes }}>
                {% case block.type %}
                  {% when 'product' %}
                    {% assign p = all_products[block.settings.product] %}
                    {% if p %}
                      {% render 'card-product', 
                        product: p, 
                        grid_position: forloop.index0, 
                        columns: 3,
                        show_swatches: section.settings.show_swatches,
                        enable_hover_preview: section.settings.enable_hover_preview,
                        max_swatches: section.settings.max_swatches,
                        card_click_behavior: section.settings.card_click_behavior,
                        show_quick_add: section.settings.show_quick_add,
                        card_image_aspect_ratio: section.settings.card_image_aspect_ratio
                      %}
                    {% endif %}
                  {% when 'collection' %}
                    {% assign coll = collections[block.settings.collection] %}
                    {% if coll %}
                      {% for p in coll.products limit: block.settings.limit %}
                        {% render 'card-product', 
                          product: p, 
                          grid_position: forloop.index0, 
                          columns: 3,
                          show_swatches: section.settings.show_swatches,
                          enable_hover_preview: section.settings.enable_hover_preview,
                          max_swatches: section.settings.max_swatches,
                          card_click_behavior: section.settings.card_click_behavior,
                          show_quick_add: section.settings.show_quick_add,
                          card_image_aspect_ratio: section.settings.card_image_aspect_ratio
                        %}
                      {% endfor %}
                    {% endif %}
                {% endcase %}
              </div>
            {% endfor %}
          </div>
          <div class="split-slider__controls">
            <button class="split-slider__btn js-snap-prev" type="button" aria-label="Previous items">‹</button>
            <button class="split-slider__btn js-snap-next" type="button" aria-label="Next items">›</button>
          </div>
        </div>
      {% endif %}
    </div>
  </div>

<script src="{{ 'section-slider.js' | asset_url }}" defer></script>
  {% style %}
    .editorial-split-v2 { padding: 3rem 0; }
    .editorial-split-v2__inner { display:grid; gap:2rem; align-items:center; }
    @media (min-width: 990px){
      .editorial-split-v2__inner { grid-template-columns: 1fr 1fr; }
      .editorial-split-v2--flip .editorial-split-v2__media { order:2; }
      .editorial-split-v2--flip .editorial-split-v2__content { order:1; }
    }
    .split-slider { margin-top: 1.5rem; position: relative; }
    .split-slider__track { display:flex; gap:1rem; overflow:auto; scroll-snap-type:x mandatory; -webkit-overflow-scrolling:touch; padding-bottom: .25rem; }
    .split-slider__slide { scroll-snap-align:start; flex: 0 0 auto; min-width: clamp(220px, 40vw, 320px); }
    .split-slider__controls { display:flex; gap:.5rem; margin-top: .5rem; }
    .split-slider__btn { inline-size:2.25rem; block-size:2.25rem; border:1px solid rgba(0,0,0,.2); border-radius:999px; background:transparent; cursor:pointer; }
    .kicker { letter-spacing:.06em; text-transform:uppercase; font-size:.8rem; opacity:.8; }
  {% endstyle %}
</section>

{% schema %}
{
  "name": "Editorial split (v2)",
  "enabled_on": { "templates": ["collection"] },
  "settings": [
    { "type": "checkbox", "id": "enable_image_reveal", "label": "Enable image reveal-on-scroll", "default": true },
    { "type": "checkbox", "id": "enable_image_zoom", "label": "Enable image zoom-on-scroll", "default": true },
    { "type": "text", "id": "kicker", "label": "Kicker" },
    { "type": "text", "id": "heading", "label": "Heading" },
    { "type": "richtext", "id": "subheading", "label": "Body" },
    { "type": "text", "id": "button_label", "label": "Button label" },
    { "type": "url", "id": "button_link", "label": "Button link" },
    { "type": "select", "id": "media_type", "label": "Media type", "options":[{"value":"image","label":"Image"},{"value":"video","label":"Video"}], "default":"image" },
    { "type": "image_picker", "id": "image", "label": "Image" },
    { "type": "video", "id": "video", "label": "Video" },
    { "type": "checkbox", "id": "flip_layout", "label": "Image on right", "default": false },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "scheme-1" },
    {
      "type": "header",
      "content": "Product card settings"
    },
    {
      "type": "checkbox",
      "id": "show_swatches",
      "label": "Show color swatches on product cards",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_hover_preview",
      "label": "Enable card hover preview",
      "default": true
    },
    {
      "type": "range",
      "id": "max_swatches",
      "min": 3,
      "max": 8,
      "step": 1,
      "default": 5,
      "label": "Maximum swatches per product"
    },
    {
      "type": "select",
      "id": "card_click_behavior",
      "label": "Card click behavior",
      "options": [
        { "value": "link_with_variant", "label": "Link to product with selected variant" },
        { "value": "quick_add_variant", "label": "Quick add selected variant" }
      ],
      "default": "link_with_variant"
    },
    {
      "type": "checkbox",
      "id": "show_quick_add",
      "label": "Show quick add button",
      "default": false
    },
    {
      "type": "select",
      "id": "card_image_aspect_ratio",
      "label": "Card image aspect ratio",
      "options": [
        { "value": "3/4", "label": "Portrait (3:4)" },
        { "value": "1/1", "label": "Square (1:1)" },
        { "value": "4/3", "label": "Landscape (4:3)" }
      ],
      "default": "3/4"
    }
  ],
  "blocks": [
    { "type": "product", "name": "Slide • product", "settings": [
    { "type": "checkbox", "id": "enable_image_reveal", "label": "Enable image reveal-on-scroll", "default": true },{ "type":"product", "id":"product", "label":"Product"}] },
    { "type": "collection", "name": "Slide • collection", "settings": [
    { "type": "checkbox", "id": "enable_image_reveal", "label": "Enable image reveal-on-scroll", "default": true },
      { "type":"collection", "id":"collection", "label":"Collection" },
      { "type":"range", "id":"limit", "label":"Products to show", "min":2, "max":12, "step":1, "default":4 }
    ]}
  ],
  "presets": [{ "name": "Editorial split (v2)" }]
}
{% endschema %}



