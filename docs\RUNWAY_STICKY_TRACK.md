# Runway Layout: Sticky Panel Track Configuration

## Overview

The Runway layout features a **sticky information panel** that floats over the horizontal product gallery. By default, the panel stays sticky for the height of the gallery. However, you can extend the sticky track to include additional sections below the product (like Hermès, Prada, and other luxury brands do), creating a more immersive browsing experience.

## How It Works

The sticky panel's track height is computed as:

```
track height = gallery height + sum(heights of "follow" sections)
```

The panel will remain sticky (floating over content) until the user scrolls past all included sections, then it naturally releases and scrolls away with the page.

## Configuration Methods

### Method 1: Declarative Attributes (Recommended)

Add data attributes to sections you want to include in the sticky track:

#### `data-runway-follow`
Add this attribute to any section that should be part of the panel's sticky track.

**Example:**
```liquid
<div id="shopify-section-recommendations" class="shopify-section" data-runway-follow>
  <!-- Shop the look / Product recommendations -->
</div>

<div id="shopify-section-recently-viewed" class="shopify-section" data-runway-follow>
  <!-- Recently viewed products -->
</div>
```

#### `data-runway-stop`
Add this attribute to a section where the sticky track should **stop** (the panel will release before this section).

**Example:**
```liquid
<div id="shopify-section-newsletter" class="shopify-section" data-runway-stop>
  <!-- Newsletter signup - panel should release before this -->
</div>
```

### Method 2: Automatic Detection (Default Fallback)

If no sections are tagged with `data-runway-follow`, the system automatically includes up to **2 sections** that match these patterns:

- `.product-recs` - Product recommendations
- `.recently-viewed` - Recently viewed products
- `[data-recent-container]` - Recent items container
- `[data-keep-exploring]` - Keep exploring sections

This provides sensible defaults without requiring manual configuration.

## Usage Examples

### Example 1: Include Recommendations Only

```liquid
{%- comment -%} Product section (automatic) {%- endcomment -%}
<div id="shopify-section-product" class="shopify-section">
  <!-- Product gallery and info panel -->
</div>

{%- comment -%} Include in sticky track {%- endcomment -%}
<div id="shopify-section-recommendations" class="shopify-section" data-runway-follow>
  {% section 'product-recommendations' %}
</div>

{%- comment -%} Panel releases before this section {%- endcomment -%}
<div id="shopify-section-footer" class="shopify-section">
  {% section 'footer' %}
</div>
```

**Result:** Panel stays sticky through the product gallery + recommendations section.

### Example 2: Include Multiple Sections

```liquid
{%- comment -%} Product section {%- endcomment -%}
<div id="shopify-section-product" class="shopify-section">
  <!-- Product gallery and info panel -->
</div>

{%- comment -%} All included in sticky track {%- endcomment -%}
<div id="shopify-section-shop-the-look" class="shopify-section" data-runway-follow>
  {% section 'complementary-products' %}
</div>

<div id="shopify-section-recommendations" class="shopify-section" data-runway-follow>
  {% section 'product-recommendations' %}
</div>

<div id="shopify-section-recently-viewed" class="shopify-section" data-runway-follow>
  {% section 'recently-viewed' %}
</div>

{%- comment -%} Panel releases before newsletter {%- endcomment -%}
<div id="shopify-section-newsletter" class="shopify-section" data-runway-stop>
  {% section 'newsletter' %}
</div>
```

**Result:** Panel stays sticky through product + 3 additional sections, releases before newsletter.

### Example 3: Explicit Stop Point

```liquid
<div id="shopify-section-product" class="shopify-section">
  <!-- Product gallery and info panel -->
</div>

<div id="shopify-section-recommendations" class="shopify-section" data-runway-follow>
  {% section 'product-recommendations' %}
</div>

<div id="shopify-section-content" class="shopify-section" data-runway-follow>
  <!-- Some content section -->
</div>

{%- comment -%} Stop here - panel releases {%- endcomment -%}
<div id="shopify-section-divider" class="shopify-section" data-runway-stop>
  <!-- Visual divider or transition -->
</div>

{%- comment -%} This section is NOT included even though it has data-runway-follow {%- endcomment -%}
<div id="shopify-section-more-content" class="shopify-section" data-runway-follow>
  <!-- This won't be included because we hit the stop point above -->
</div>
```

**Result:** Panel stays sticky through product + recommendations + content, releases at the divider. The "more-content" section is ignored because it comes after the stop point.

## Technical Details

### CSS Variables

The system uses these CSS variables:

- `--runway-track-h`: Computed total track height (gallery + follow sections)
- `--runway-slider-h`: Gallery height (fallback if track not computed)
- `--runway-extra-space`: Additional page height to ensure footer clearance
- `--header-h`: Header height (for sticky positioning)
- `--pi-top-gap`: Top gap for the panel (default: 6px)

### Responsive Behavior

- **Desktop (≥990px)**: Full sticky track behavior with computed heights
- **Mobile (<990px)**: Standard mobile layout (no sticky panel)

### Performance

The system uses:
- **ResizeObserver**: Automatically recalculates when content changes
- **MutationObserver**: Detects when sections are added/removed or attributes change
- **Passive event listeners**: Optimized scroll and resize handling
- **RequestAnimationFrame**: Smooth, performant updates

### Browser Support

- Modern browsers with CSS `position: sticky` support
- Graceful fallback: If observers aren't supported, initial calculation still works
- Mobile browsers: Standard layout (no sticky behavior)

## Best Practices

1. **Tag sections explicitly** for predictable behavior across different page templates
2. **Use `data-runway-stop`** before major content transitions (newsletter, footer, etc.)
3. **Limit follow sections** to 2-4 for optimal UX (too many can feel disorienting)
4. **Test on real content** - empty sections won't contribute height
5. **Consider mobile** - this only affects desktop; ensure mobile layout works well

## Troubleshooting

### Panel releases too early
- Check that sections have `data-runway-follow` attribute
- Verify sections have actual content/height
- Inspect `--runway-track-h` in DevTools to see computed value

### Panel never releases
- Add `data-runway-stop` to a section before the footer
- Check that you haven't included too many sections
- Verify `--runway-extra-space` is being calculated

### Panel content gets cut off
- The system automatically calculates `--runway-extra-space` to prevent this
- Check that `overflow: visible` is set on the panel card
- Verify no ancestor has `overflow: hidden`

### Footer collision
- The `::after` pseudo-element on the product section should extend the page
- Check that `--runway-extra-space` is being set correctly
- Inspect the product section's computed height in DevTools

## Customization

### Adjust Default Section Limit

In `sections/product.liquid`, find this line:

```javascript
if (guesses.length >= 2) break;
```

Change `2` to your preferred default limit (e.g., `3` or `4`).

### Customize Auto-Detection Selectors

Modify the `defaultsIfEmpty` function to match your theme's section patterns:

```javascript
const hit = sib.querySelector(
  '.product-recs, .recently-viewed, .your-custom-class, [data-your-attribute]'
);
```

### Adjust Top Gap

The panel's top gap can be customized via CSS variable:

```css
.product--layout-runway .product__info {
  --pi-top-gap: 12px; /* Increase gap from header */
}
```

## Related Documentation

- [Runway Layout Overview](./RUNWAY_LAYOUT.md)
- [Product Section Configuration](./PRODUCT_SECTION.md)
- [Theme Customization Guide](../USER_GUIDE.md)

