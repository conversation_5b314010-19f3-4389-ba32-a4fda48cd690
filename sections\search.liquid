{% comment %}
  Search Section (static on search template)

  - Product-focused search with optional filters and grid controls
  - Supports pagination, Load more, or Infinite scrolling
  - Optional color swatch rendering inside filters

  Docs: https://shopify.dev/docs/storefronts/themes/architecture/templates/search
{% endcomment %}
{% comment %}theme-check-disable ParserBlockingJavaScript{% endcomment %}

{%- liquid
  assign heading = section.settings.heading
  if heading == blank
    assign heading = 'search.title' | t
  endif
  assign filters_type = section.settings.filters_type | default: 'sidebar'
  assign show_grid_buttons = section.settings.show_grid_buttons | default: true
  assign show_filter_swatches = section.settings.show_filter_swatches | default: true
  assign paginate_by = section.settings.paginate_by | default: 24
  assign pagination_type = section.settings.pagination_type | default: 'pagination'
  assign container_width = section.settings.container_width | default: 'inherit'
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0

  comment
    Determine cols for desktop grid toggle; default to 4
  endcomment
  assign grid_cols_desktop = 4
-%}

{%- render 'color-swatch-mapping' -%}

<section class="search" data-section="{{ section.id }}">
  <div class="search__inner{% if container_width != 'inherit' %} search__inner--custom{% endif %} page-width"
    {% if container_width == 'narrow' %}style="--container-max: 90rem;"{% endif %}
    {% if container_width == 'wide' %}style="--container-max: 110rem;"{% endif %}
  >
    <header class="search__header">
      <h1 class="search__title h2">{{ heading }}</h1>
      <form class="search__form" action="{{ routes.search_url }}" method="get" role="search">
        <input
          type="search"
          name="q"
          value="{{ search.terms | escape }}"
          placeholder="{{ 'search.placeholder' | t }}"
          aria-label="{{ 'search.title' | t }}"
        >
        <input type="hidden" name="type" value="product">
        <button type="submit" class="button">{{ 'search.submit' | t }}</button>
      </form>

      {% if show_grid_buttons %}
        <div class="search__grid-toggle" role="group" aria-label="Grid columns">
          <button type="button" class="grid-toggle__btn" data-cols="3" aria-pressed="false" title="3 per row">3</button>
          <button type="button" class="grid-toggle__btn is-active" data-cols="4" aria-pressed="true" title="4 per row">4</button>
        </div>
      {% endif %}
    </header>

    <div class="search__layout search__layout--{{ filters_type }}" data-filters-type="{{ filters_type }}">
      {% if filters_type == 'sidebar' %}
        <aside class="search__filters" aria-label="Filters">
          {% render 'search-facets', show_swatches: show_filter_swatches %}
        </aside>
      {% else %}
        <div class="search__filters-drawer">
          <details class="filters-drawer" scroll-lock>
            <summary class="filters-drawer__toggle">
              {{ 'products.facets.filter_button' | t }}
            </summary>
            <div class="filters-drawer__content" role="dialog" aria-label="Filters">
              {% render 'search-facets', show_swatches: show_filter_swatches %}
              <button class="filters-drawer__close" type="button" onclick="this.closest('details').removeAttribute('open')">
                {{ 'general.close' | t }}
              </button>
            </div>
          </details>
        </div>
      {% endif %}

      <div class="search__results">
        {% if search.performed %}
          {% if search.results_count == 0 %}
            <p class="search__empty">{{ 'search.no_results_html' | t: terms: search.terms }}</p>
          {% else %}
            <p class="search__count">{{ 'search.results_for_html' | t: terms: search.terms, count: search.results_count }}</p>

            {% paginate search.results by paginate_by %}
              <ul
                id="SearchResults-{{ section.id }}"
                class="search-grid"
                data-cols-desktop="{{ grid_cols_desktop }}"
                data-pagination="{{ pagination_type }}"
              >
                {% for result in search.results %}
                  {% if result.object_type == 'product' %}
                    <li class="search-grid__item">
                      {% render 'card-product', product: result, show_swatches: true, columns: grid_cols_desktop %}
                    </li>
                  {% endif %}
                {% endfor %}
              </ul>

              {% if pagination_type == 'pagination' %}
                {% render 'pagination', paginate: paginate %}
              {% else %}
                <div class="search__progress" data-pagination-controls>
                  {% if paginate.next %}
                    <button
                      class="button button--secondary load-more"
                      data-next-url="{{ paginate.next.url }}"
                      type="button"
                    >
                      <span class="load-more__label">{% if pagination_type == 'load_more' %}Load more{% else %}Loading…{% endif %}</span>
                      {% render 'loading-spinner' %}
                    </button>
                  {% else %}
                    <span class="load-more__end" aria-live="polite">No more results</span>
                  {% endif %}
                </div>
              {% endif %}
            {% endpaginate %}
          {% endif %}
        {% endif %}
      </div>
    </div>
  </div>
</section>

{% stylesheet %}
  /* Container width and offsets */
  #shopify-section-{{ section.id }} {
    margin-top: {{ mobile_offset_top | default: 0 }}px;
    margin-bottom: {{ mobile_offset_bottom | default: 0 }}px;
  }
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} {
      margin-top: {{ desktop_offset_top | default: 0 }}px;
      margin-bottom: {{ desktop_offset_bottom | default: 0 }}px;
    }
  }

  .search__inner {
    max-width: var(--container-max, var(--page-width));
    margin: 0 auto;
    padding-left: var(--page-margin, 1rem);
    padding-right: var(--page-margin, 1rem);
  }

  .search__header {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
  }

  .search__form {
    display: flex;
    gap: .5rem;
    align-items: center;
  }

  .search__grid-toggle {
    display: inline-flex;
    gap: .25rem;
    border: 1px solid rgba(var(--color-foreground), .15);
    border-radius: 6px;
    padding: 2px;
  }
  .grid-toggle__btn {
    appearance: none;
    background: transparent;
    border: 0;
    border-radius: 4px;
    padding: .25rem .5rem;
    cursor: pointer;
  }
  .grid-toggle__btn.is-active {
    background: rgba(var(--color-foreground), .08);
  }

  .search__layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  @media (min-width: 990px) {
    .search__layout--sidebar {
      grid-template-columns: 260px 1fr;
      align-items: start;
    }
  }
  .search__filters {
    position: sticky;
    top: 1rem;
    align-self: start;
  }
  .filters-drawer[open] .filters-drawer__content {
    max-height: 80vh;
    overflow: auto;
    padding: 1rem;
    border: 1px solid rgba(var(--color-foreground), .15);
    border-radius: 8px;
    background: var(--color-background);
  }
  .filters-drawer__toggle {
    display: inline-flex;
    align-items: center;
    gap: .5rem;
    border: 1px solid rgba(var(--color-foreground), .2);
    padding: .5rem .75rem;
    border-radius: 6px;
    cursor: pointer;
  }

  .search__count { opacity: .7; margin-bottom: .5rem; }

  .search-grid {
    --cols-desktop: 4;
    --cols-tablet: 2;
    --cols-mobile: 1;
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(var(--cols-mobile), 1fr);
  }
  @media (min-width: 750px) {
    .search-grid { grid-template-columns: repeat(var(--cols-tablet), 1fr); }
  }
  @media (min-width: 990px) {
    .search-grid { grid-template-columns: repeat(var(--cols-desktop), 1fr); }
  }

  .search__progress { text-align: center; margin: 1.5rem 0; }

  .load-more { display: inline-flex; gap: .5rem; align-items: center; }
  .load-more .spinner { display: none; }
  .load-more.is-loading .spinner { display: inline-block; }
{% endstylesheet %}

<script>
  (function(){
    const root = document.getElementById('SearchResults-{{ section.id }}');
    if (!root) return;

    // Grid toggle (3 or 4 columns)
    const toggleWrap = document.querySelector('#shopify-section-{{ section.id }} .search__grid-toggle');
    if (toggleWrap) {
      const buttons = toggleWrap.querySelectorAll('.grid-toggle__btn');
      const apply = (cols) => {
        root.style.setProperty('--cols-desktop', cols);
        buttons.forEach(b => {
          const active = b.dataset.cols === String(cols);
          b.classList.toggle('is-active', active);
          b.setAttribute('aria-pressed', String(active));
        });
        try { localStorage.setItem('search_grid_cols', String(cols)); } catch(e) {}
      };
      // Restore
      try {
        const saved = localStorage.getItem('search_grid_cols');
        if (saved) apply(saved);
      } catch(e) {}
      // Bind
      buttons.forEach(btn => btn.addEventListener('click', () => apply(btn.dataset.cols)));
    }

    const paginationType = root.getAttribute('data-pagination');
    if (paginationType === 'pagination') return; // nothing to enhance

    const controls = document.querySelector('#shopify-section-{{ section.id }} [data-pagination-controls]');
    if (!controls) return;
    const btn = controls.querySelector('.load-more');

    const fetchNext = async () => {
      const nextUrl = btn?.getAttribute('data-next-url');
      if (!nextUrl) return false;
      btn.classList.add('is-loading');
      try {
        const res = await fetch(nextUrl, { headers: { 'X-Requested-With': 'XMLHttpRequest' }});
        const html = await res.text();
        const doc = new DOMParser().parseFromString(html, 'text/html');
        const newList = doc.querySelector('#SearchResults-{{ section.id }}');
        const newBtn = doc.querySelector('#shopify-section-{{ section.id }} .load-more');
        if (newList) {
          newList.querySelectorAll('.search-grid__item').forEach(item => root.appendChild(item));
        }
        if (newBtn) {
          btn.setAttribute('data-next-url', newBtn.getAttribute('data-next-url'));
        } else {
          // No more pages
          btn.remove();
          const end = document.createElement('span');
          end.className = 'load-more__end';
          end.textContent = 'No more results';
          controls.appendChild(end);
          return false;
        }
      } catch(e) {
        console.error(e);
      } finally {
        btn.classList.remove('is-loading');
      }
      return true;
    };

    if (paginationType === 'load_more' && btn) {
      btn.addEventListener('click', fetchNext);
    } else if (paginationType === 'infinite') {
      const io = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) fetchNext();
        });
      }, { rootMargin: '400px' });
      if (btn) io.observe(btn);
    }
  })();
</script>

{% schema %}
{
  "name": "Search",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Search"
    },
    {
      "type": "checkbox",
      "id": "show_filter_swatches",
      "label": "Show color swatch in filters",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_grid_buttons",
      "label": "Show grid button",
      "default": true
    },
    {
      "type": "select",
      "id": "filters_type",
      "label": "Filters type",
      "default": "sidebar",
      "options": [
        { "value": "sidebar", "label": "Sidebar" },
        { "value": "drawer", "label": "Drawer" }
      ]
    },
    {
      "type": "range",
      "id": "paginate_by",
      "label": "Paginate by",
      "min": 8,
      "max": 60,
      "step": 4,
      "default": 24
    },
    {
      "type": "select",
      "id": "pagination_type",
      "label": "Pagination type",
      "default": "pagination",
      "options": [
        { "value": "pagination", "label": "Pagination" },
        { "value": "load_more", "label": "Load more" },
        { "value": "infinite", "label": "Infinite scrolling" }
      ]
    },
    { "type": "header", "content": "Layout" },
    {
      "type": "select",
      "id": "container_width",
      "label": "Container width",
      "default": "inherit",
      "options": [
        { "value": "inherit", "label": "Inherit" },
        { "value": "narrow", "label": "Narrow" },
        { "value": "wide", "label": "Wide" }
      ]
    },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ]
}
{% endschema %}
