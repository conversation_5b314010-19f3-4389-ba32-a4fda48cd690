/* Image Zoom on Scroll — IntersectionObserver-driven */
(function () {
  'use strict';

  function init() {
    if (!('IntersectionObserver' in window)) {
      // Fallback: immediately mark as zoomed/revealed
      document.querySelectorAll('[data-zoom-on-scroll]').forEach(setupAndTriggerZoom);
      document.querySelectorAll('[data-reveal-on-scroll]').forEach(setupAndTriggerReveal);
      document.querySelectorAll('[data-text-reveal]').forEach(setupAndTriggerTextReveal);
      return;
    }

    var observerZoom = new IntersectionObserver(function (entries) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          setupAndTriggerZoom(entry.target);
          observerZoom.unobserve(entry.target);
        }
      });
    }, {
      root: null,
      rootMargin: '0px 0px -10% 0px',
      threshold: 0.1
    });

    var observerReveal = new IntersectionObserver(function (entries) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          setupAndTriggerReveal(entry.target);
          observerReveal.unobserve(entry.target);
        }
      });
    }, {
      root: null,
      rootMargin: '0px 0px -10% 0px',
      threshold: 0.1
    });

    var observerTextReveal = new IntersectionObserver(function (entries) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          setupAndTriggerTextReveal(entry.target);
          observerTextReveal.unobserve(entry.target);
        }
      });
    }, {
      root: null,
      rootMargin: '0px 0px -10% 0px',
      threshold: 0.1
    });

    // Check which elements are above the fold on initial load
    var viewportHeight = window.innerHeight;
    var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    document.querySelectorAll('[data-zoom-on-scroll]').forEach(function (el) {
      applyZoomOptions(el);

      // Get element position
      var rect = el.getBoundingClientRect();
      var elementTop = rect.top + scrollTop;

      // If element is below the fold (not visible on initial load), observe it
      // Otherwise, trigger a simple zoom-in on page load
      if (elementTop > scrollTop + viewportHeight) {
        observerZoom.observe(el);
      } else {
        setupAndTriggerZoom(el);
      }
    });

    // Add a small onload delay for above-the-fold reveals to let the page settle
    var defaultOnloadRevealDelay = 300; // ms
    document.querySelectorAll('[data-reveal-on-scroll]').forEach(function (el) {
      applyRevealOptions(el);

      // Get element position
      var rect = el.getBoundingClientRect();
      var elementTop = rect.top + scrollTop;

      // If element is below the fold, observe it
      if (elementTop > scrollTop + viewportHeight) {
        observerReveal.observe(el);
      } else {
        // Above-the-fold: wait a touch after full load before revealing
        var attrDelay = parseInt(el.getAttribute('data-reveal-onload-delay') || '', 10);
        var delay = isNaN(attrDelay) ? defaultOnloadRevealDelay : Math.max(0, attrDelay);
        setTimeout(function(){ setupAndTriggerReveal(el); }, delay);
      }
    });

    // Text reveal: separate pipeline from generic reveal
    var defaultOnloadTextDelay = 300; // ms
    document.querySelectorAll('[data-text-reveal]').forEach(function (el) {
      applyTextRevealOptions(el);

      var rect = el.getBoundingClientRect();
      var elementTop = rect.top + scrollTop;

      if (elementTop > scrollTop + viewportHeight) {
        observerTextReveal.observe(el);
      } else {
        var attrDelay = parseInt(el.getAttribute('data-text-reveal-onload-delay') || '', 10);
        var delay = isNaN(attrDelay) ? defaultOnloadTextDelay : Math.max(0, attrDelay);
        setTimeout(function(){ setupAndTriggerTextReveal(el); }, delay);
      }
    });
  }

  function applyZoomOptions(el) {
    var from = el.getAttribute('data-zoom-from');
    var to = el.getAttribute('data-zoom-to');
    var duration = el.getAttribute('data-zoom-duration');
    var delay = el.getAttribute('data-zoom-delay');
    var ease = el.getAttribute('data-zoom-ease');

    if (from) el.style.setProperty('--zoom-from', from);
    if (to) el.style.setProperty('--zoom-to', to);
    if (duration) el.style.setProperty('--zoom-duration', duration);
    if (delay) el.style.setProperty('--zoom-delay', delay);
    if (ease) el.style.setProperty('--zoom-ease', ease);
  }

  function applyRevealOptions(el) {
    var duration = el.getAttribute('data-reveal-duration');
    var delay = el.getAttribute('data-reveal-delay');
    var ease = el.getAttribute('data-reveal-ease');
    var translate = el.getAttribute('data-reveal-translate');
    if (duration) el.style.setProperty('--reveal-duration', duration);
    if (delay) el.style.setProperty('--reveal-delay', delay);
    if (ease) el.style.setProperty('--reveal-ease', ease);
    if (translate) el.style.setProperty('--reveal-translate', translate);
  }

  function applyTextRevealOptions(el) {
    var duration = el.getAttribute('data-text-reveal-duration');
    var delay = el.getAttribute('data-text-reveal-delay');
    var ease = el.getAttribute('data-text-reveal-ease');
    var translate = el.getAttribute('data-text-reveal-translate');
    if (duration) el.style.setProperty('--text-reveal-duration', duration);
    if (delay) el.style.setProperty('--text-reveal-delay', delay);
    if (ease) el.style.setProperty('--text-reveal-ease', ease);
    if (translate) el.style.setProperty('--text-reveal-translate', translate);
  }

  function setupAndTriggerZoom(el) {
    applyZoomOptions(el);
    // Ensure initial styles apply before toggling the class
    // to avoid any initial flicker or reverse motion.
    // Using rAF twice guards against cases where layout is pending.
    requestAnimationFrame(function () {
      // Force layout flush
      void el.offsetWidth;
      requestAnimationFrame(function () {
        el.classList.add('is-zoomed');
      });
    });
  }

  function setupAndTriggerReveal(el) {
    applyRevealOptions(el);
    // Guard against flicker: ensure initial styles apply first
    requestAnimationFrame(function(){
      void el.offsetWidth;
      requestAnimationFrame(function(){ el.classList.add('is-revealed'); });
    });
  }

  function setupAndTriggerTextReveal(el) {
    applyTextRevealOptions(el);
    requestAnimationFrame(function(){
      void el.offsetWidth;
      requestAnimationFrame(function(){ el.classList.add('is-text-revealed'); });
    });
  }

  // Defer activation until the full page load so above-the-fold
  // media won’t animate immediately on first paint.
  if (document.readyState === 'complete') {
    init();
  } else {
    window.addEventListener('load', init);
  }
})();
