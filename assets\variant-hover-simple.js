/* Simple Swatch Hover Test - variant-hover-simple.js */
(function() {
  'use strict';
  
  console.log('🎯 Simple swatch hover test script loaded at', new Date());
  
  // More comprehensive element search
  function debugPageElements() {
    console.log('🔍 === DEBUG: Page Elements ===');
    
    // Check all possible card selectors
    const cardWrappers = document.querySelectorAll('.card-wrapper');
    const cards = document.querySelectorAll('.card');
    const cardMedia = document.querySelectorAll('.card__media');
    const swatches = document.querySelectorAll('.swatch');
    const swatchButtons = document.querySelectorAll('button.swatch');
    const swatchContainers = document.querySelectorAll('.card__swatches');
    
    console.log('Cards found:');
    console.log('  .card-wrapper:', cardWrappers.length);
    console.log('  .card:', cards.length);
    console.log('  .card__media:', cardMedia.length);
    console.log('  .card__swatches:', swatchContainers.length);
    console.log('  .swatch:', swatches.length);
    console.log('  button.swatch:', swatchButtons.length);
    
    if (swatches.length > 0) {
      console.log('🔍 First swatch details:', {
        element: swatches[0],
        classList: Array.from(swatches[0].classList),
        variantId: swatches[0].dataset.variantId,
        src: swatches[0].dataset.src,
        colorName: swatches[0].dataset.colorName,
        mediaId: swatches[0].dataset.mediaId,
        allDatasets: swatches[0].dataset
      });
      
      // Check parent structure
      const parent = swatches[0].closest('.card-wrapper');
      console.log('🔍 First swatch parent card:', parent);
      if (parent) {
        const primaryImg = parent.querySelector('.card-media__primary');
        const previewImg = parent.querySelector('.card-media__preview');
        console.log('🔍 Images in parent card:', {
          primary: primaryImg,
          preview: previewImg,
          primarySrc: primaryImg?.src,
          previewSrc: previewImg?.src
        });
      }
    }
    
    // Check if scripts are loading multiple times
    const scripts = document.querySelectorAll('script[src*="variant-hover"]');
    console.log('🔍 Script tags found:', scripts.length);
  }
  
  // Run debug immediately and after delays
  debugPageElements();
  setTimeout(debugPageElements, 1000);
  setTimeout(debugPageElements, 3000);
  
  // Additional debug: Check section settings
  setTimeout(() => {
    const section = document.querySelector('.collection-product-grid');
    if (section) {
      console.log('🔍 Section data attributes:', {
        swatchMode: section.dataset.swatchMode,
        allAttributes: section.getAttributeNames().reduce((acc, name) => {
          acc[name] = section.getAttribute(name);
          return acc;
        }, {})
      });
    }
  }, 4000);
  
  function initVariantDisplay() {
    console.log('🚀 Initializing variant display system...');
    
    // Check if we're in hover overlay mode (look for data attribute on section)
    const section = document.querySelector('[data-swatch-mode]');
    const isHoverMode = section?.dataset.swatchMode === 'hover_overlay';
    

    
    if (!isHoverMode) {
      return;
    }
    
    // Find all product cards
    const cards = document.querySelectorAll('.card-wrapper, .card');
    
    cards.forEach((card, index) => {
      // Find existing variant options in this card
      const swatchContainer = card.querySelector('.card__swatches');
      
      // Create color swatches HTML
      let colorSwatchesHTML = '';
      if (swatchContainer && swatchContainer.innerHTML.trim()) {
        colorSwatchesHTML = `<div class="overlay-colors">${swatchContainer.outerHTML}</div>`;
      }
      
      // Create overlay element
      const overlay = document.createElement('div');
      overlay.className = 'card-hover-overlay';
      overlay.innerHTML = `
        <div class="card-hover-overlay__content">
          ${colorSwatchesHTML}
          <div class="overlay-sizes">
            <button class="size-btn">XS</button>
            <button class="size-btn">S</button>
            <button class="size-btn">M</button>
            <button class="size-btn">L</button>
            <button class="size-btn">XL</button>
            <button class="size-btn">XXL</button>
          </div>
          <button class="overlay-quick-add">QUICK ADD</button>
        </div>
      `;
      
      // Insert overlay into card
      const cardMedia = card.querySelector('.card__media, .media');
      if (cardMedia) {
        cardMedia.style.position = 'relative';
        cardMedia.appendChild(overlay);
      }
      
      // Initially hide overlay
      overlay.style.display = 'none';
      
      // Show overlay on card hover
      card.addEventListener('mouseenter', function() {
        console.log(`�️ Card ${index + 1}: Showing overlay`);
        overlay.style.display = 'flex';
      });
      
      // Hide overlay when leaving card
      card.addEventListener('mouseleave', function() {
        console.log(`�️ Card ${index + 1}: Hiding overlay`);
        overlay.style.display = 'none';
      });
      
      // Visual debug indicator
      card.style.border = '2px dashed blue';
      card.title = (card.title || '') + ' [Hover to show variants overlay]';
    });
    
    if (cards.length === 0) {
      console.log('❌ No product cards found!');
    }
  }
  
  // Run immediately and with delays to catch late-loading content
  document.addEventListener('DOMContentLoaded', initVariantDisplay);
  
  // Also run on Shopify section loads
  document.addEventListener('shopify:section:load', initVariantDisplay);
  
  // Run after a short delay to catch dynamic content
  setTimeout(initVariantDisplay, 500);
  setTimeout(initVariantDisplay, 1500);
})();
