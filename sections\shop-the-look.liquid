{% comment %}
  Shop The Look

  - Large image with up to 4 hotspots mapped to products
  - Optional: first hotspot opens by default on desktop
  - Product panel renders product cards; thumbnails slider to switch items
  - Colors for overlay, hotspots, thumbnails overlay and active borders
  - Layout controls: container width + offsets
{% endcomment %}

{%- liquid
  assign heading = section.settings.heading
  assign text = section.settings.text

  assign container_width = section.settings.container_width | default: 'inherit'
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0

  assign overlay_color = section.settings.overlay_color | default: '#000000'
  assign overlay_opacity = section.settings.overlay_opacity | default: 0
  assign hotspot_color = section.settings.hotspot_color | default: '#ffffff'
  assign hotspot_bg = section.settings.hotspot_bg | default: '#111111'
  assign hotspot_color_active = section.settings.hotspot_color_active | default: '#111111'
  assign hotspot_bg_active = section.settings.hotspot_bg_active | default: '#ffffff'
  assign thumbs_overlay_color = section.settings.thumbs_overlay_color | default: '#000000'
  assign thumbs_overlay_opacity = section.settings.thumbs_overlay_opacity | default: 0
  assign thumb_active_border_desktop = section.settings.thumb_active_border_desktop | default: '#111111'
  assign thumb_active_border_mobile = section.settings.thumb_active_border_mobile | default: '#111111'

  assign first_opened = section.settings.first_opened | default: true
-%}

<section class="shop-the-look" data-section="{{ section.id }}">
  <div class="stl__inner{% if container_width != 'inherit' %} stl__inner--custom{% endif %} page-width"
       {% if container_width == 'narrow' %}style="--container-max: 90rem;"{% endif %}
       {% if container_width == 'wide' %}style="--container-max: 110rem;"{% endif %}
  >
    {% if heading or text %}
      <header class="stl__header">
        {% if heading %}<h2 class="stl__title h2">{{ heading }}</h2>{% endif %}
        {% if text %}<div class="stl__text rte">{{ text }}</div>{% endif %}
      </header>
    {% endif %}

    {% assign has_blocks = false %}
    {% for block in section.blocks %}
      {% if block.type == 'look' %}{% assign has_blocks = true %}{% break %}{% endif %}
    {% endfor %}

    {% if has_blocks %}
      {% for block in section.blocks %}
        {% if block.type == 'look' %}
          {%- liquid
            assign img_d = block.settings.image_desktop
            assign img_m = block.settings.image_mobile
            assign p1 = block.settings.product_1
            assign p2 = block.settings.product_2
            assign p3 = block.settings.product_3
            assign p4 = block.settings.product_4
          -%}

          <div class="stl-look" id="Look-{{ block.id }}" {{ block.shopify_attributes }}
               style="--overlay-color: {{ overlay_color }}; --overlay-opacity: {{ overlay_opacity }}; --hotspot-color: {{ hotspot_color }}; --hotspot-bg: {{ hotspot_bg }}; --hotspot-color-active: {{ hotspot_color_active }}; --hotspot-bg-active: {{ hotspot_bg_active }}; --thumbs-overlay-color: {{ thumbs_overlay_color }}; --thumbs-overlay-opacity: {{ thumbs_overlay_opacity }}; --thumb-border-desktop: {{ thumb_active_border_desktop }}; --thumb-border-mobile: {{ thumb_active_border_mobile }};">
            <div class="stl-media">
              <div class="stl-overlay" aria-hidden="true"></div>
              <picture>
                {% if img_m %}
                  <source media="(max-width: 749px)" srcset="{{ img_m | image_url: width: 1200 }} 1200w" sizes="100vw">
                {% endif %}
                {% if img_d %}
                  {{ img_d | image_url: width: 2000 | image_tag: widths: '1000, 1400, 1600, 2000', sizes: '100vw', loading: 'lazy', decoding: 'async', alt: '' }}
                {% else %}
                  {{ 'image' | placeholder_svg_tag: 'stl-placeholder' }}
                {% endif %}
              </picture>

              {% comment %} Hotspots {% endcomment %}
              {% if p1 %}
                <button class="stl-hotspot" data-index="0"
                  style="--top-d: {{ block.settings.p1_top_desktop | default: 50 }}%; --left-d: {{ block.settings.p1_left_desktop | default: 50 }}%; --top-m: {{ block.settings.p1_top_mobile | default: 50 }}%; --left-m: {{ block.settings.p1_left_mobile | default: 50 }}%;"
                  aria-label="View {{ p1.title | escape }}"></button>
              {% endif %}
              {% if p2 %}
                <button class="stl-hotspot" data-index="1"
                  style="--top-d: {{ block.settings.p2_top_desktop | default: 50 }}%; --left-d: {{ block.settings.p2_left_desktop | default: 50 }}%; --top-m: {{ block.settings.p2_top_mobile | default: 50 }}%; --left-m: {{ block.settings.p2_left_mobile | default: 50 }}%;"
                  aria-label="View {{ p2.title | escape }}"></button>
              {% endif %}
              {% if p3 %}
                <button class="stl-hotspot" data-index="2"
                  style="--top-d: {{ block.settings.p3_top_desktop | default: 50 }}%; --left-d: {{ block.settings.p3_left_desktop | default: 50 }}%; --top-m: {{ block.settings.p3_top_mobile | default: 50 }}%; --left-m: {{ block.settings.p3_left_mobile | default: 50 }}%;"
                  aria-label="View {{ p3.title | escape }}"></button>
              {% endif %}
              {% if p4 %}
                <button class="stl-hotspot" data-index="3"
                  style="--top-d: {{ block.settings.p4_top_desktop | default: 50 }}%; --left-d: {{ block.settings.p4_left_desktop | default: 50 }}%; --top-m: {{ block.settings.p4_top_mobile | default: 50 }}%; --left-m: {{ block.settings.p4_left_mobile | default: 50 }}%;"
                  aria-label="View {{ p4.title | escape }}"></button>
              {% endif %}
            </div>

            <div class="stl-panel" data-panel>
              {% if p1 %}<div class="stl-panel__item" data-index="0">{%- render 'card-product', card_product: p1, columns: 4, show_swatches: false -%}</div>{% endif %}
              {% if p2 %}<div class="stl-panel__item" data-index="1">{%- render 'card-product', card_product: p2, columns: 4, show_swatches: false -%}</div>{% endif %}
              {% if p3 %}<div class="stl-panel__item" data-index="2">{%- render 'card-product', card_product: p3, columns: 4, show_swatches: false -%}</div>{% endif %}
              {% if p4 %}<div class="stl-panel__item" data-index="3">{%- render 'card-product', card_product: p4, columns: 4, show_swatches: false -%}</div>{% endif %}
            </div>

            <div class="stl-thumbs" data-thumbs>
              <div class="stl-thumbs__overlay" aria-hidden="true"></div>
              <div class="stl-thumbs__track">
                {% if p1 and p1.featured_media %}
                  <button class="stl-thumb" data-index="0" aria-label="{{ p1.title | escape }}">
                    {{ p1.featured_media | image_url: width: 120 | image_tag: widths: '80, 100, 120', sizes: '60px', alt: '' }}
                  </button>
                {% endif %}
                {% if p2 and p2.featured_media %}
                  <button class="stl-thumb" data-index="1" aria-label="{{ p2.title | escape }}">
                    {{ p2.featured_media | image_url: width: 120 | image_tag: widths: '80, 100, 120', sizes: '60px', alt: '' }}
                  </button>
                {% endif %}
                {% if p3 and p3.featured_media %}
                  <button class="stl-thumb" data-index="2" aria-label="{{ p3.title | escape }}">
                    {{ p3.featured_media | image_url: width: 120 | image_tag: widths: '80, 100, 120', sizes: '60px', alt: '' }}
                  </button>
                {% endif %}
                {% if p4 and p4.featured_media %}
                  <button class="stl-thumb" data-index="3" aria-label="{{ p4.title | escape }}">
                    {{ p4.featured_media | image_url: width: 120 | image_tag: widths: '80, 100, 120', sizes: '60px', alt: '' }}
                  </button>
                {% endif %}
              </div>
            </div>
          </div>
        {% endif %}
      {% endfor %}
    {% else %}
      <p class="stl__empty">Add a Look block and choose products.</p>
    {% endif %}
  </div>
</section>

<script>
  (() => {
    const root = document.getElementById('shopify-section-{{ section.id }}');
    if (!root) return;

    const firstOpened = {{ first_opened | json }};
    const DESKTOP = 990;

    root.querySelectorAll('.stl-look').forEach((look) => {
      const hotspots = look.querySelectorAll('.stl-hotspot');
      const thumbs = look.querySelectorAll('.stl-thumb');
      const panel = look.querySelector('[data-panel]');
      const items = panel ? panel.querySelectorAll('.stl-panel__item') : [];

      const setActive = (i, fromUser = false) => {
        hotspots.forEach((el, idx) => el.classList.toggle('is-active', idx === i));
        thumbs.forEach((el, idx) => el.classList.toggle('is-active', idx === i));
        if (panel) items.forEach((el, idx) => el.toggleAttribute('hidden', idx !== i));
      };

      hotspots.forEach(btn => btn.addEventListener('click', () => setActive(parseInt(btn.getAttribute('data-index')))));
      thumbs.forEach(btn => btn.addEventListener('click', () => setActive(parseInt(btn.getAttribute('data-index')))));

      const initDefault = () => {
        const w = window.innerWidth || document.documentElement.clientWidth;
        if (firstOpened && w >= DESKTOP && hotspots.length) {
          setActive(0);
        } else {
          // Ensure all hidden by default on mobile if not explicitly enabled
          if (panel) items.forEach(el => el.setAttribute('hidden', ''));
        }
      };
      initDefault();
      window.addEventListener('resize', initDefault, { passive: true });
    });
  })();
</script>

{% style %}
  /* Offsets */
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; }
  }

  .stl__inner.page-width { max-width: var(--container-max, var(--page-width)); margin: 0 auto; padding-left: var(--page-margin, 1rem); padding-right: var(--page-margin, 1rem); }
  .stl__header { margin-bottom: var(--heading-gap); text-align: left; }
  .stl__text { color: rgba(var(--color-foreground), .8); max-width: 60rem; }

  .stl-look { display: grid; gap: 1rem; margin-bottom: 2rem; }
  .stl-media { position: relative; }
  .stl-media picture, .stl-media img, .stl-media svg { display: block; width: 100%; height: auto; }
  .stl-overlay { position: absolute; inset: 0; background: var(--overlay-color); opacity: var(--overlay-opacity); pointer-events: none; }

  .stl-hotspot { position: absolute; transform: translate(-50%, -50%); top: var(--top-m); left: var(--left-m); border-radius: 999px; width: 28px; height: 28px; border: 2px solid var(--hotspot-bg); background: var(--hotspot-bg); color: var(--hotspot-color); box-shadow: 0 0 0 2px rgba(255,255,255,.4); cursor: pointer; }
  .stl-hotspot.is-active { background: var(--hotspot-bg-active); color: var(--hotspot-color-active); border-color: var(--hotspot-bg-active); }
  @media (min-width: 990px) { .stl-hotspot { top: var(--top-d); left: var(--left-d); } }

  .stl-panel { display: grid; gap: 1rem; }
  @media (min-width: 990px) {
    .stl-look { grid-template-columns: 3fr 2fr; align-items: start; }
    .stl-panel { position: sticky; top: 2rem; }
  }

  .stl-thumbs { position: relative; padding-top: .25rem; }
  .stl-thumbs__overlay { position: absolute; inset: 0; background: var(--thumbs-overlay-color); opacity: var(--thumbs-overlay-opacity); pointer-events: none; border-radius: 6px; }
  .stl-thumbs__track { display: grid; gap: .5rem; grid-auto-flow: column; overflow-x: auto; padding: .5rem; }
  .stl-thumb { background: transparent; border: 2px solid transparent; border-radius: 6px; padding: 0; cursor: pointer; line-height: 0; }
  .stl-thumb.is-active { border-color: var(--thumb-border-mobile); }
  @media (min-width: 990px) { .stl-thumb.is-active { border-color: var(--thumb-border-desktop); } }

  .stl__empty { opacity: .7; }
{% endstyle %}

{% schema %}
{
  "name": "Shop the look",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "text", "id": "heading", "label": "Heading", "default": "Shop the look" },
    { "type": "richtext", "id": "text", "label": "Text" },
    { "type": "checkbox", "id": "first_opened", "label": "Make first opened (desktop)", "default": true },

    { "type": "header", "content": "Colors" },
    { "type": "color", "id": "overlay_color", "label": "Overlay color", "default": "#000000" },
    { "type": "range", "id": "overlay_opacity", "label": "Overlay opacity", "min": 0, "max": 1, "step": 0.1, "default": 0 },
    { "type": "color", "id": "hotspot_color", "label": "Hot spot", "default": "#ffffff" },
    { "type": "color", "id": "hotspot_bg", "label": "Hot spot background", "default": "#111111" },
    { "type": "color", "id": "hotspot_color_active", "label": "Hot spot (active)", "default": "#111111" },
    { "type": "color", "id": "hotspot_bg_active", "label": "Hot spot background (active)", "default": "#ffffff" },
    { "type": "color", "id": "thumbs_overlay_color", "label": "Thumbs slider overlay color", "default": "#000000" },
    { "type": "range", "id": "thumbs_overlay_opacity", "label": "Thumbs slider overlay opacity", "min": 0, "max": 1, "step": 0.1, "default": 0 },
    { "type": "color", "id": "thumb_active_border_desktop", "label": "Thumb active (desktop)", "default": "#111111" },
    { "type": "color", "id": "thumb_active_border_mobile", "label": "Thumb active (mobile)", "default": "#111111" },

    { "type": "header", "content": "Layout" },
    {
      "type": "select",
      "id": "container_width",
      "label": "Container width",
      "default": "inherit",
      "options": [
        { "value": "inherit", "label": "Inherit" },
        { "value": "narrow", "label": "Narrow" },
        { "value": "wide", "label": "Wide" }
      ]
    },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "blocks": [
    {
      "type": "look",
      "name": "Look",
      "limit": 4,
      "settings": [
        { "type": "image_picker", "id": "image_desktop", "label": "Image (desktop)" },
        { "type": "image_picker", "id": "image_mobile", "label": "Image (mobile)" },

        { "type": "header", "content": "Product 1" },
        { "type": "product", "id": "product_1", "label": "Product" },
        { "type": "range", "id": "p1_top_desktop", "label": "Point position top (desktop)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p1_left_desktop", "label": "Point position left (desktop)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p1_top_mobile", "label": "Point position top (mobile)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p1_left_mobile", "label": "Point position left (mobile)", "min": 0, "max": 100, "step": 1, "default": 50 },

        { "type": "header", "content": "Product 2" },
        { "type": "product", "id": "product_2", "label": "Product" },
        { "type": "range", "id": "p2_top_desktop", "label": "Point position top (desktop)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p2_left_desktop", "label": "Point position left (desktop)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p2_top_mobile", "label": "Point position top (mobile)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p2_left_mobile", "label": "Point position left (mobile)", "min": 0, "max": 100, "step": 1, "default": 50 },

        { "type": "header", "content": "Product 3" },
        { "type": "product", "id": "product_3", "label": "Product" },
        { "type": "range", "id": "p3_top_desktop", "label": "Point position top (desktop)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p3_left_desktop", "label": "Point position left (desktop)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p3_top_mobile", "label": "Point position top (mobile)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p3_left_mobile", "label": "Point position left (mobile)", "min": 0, "max": 100, "step": 1, "default": 50 },

        { "type": "header", "content": "Product 4" },
        { "type": "product", "id": "product_4", "label": "Product" },
        { "type": "range", "id": "p4_top_desktop", "label": "Point position top (desktop)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p4_left_desktop", "label": "Point position left (desktop)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p4_top_mobile", "label": "Point position top (mobile)", "min": 0, "max": 100, "step": 1, "default": 50 },
        { "type": "range", "id": "p4_left_mobile", "label": "Point position left (mobile)", "min": 0, "max": 100, "step": 1, "default": 50 }
      ]
    }
  ],
  "presets": [
    { "name": "Shop the look", "blocks": [ { "type": "look" } ] }
  ]
}
{% endschema %}
