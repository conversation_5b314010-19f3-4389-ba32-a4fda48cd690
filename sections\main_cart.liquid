<section class="cart container container--cart" data-cart-root>
  <header class="cart-header text-center">
    <h1>Your Shopping Cart</h1>
  </header>

  {%- assign threshold = section.settings.free_shipping_threshold | times: 100 -%}
  <div class="cart-progress" aria-live="polite">
    {%- assign pct = 0 -%}
    {%- if threshold > 0 -%}
      {%- assign pct = cart.total_price | divided_by: threshold | times: 100 | at_most: 100 -%}
    {%- endif -%}
    <div class="cart-progress__bar"><span style="width:{{ pct }}%"></span></div>
    <div class="cart-progress__msg">
      {%- if cart.total_price < threshold and threshold > 0 -%}
        You're <strong>{{ threshold | minus: cart.total_price | money }}</strong> away from your free standard shipping!
      {%- elsif threshold > 0 -%}
        🎉 You've unlocked free standard shipping!
      {%- endif -%}
      {%- if threshold > 0 -%}<strong class="text-nowrap">{{ threshold | money }}</strong>{%- endif -%}
    </div>
  </div>

  <div class="cart-layout">
    <!-- LEFT: items -->
    <div class="cart-items">
      {% if cart.item_count == 0 %}
        <p>Your cart is empty.</p>
      {% else %}
        {% for item in cart.items %}
          {% render 'cart-item', item: item %}
        {% endfor %}
      {% endif %}
    </div>

    <!-- RIGHT: summary -->
    <aside class="cart-summary" data-cart-summary>
      <h3>Summary</h3>

      <div class="summary-row">
        <span><strong>Total ({{ cart.item_count }} items)</strong></span>
        <strong data-cart-total>{{ cart.total_price | money }}</strong>
      </div>

      <form action="/cart" method="post" class="checkout">
        <button type="submit" name="checkout" class="btn btn--primary btn--xl">Checkout securely »</button>
      </form>

      {% if section.settings.show_payment_icons %}
        <div class="payment-icons">{% render 'payment-icons' %}</div>
      {% endif %}
      <p class="disclaimer">Discount codes or gift cards can be entered on the checkout page.</p>
    </aside>
  </div>
</section>

{% schema %}
{
  "name": "Main cart",
  "settings": [
    { "type": "header", "content": "Free shipping" },
    { "type": "text", "id": "free_shipping_threshold", "label": "Threshold (shop currency, e.g. 120)", "default": "120" },
    { "type": "header", "content": "UI" },
    { "type": "checkbox", "id": "show_payment_icons", "label": "Show payment icons", "default": true }
  ]
}
{% endschema %}

{% stylesheet %}
.container--cart { width: 100%; margin: 0 auto; }
.cart-header h1 { margin: 16px 0 24px; }
.cart-progress { margin: 8px 0 24px; }
.cart-progress__bar { height: 8px; background:#e6e6e6; border-radius: 999px; position: relative; }
.cart-progress__bar span { display:block; height:100%; background:#111; border-radius: 999px; transition: width .2s; }
.cart-progress__msg { display:flex; justify-content:space-between; align-items:center; gap:16px; margin-top:8px; }
.cart-layout { display:grid; grid-template-columns: 1fr 360px; gap: 32px; align-items:start; }
@media (max-width: 900px){ .cart-layout{ grid-template-columns: 1fr; } .cart-summary{ position:static } }
.cart-items { display:flex; flex-direction:column; gap: 16px; }
.cart-summary { position: sticky; top: 16px; border:1px solid #e5e5e5; border-radius:16px; padding:16px; }
.summary-row { display:flex; justify-content:space-between; margin: 8px 0 16px; }
.btn--primary { background:#000; color:#fff; border-radius:999px; padding:16px 24px; width:100%; }
.disclaimer { color:#666; font-size:12px; text-align:center; margin-top:12px; }
{% endstylesheet %}

{% javascript %}
/* Minimal AJAX cart controller (change qty/remove + recompute UI) */
(function(){
  const root = document.querySelector('[data-cart-root]');
  if(!root || root.dataset.init) return; root.dataset.init = '1';

  const fmt = (cents) => {
    try { return new Intl.NumberFormat('{{ shop.locale }}', { style:'currency', currency: '{{ cart.currency.iso_code }}' }).format(cents/100); }
    catch { return (cents/100).toFixed(2); }
  };

  async function updateLine(key, quantity){
    const res = await fetch('/cart/change.js', {
      method:'POST',
      headers:{'Content-Type':'application/json'},
      body: JSON.stringify({ id: key, quantity })
    });
    const cart = await res.json();
    redraw(cart);
  }

  function redraw(cart){
    // per-item subtotal and qty fields
    cart.items.forEach(i => {
      const row = document.querySelector(`[data-line-key="${i.key}"]`);
      if(!row) return;
      row.querySelector('[data-line-qty]').value = i.quantity;
      row.querySelector('[data-line-subtotal]').textContent = fmt(i.final_line_price);
    });
    // removed lines
    document.querySelectorAll('[data-line-key]').forEach(row=>{
      if(!cart.items.find(i=>i.key===row.dataset.lineKey)) row.remove();
    });
    // total
    const totalEl = document.querySelector('[data-cart-total]');
    if(totalEl) totalEl.textContent = fmt(cart.total_price);

    // progress
    const threshold = {{ section.settings.free_shipping_threshold | default: '0' }};
    if(threshold>0){
      const t = threshold*100, pct = Math.min(100, Math.floor(cart.total_price / t * 100));
      const bar = document.querySelector('.cart-progress__bar span');
      const msg = document.querySelector('.cart-progress__msg');
      if(bar) bar.style.width = pct + '%';
      if(msg){
        msg.innerHTML = cart.total_price < t
          ? `You're <strong>${fmt(t - cart.total_price)}</strong> away from your free standard shipping! <strong class="text-nowrap">${fmt(t)}</strong>`
          : `🎉 You've unlocked free standard shipping! <strong class="text-nowrap">${fmt(t)}</strong>`;
      }
    }
  }

  root.addEventListener('click', (e)=>{
    const minus = e.target.closest('[data-qty-minus]');
    const plus  = e.target.closest('[data-qty-plus]');
    const rem   = e.target.closest('[data-remove]');
    const save  = e.target.closest('[data-save]');
    if(minus || plus){
      const row = (minus||plus).closest('[data-line-key]');
      const qtyEl = row.querySelector('[data-line-qty]');
      const next = Math.max(0, parseInt(qtyEl.value,10) + (plus?1:-1));
      updateLine(row.dataset.lineKey, next);
    }
    if(rem){
      const row = rem.closest('[data-line-key]');
      updateLine(row.dataset.lineKey, 0);
    }
    if(save){
      // Optional: integrate with your wishlist bridge, then remove from cart
      try {
        window.WPWishlist?.add({ productGid: save.dataset.productGid, handle: save.dataset.handle });
      } catch(_){}
      const row = save.closest('[data-line-key]');
      updateLine(row.dataset.lineKey, 0);
    }
  });
})();
{% endjavascript %}