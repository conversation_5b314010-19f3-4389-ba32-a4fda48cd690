{% comment %}
  Product Recommendations Section

  - Shows Shopify recommendations or a chosen collection
  - Optional Recently viewed tab, built client-side
  - Horizontal scroll track with configurable visible count
  - Static on product templates (intended usage)
{% endcomment %}

{%- liquid
  assign heading = section.settings.heading
  assign products_to_show = section.settings.products_to_show | default: 4
  assign use_shopify_recs = section.settings.use_shopify_recs | default: true
  assign recs_collection = section.settings.recs_collection
  assign recs_intent = section.settings.recs_intent | default: 'related'

  assign use_curated_metafield = section.settings.use_curated_metafield | default: false
  assign curated_metafield = section.settings.curated_metafield | default: 'theme.complete_the_look'
  assign curated_ns = curated_metafield | split: '.' | first
  assign curated_key = curated_metafield | split: '.' | last
  assign curated = nil
  if use_curated_metafield
    assign curated = product.metafields[curated_ns][curated_key]
  endif

  assign show_recently = section.settings.recently_show | default: true
  assign recs_tab_label = section.settings.recs_tab_label | default: 'Recommended'
  assign recent_tab_label = section.settings.recent_tab_label | default: 'Recently viewed'

  assign visible_count = section.settings.visible_count | default: 4
  assign container_width = section.settings.container_width | default: 'inherit'
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="product-recs" data-section="{{ section.id }}">
  <div class="product-recs__inner{% if container_width != 'inherit' %} product-recs__inner--custom{% endif %} page-width"
       {% if container_width == 'narrow' %}style="--container-max: 90rem;"{% endif %}
       {% if container_width == 'wide' %}style="--container-max: 110rem;"{% endif %}
  >
    {% if heading %}
      <header class="product-recs__header">
        <h2 class="product-recs__title h2">{{ heading }}</h2>
      </header>
    {% endif %}

    <div class="product-recs__tabs" role="tablist" aria-label="{{ heading | default: 'Recommendations' }}">
      <button id="RecsTab-{{ section.id }}-0" class="product-recs__tab is-active" role="tab" type="button" aria-selected="true" tabindex="0" aria-controls="RecsPanel-{{ section.id }}-0">{{ recs_tab_label }}</button>
      {% if show_recently %}
        <button id="RecsTab-{{ section.id }}-1" class="product-recs__tab" role="tab" type="button" aria-selected="false" tabindex="-1" aria-controls="RecsPanel-{{ section.id }}-1">{{ recent_tab_label }}</button>
      {% endif %}
    </div>

    <div class="product-recs__panels" style="--visible: {{ visible_count }};" data-visible="{{ visible_count }}">
      <div id="RecsPanel-{{ section.id }}-0" class="product-recs__panel" role="tabpanel" aria-labelledby="RecsTab-{{ section.id }}-0">
        <div class="recs-nav" aria-hidden="true">
          <button class="recs-nav__btn recs-nav__btn--prev" type="button" aria-label="Previous" data-scroll-prev data-track="#RecsTrack-{{ section.id }}-0">‹</button>
          <button class="recs-nav__btn recs-nav__btn--next" type="button" aria-label="Next" data-scroll-next data-track="#RecsTrack-{{ section.id }}-0">›</button>
        </div>
        <div id="RecsTrack-{{ section.id }}-0" class="recs-track" data-recs-container>
          {% comment %} Curated metafield takes precedence when enabled and has values {% endcomment %}
          {% if use_curated_metafield and curated and curated.value and curated.value.size > 0 %}
            {% for p in curated.value limit: products_to_show %}
              <div class="recs-item">{%- render 'card-product', card_product: p, columns: 4, show_swatches: false -%}</div>
            {% endfor %}
          {% else %}
            {% if use_shopify_recs %}
              {% if recommendations.performed and recommendations.products_count > 0 %}
                {% for p in recommendations.products limit: products_to_show %}
                  <div class="recs-item">{%- render 'card-product', card_product: p, columns: 4, show_swatches: false -%}</div>
                {% endfor %}
              {% endif %}
            {% else %}
              {% if recs_collection %}
                {% for p in recs_collection.products limit: products_to_show %}
                  <div class="recs-item">{%- render 'card-product', card_product: p, columns: 4, show_swatches: false -%}</div>
                {% endfor %}
              {% else %}
                <p class="recs-empty">Select a collection or enable Shopify recommendations.</p>
              {% endif %}
            {% endif %}
          {% endif %}
        </div>
      </div>

      {% if show_recently %}
        <div id="RecsPanel-{{ section.id }}-1" class="product-recs__panel" role="tabpanel" aria-labelledby="RecsTab-{{ section.id }}-1" hidden>
          <div class="recs-nav" aria-hidden="true">
            <button class="recs-nav__btn recs-nav__btn--prev" type="button" aria-label="Previous" data-scroll-prev data-track="#RecsTrack-{{ section.id }}-1">‹</button>
            <button class="recs-nav__btn recs-nav__btn--next" type="button" aria-label="Next" data-scroll-next data-track="#RecsTrack-{{ section.id }}-1">›</button>
          </div>
          <div id="RecsTrack-{{ section.id }}-1" class="recs-track" data-recent-container>
            <p class="recs-empty" data-recent-empty hidden>No recently viewed products.</p>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</section>

<script>
  (() => {
    const root = document.getElementById('shopify-section-{{ section.id }}');
    if (!root) return;

    // Tabs behavior
    const tabs = root.querySelectorAll('.product-recs__tab');
    const panels = root.querySelectorAll('.product-recs__panel');
    const activate = (i, focus = false) => {
      tabs.forEach((t, idx) => {
        const active = idx === i;
        t.classList.toggle('is-active', active);
        t.setAttribute('aria-selected', active ? 'true' : 'false');
        t.tabIndex = active ? 0 : -1;
        panels[idx]?.toggleAttribute('hidden', !active);
      });
      if (focus) tabs[i]?.focus();
    };
    tabs.forEach((btn, i) => btn.addEventListener('click', () => activate(i, false)));
    // Keyboard navigation (ArrowLeft/Right, Home, End)
    const onKey = (ev) => {
      const keys = ['ArrowLeft','ArrowRight','Home','End'];
      if (!keys.includes(ev.key)) return;
      ev.preventDefault();
      const current = Array.from(tabs).findIndex(t => t.getAttribute('aria-selected') === 'true');
      let next = current;
      if (ev.key === 'ArrowRight') next = (current + 1) % tabs.length;
      else if (ev.key === 'ArrowLeft') next = (current - 1 + tabs.length) % tabs.length;
      else if (ev.key === 'Home') next = 0;
      else if (ev.key === 'End') next = tabs.length - 1;
      activate(next, true);
    };
    root.querySelector('.product-recs__tabs')?.addEventListener('keydown', onKey);

    // Shopify Recommendations fetch
    const useShopify = {{ use_shopify_recs | json }};
    const intent = {{ recs_intent | json }}; // 'related' or 'complementary'
    const recsContainer = root.querySelector('[data-recs-container]');
    const forceClient = intent !== 'related';
    const hasServerRecs = recsContainer && recsContainer.children.length > 0 && !forceClient;
    if (useShopify && recsContainer && !hasServerRecs) {
      const url = new URL({{ routes.product_recommendations_url | json }}, window.location.origin);
      url.searchParams.set('section_id', '{{ section.id }}');
      url.searchParams.set('product_id', '{{ product.id }}');
      url.searchParams.set('limit', '{{ products_to_show }}');
      if (intent) url.searchParams.set('intent', intent);
      fetch(url.toString())
        .then(r => r.text())
        .then(html => {
          const doc = new DOMParser().parseFromString(html, 'text/html');
          const from = doc.querySelector('#shopify-section-{{ section.id }} [data-recs-container]');
          if (from && recsContainer) {
            recsContainer.innerHTML = from.innerHTML;
            initNav('#RecsTrack-{{ section.id }}-0');
            initNav(root.querySelector('[data-track="#RecsTrack-{{ section.id }}-0"]')?.getAttribute('data-track'));
          }
        })
        .catch(() => {});
    }

    // Recently viewed: store and render simple cards from /products/{handle}.js
    const storeKey = 'recently_viewed_handles';
    const maxItems = {{ products_to_show | json }};
    try {
      const handle = {{ product.handle | json }};
      if (handle) {
        const raw = localStorage.getItem(storeKey);
        const list = raw ? JSON.parse(raw) : [];
        const filtered = list.filter(h => h !== handle);
        filtered.unshift(handle);
        localStorage.setItem(storeKey, JSON.stringify(filtered.slice(0, 20)));
      }
    } catch(e) {}

    const recentWrap = root.querySelector('[data-recent-container]');
    if (recentWrap) {
      const empty = recentWrap.querySelector('[data-recent-empty]');
      const renderRecent = async () => {
        try {
          const raw = localStorage.getItem(storeKey);
          if (!raw) { empty?.removeAttribute('hidden'); return; }
          const handles = JSON.parse(raw).filter(h => h !== {{ product.handle | json }}).slice(0, maxItems);
          if (!handles.length) { empty?.removeAttribute('hidden'); return; }
          const frag = document.createDocumentFragment();
          for (const h of handles) {
            try {
              const res = await fetch(`/products/${h}.js`);
              const data = await res.json();
              const item = document.createElement('div');
              item.className = 'recs-item recs-item--simple';
              const img = data.images?.[0];
              const v = Array.isArray(data.variants) && data.variants.length ? data.variants[0] : null;
              const price = v?.price ?? data.price;
              const compare = v?.compare_at_price ?? data.compare_at_price;
              const showPrice = {{ section.settings.recent_show_price | default: false | json }};
              const showCompare = {{ section.settings.recent_show_compare | default: true | json }};
              const fmt = new Intl.NumberFormat({{ request.locale.iso_code | json }}, { style: 'currency', currency: {{ localization.country.currency.iso_code | json }} });
              const priceHtml = showPrice && price ? `<span class="recs-simple__price">${fmt.format(price/100)}</span>` : '';
              const compareHtml = showPrice && showCompare && compare && compare > price ? `<span class="recs-simple__compare">${fmt.format(compare/100)}</span>` : '';
              item.innerHTML = `
                <a class="recs-simple" href="${data.url}" aria-label="${data.title}">
                  ${img ? `<img src="${img}" alt="" loading="lazy">` : ''}
                  <span class="recs-simple__title">${data.title}</span>
                  ${priceHtml || compareHtml ? `<span class="recs-simple__prices">${compareHtml}${priceHtml}</span>` : ''}
                </a>`;
              frag.appendChild(item);
            } catch(_) {}
          }
          recentWrap.innerHTML = '';
          recentWrap.appendChild(frag);
        } catch(_) {
          empty?.removeAttribute('hidden');
        }
      };
      renderRecent();
    }

    // Snap-scrolling arrows
    function initNavFromButton(btn) {
      const selector = btn?.getAttribute('data-track');
      if (!selector) return null;
      const track = root.querySelector(selector);
      if (!track) return null;
      const visible = parseInt(root.querySelector('.product-recs__panels')?.getAttribute('data-visible') || '4', 10);
      const scrollByAmount = () => Math.ceil(track.clientWidth / Math.max(visible, 1));
      const update = () => {
        const maxScroll = track.scrollWidth - track.clientWidth - 1;
        const prev = root.querySelector(`[data-scroll-prev][data-track="${selector}"]`);
        const next = root.querySelector(`[data-scroll-next][data-track="${selector}"]`);
        if (prev) prev.disabled = track.scrollLeft <= 0;
        if (next) next.disabled = track.scrollLeft >= maxScroll;
      };
      const prevBtn = root.querySelector(`[data-scroll-prev][data-track="${selector}"]`);
      const nextBtn = root.querySelector(`[data-scroll-next][data-track="${selector}"]`);
      prevBtn?.addEventListener('click', () => { track.scrollBy({ left: -scrollByAmount(), behavior: 'smooth' }); });
      nextBtn?.addEventListener('click', () => { track.scrollBy({ left: scrollByAmount(), behavior: 'smooth' }); });
      track.addEventListener('scroll', update, { passive: true });
      window.addEventListener('resize', update);
      update();
      return { update };
    }
    function initNav(selector) {
      const btn = root.querySelector(`[data-track="${selector}"]`);
      if (!btn) return;
      initNavFromButton(btn);
    }
    // Initialize nav for both panels if present
    root.querySelectorAll('.recs-nav__btn').forEach(initNavFromButton);
  })();
</script>

{% style %}
  /* Offsets */
  #shopify-section-{{ section.id }} {
    margin-top: {{ mobile_offset_top }}px;
    margin-bottom: {{ mobile_offset_bottom }}px;
  }
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} {
      margin-top: {{ desktop_offset_top }}px;
      margin-bottom: {{ desktop_offset_bottom }}px;
    }
  }

  .product-recs__inner.page-width {
    max-width: var(--container-max, var(--page-width));
    margin: 0 auto;
    padding-left: var(--page-margin, 1rem);
    padding-right: var(--page-margin, 1rem);
  }

  .product-recs__header { margin-bottom: 1rem; }
  .product-recs__title { margin: 0; }

  .product-recs__tabs { display: flex; gap: .75rem; align-items: center; }
  .product-recs__tab { appearance: none; background: transparent; border: 0; border-bottom: 2px solid transparent; padding: .5rem .75rem; cursor: pointer; font-weight: 600; }
  .product-recs__tab.is-active { border-color: currentColor; }

  .product-recs__panels { margin-top: 1rem; }
  .recs-track { display: grid; gap: 1rem; grid-auto-flow: column; grid-auto-columns: calc((100% - (var(--gap, 1rem) * (var(--visible, 4) - 1))) / var(--visible, 4)); overflow-x: auto; scroll-snap-type: x mandatory; }
  .recs-item { scroll-snap-align: start; }
  .product-recs__panel { position: relative; }
  .recs-nav { position: absolute; inset: 0; pointer-events: none; display: flex; justify-content: space-between; align-items: center; padding: 0 .25rem; }
  .recs-nav__btn { pointer-events: auto; border: 1px solid rgba(0,0,0,.2); background: rgba(255,255,255,.9); color: inherit; border-radius: 999px; width: 32px; height: 32px; line-height: 30px; text-align: center; font-size: 18px; cursor: pointer; }
  .recs-nav__btn[disabled] { opacity: .4; cursor: default; }

  /* Simple card for Recently viewed (when JSON used) */
  .recs-item--simple .recs-simple { display: grid; gap: .5rem; text-decoration: none; color: inherit; }
  .recs-item--simple img { width: 100%; height: auto; display: block; border-radius: 4px; }
  .recs-item--simple .recs-simple__title { font-size: .95rem; }
  .recs-item--simple .recs-simple__prices { display: inline-flex; gap: .5rem; align-items: baseline; }
  .recs-item--simple .recs-simple__compare { text-decoration: line-through; opacity: .7; }
  .recs-empty { opacity: .7; }
{% endstyle %}

{% schema %}
{
  "name": "Product recommendations",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "text", "id": "heading", "label": "Heading", "default": "You may also like" },
    { "type": "range", "id": "products_to_show", "label": "Products to show", "min": 2, "max": 12, "step": 1, "default": 4 },

    { "type": "header", "content": "Recommendations" },
    {
      "type": "select",
      "id": "recs_intent",
      "label": "Recommendation intent",
      "default": "related",
      "options": [
        { "value": "related", "label": "Related" },
        { "value": "complementary", "label": "Complementary (Complete the look)" }
      ],
      "info": "Choose 'Complementary' to power a Complete the Look section."
    },
    { "type": "text", "id": "recs_tab_label", "label": "Tab label", "default": "Recommended" },
    { "type": "checkbox", "id": "use_shopify_recs", "label": "Use Shopify recommendations", "default": true, "info": "Uses Shopify's product recommendations API" },
    { "type": "collection", "id": "recs_collection", "label": "Resource collection", "info": "Used when Shopify recommendations are disabled." },

    { "type": "header", "content": "Curated override (optional)" },
    { "type": "checkbox", "id": "use_curated_metafield", "label": "Use curated product metafield when available", "default": false, "info": "Create a product metafield of type 'List of products'." },
    { "type": "text", "id": "curated_metafield", "label": "Metafield (namespace.key)", "default": "theme.complete_the_look", "info": "Example: theme.complete_the_look (List of products)." },

    { "type": "header", "content": "Recently viewed" },
    { "type": "checkbox", "id": "recently_show", "label": "Show", "default": true },
    { "type": "text", "id": "recent_tab_label", "label": "Tab label", "default": "Recently viewed" },
    { "type": "checkbox", "id": "recent_show_price", "label": "Show price on recently viewed", "default": false },
    { "type": "checkbox", "id": "recent_show_compare", "label": "Show compare-at price when on sale", "default": true },

    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "visible_count", "label": "Layout (visible without scrolling)", "min": 2, "max": 6, "step": 1, "default": 4 },
    {
      "type": "select",
      "id": "container_width",
      "label": "Container width",
      "default": "inherit",
      "options": [
        { "value": "inherit", "label": "Inherit" },
        { "value": "narrow", "label": "Narrow" },
        { "value": "wide", "label": "Wide" }
      ]
    },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ]
}
{% endschema %}
