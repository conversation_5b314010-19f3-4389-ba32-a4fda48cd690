{%- liquid
  assign year = 'now' | date: '%Y'
  assign copyright_text = section.settings.copyright_text | replace: '%date%', year
  assign logo = section.settings.logo
  assign logo_width = section.settings.logo_width | default: 140
  assign quick_menu = section.settings.quick_links | default: section.settings.menu
-%}

<footer class="footer color-{{ section.settings.color_scheme }}" role="contentinfo">
  <div class="footer__inner page-width">
    <div class="footer__top">
      <div class="footer__brand">
        {% if logo %}
          <a class="footer__logo" href="{{ routes.root_url }}" aria-label="{{ shop.name | escape }}">
            {{ logo | image_url: width: 600 | image_tag: width: logo_width, alt: shop.name, loading: 'lazy' }}
          </a>
        {% endif %}
        {% if section.settings.text != blank %}
          <div class="footer__text rte">{{ section.settings.text }}</div>
        {% endif %}
      </div>

      <div class="footer__blocks">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'about' %}
              {% if block.settings.heading != blank or block.settings.text != blank %}
                <div class="footer-block footer-block--about" {{ block.shopify_attributes }}>
                  {% if block.settings.heading != blank %}
                    <h3 class="footer-block__heading">{{ block.settings.heading | escape }}</h3>
                  {% endif %}
                  {% if block.settings.text != blank %}
                    <div class="footer-block__text rte">{{ block.settings.text }}</div>
                  {% endif %}
                  {% if block.settings.show_social %}
                    <div class="footer__social">
                      {% assign socials = '' %}
                      {% if settings.social_facebook != blank %}{% assign socials = socials | append: '<a href="' | append: settings.social_facebook | append: '" target="_blank" rel="noopener">Facebook</a>' %}{% endif %}
                      {% if settings.social_instagram != blank %}{% if socials != '' %}{% assign socials = socials | append: ' ' %}{% endif %}{% assign socials = socials | append: '<a href="' | append: settings.social_instagram | append: '" target="_blank" rel="noopener">Instagram</a>' %}{% endif %}
                      {% if settings.social_tiktok != blank %}{% if socials != '' %}{% assign socials = socials | append: ' ' %}{% endif %}{% assign socials = socials | append: '<a href="' | append: settings.social_tiktok | append: '" target="_blank" rel="noopener">TikTok</a>' %}{% endif %}
                      {% if settings.social_pinterest != blank %}{% if socials != '' %}{% assign socials = socials | append: ' ' %}{% endif %}{% assign socials = socials | append: '<a href="' | append: settings.social_pinterest | append: '" target="_blank" rel="noopener">Pinterest</a>' %}{% endif %}
                      {% if settings.social_youtube != blank %}{% if socials != '' %}{% assign socials = socials | append: ' ' %}{% endif %}{% assign socials = socials | append: '<a href="' | append: settings.social_youtube | append: '" target="_blank" rel="noopener">YouTube</a>' %}{% endif %}
                      {% if settings.social_twitter != blank %}{% if socials != '' %}{% assign socials = socials | append: ' ' %}{% endif %}{% assign socials = socials | append: '<a href="' | append: settings.social_twitter | append: '" target="_blank" rel="noopener">Twitter</a>' %}{% endif %}
                      {% if settings.social_linkedin != blank %}{% if socials != '' %}{% assign socials = socials | append: ' ' %}{% endif %}{% assign socials = socials | append: '<a href="' | append: settings.social_linkedin | append: '" target="_blank" rel="noopener">LinkedIn</a>' %}{% endif %}
                      {% if socials != '' %}{{ socials }}{% endif %}
                    </div>
                  {% endif %}
                </div>
              {% endif %}
            {% when 'newsletter' %}
              <div class="footer-block footer-block--newsletter" {{ block.shopify_attributes }}>
                {% if block.settings.heading != blank %}
                  <h3 class="footer-block__heading">{{ block.settings.heading | escape }}</h3>
                {% endif %}
                {% if block.settings.show %}
                  {% form 'customer' %}
                    {% if form.posted_successfully? %}
                      <p class="footer__success" role="status">{{ block.settings.success_message | default: 'Thanks for subscribing!' }}</p>
                    {% endif %}
                    {% if form.errors %}
                      <div class="footer__errors" role="alert">{{ form.errors | default_errors }}</div>
                    {% endif %}
                    <div class="footer__newsletter">
                      <input type="hidden" name="contact[tags]" value="newsletter">
                      <input class="footer__input" type="email" name="contact[email]" placeholder="{{ block.settings.placeholder | default: 'Your email' }}" autocomplete="email" required>
                      <button class="footer__button footer__button--{{ block.settings.button_style | default: 'primary' }}" type="submit">{{ block.settings.button_label | default: 'Subscribe' }}</button>
                    </div>
                    {% if block.settings.enable_gdpr and block.settings.gdpr_text != blank %}
                      <p class="footer__gdpr">{{ block.settings.gdpr_text }}</p>
                    {% endif %}
                  {% endform %}
                {% endif %}
              </div>
            {% when 'menu' %}
              {% if block.settings.menu != blank %}
                <div class="footer-block footer-block--menu" {{ block.shopify_attributes }}>
                  {% if block.settings.heading != blank %}
                    <h3 class="footer-block__heading">{{ block.settings.heading | escape }}</h3>
                  {% endif %}
                  <ul class="footer__menu" role="list">
                    {% for link in block.settings.menu.links %}
                      <li><a href="{{ link.url }}">{{ link.title }}</a></li>
                    {% endfor %}
                  </ul>
                </div>
              {% endif %}
          {% endcase %}
        {% endfor %}
      </div>
    </div>

    <div class="footer__middle">
      {% if quick_menu != blank %}
        <nav class="footer__links" aria-label="Quick links">
          {% for link in quick_menu.links %}
            <a href="{{ link.url }}">{{ link.title }}</a>
          {% endfor %}
        </nav>
      {% endif %}

      <div class="footer__selectors">
        {% form 'localization' %}
          {% if section.settings.show_country_selector and localization.available_countries.size > 1 %}
            <div class="footer__selector">
              <label for="FooterCountry-{{ section.id }}">Country/Region</label>
              <select id="FooterCountry-{{ section.id }}" name="country_code">
                {% for country in localization.available_countries %}
                  <option value="{{ country.iso_code }}" {% if country.iso_code == localization.country.iso_code %}selected{% endif %}>
                    {{ country.name }} ({{ country.currency.iso_code }} {{ country.currency.symbol }})
                  </option>
                {% endfor %}
              </select>
            </div>
          {% endif %}
          {% if section.settings.show_language_selector and localization.available_languages.size > 1 %}
            <div class="footer__selector">
              <label for="FooterLanguage-{{ section.id }}">Language</label>
              <select id="FooterLanguage-{{ section.id }}" name="locale_code">
                {% for language in localization.available_languages %}
                  <option value="{{ language.iso_code }}" {% if language.iso_code == localization.language.iso_code %}selected{% endif %}>
                    {{ language.endonym_name | default: language.name }}
                  </option>
                {% endfor %}
              </select>
            </div>
          {% endif %}
        {% endform %}

        {% if section.settings.enable_follow_on_shop %}
          <div class="footer__shop-follow">
            <a href="https://shop.app/" rel="noopener" target="_blank">Follow on Shop</a>
          </div>
        {% endif %}
      </div>
    </div>

    <div class="footer__bottom">
      <div class="footer__copyright">
        {% if copyright_text != blank %}
          <small>&copy; {{ copyright_text }}</small>
        {% else %}
          <small>&copy; {{ year }} {{ shop.name }} — {{ powered_by_link }}</small>
        {% endif %}
      </div>
      <div class="footer__payment">
        {% if section.settings.show_payment_icons %}
          {% for type in shop.enabled_payment_types %}
            {{ type | payment_type_svg_tag }}
          {% endfor %}
        {% endif %}
      </div>
    </div>
  </div>

  {% if section.settings.show_back_to_top %}
    <button type="button" class="back-to-top" aria-label="Back to top">
      ↑
    </button>
  {% endif %}
</footer>

{% stylesheet %}
  #shopify-section-{{ section.id }} .footer {
    --footer-bg: {{ section.settings.bg | default: 'transparent' }};
    --footer-heading: {{ section.settings.heading_color | default: 'inherit' }};
    --footer-text: {{ section.settings.text_color | default: 'inherit' }};
    --footer-copyright: {{ section.settings.copyright_color | default: 'inherit' }};
    --footer-link: {{ section.settings.link_color | default: 'inherit' }};
    --footer-link-hover: {{ section.settings.link_hover_color | default: 'inherit' }};
    --footer-input-bg: {{ section.settings.input_bg | default: 'transparent' }};
    --footer-input-border: {{ section.settings.input_border | default: 'rgba(0,0,0,.2)' }};
    --footer-input-text: {{ section.settings.input_text | default: 'inherit' }};
    --footer-social: {{ section.settings.social_icon_color | default: 'inherit' }};
    --footer-social-bg: {{ section.settings.social_icon_bg | default: 'transparent' }};
    --footer-social-border: {{ section.settings.social_border_color | default: 'rgba(0,0,0,.2)' }};
    --footer-social-hover: {{ section.settings.social_icon_color_hover | default: 'inherit' }};
    --footer-social-bg-hover: {{ section.settings.social_icon_bg_hover | default: 'transparent' }};
    --footer-social-border-hover: {{ section.settings.social_border_color_hover | default: 'rgba(0,0,0,.4)' }};
    --footer-border: {{ section.settings.border_color | default: 'rgba(0,0,0,.1)' }};
    --btt-color: {{ section.settings.btt_color | default: 'inherit' }};
    --btt-bg: {{ section.settings.btt_bg | default: 'transparent' }};
    --btt-border: {{ section.settings.btt_border | default: 'rgba(0,0,0,.2)' }};
    --btt-color-hover: {{ section.settings.btt_color_hover | default: 'inherit' }};
    --btt-bg-hover: {{ section.settings.btt_bg_hover | default: 'transparent' }};
    --btt-border-hover: {{ section.settings.btt_border_hover | default: 'rgba(0,0,0,.4)' }};
  }
  .footer { background: var(--footer-bg); padding: 2rem 0; border-top: 1px solid var(--footer-border); }
  .footer__inner { max-width: var(--page-width); margin: 0 auto; }
  .footer__top { display: grid; gap: 2rem; grid-template-columns: 1fr; }
  .footer__brand .footer__logo img { display:block; height:auto; }
  .footer__text { color: var(--footer-text); margin-top: .75rem; }

  .footer__blocks { display: grid; gap: 1.5rem; grid-template-columns: repeat(2, minmax(0, 1fr)); }
  @media (min-width: 900px){ .footer__top { grid-template-columns: 1.2fr 2fr; } .footer__blocks { grid-template-columns: repeat(3, minmax(0, 1fr)); } }

  .footer-block__heading { color: var(--footer-heading); margin: 0 0 .5rem; font-weight: 600; }
  .footer-block__text { color: var(--footer-text); }
  .footer__menu { list-style: none; padding: 0; margin: 0; display: grid; gap: .35rem; }
  .footer__menu a { color: var(--footer-link); text-decoration: none; }
  .footer__menu a:hover { color: var(--footer-link-hover); }

  .footer__newsletter { display: flex; gap: .5rem; align-items: center; }
  .footer__input { flex: 1; min-width: 220px; padding: .6rem .7rem; border: 1px solid var(--footer-input-border); background: var(--footer-input-bg); color: var(--footer-input-text); border-radius: var(--style-border-radius-inputs, 4px); }
  .footer__button { padding: .6rem .9rem; cursor: pointer; }
  .footer__button--primary { background: var(--color-foreground); color: var(--color-background); border: 1px solid var(--color-foreground); }
  .footer__button--secondary { background: transparent; color: var(--color-foreground); border: 1px solid var(--color-foreground); }
  .footer__gdpr { font-size: .875rem; opacity: .8; margin-top: .5rem; }
  .footer__success { color: #065f46; }
  .footer__errors { color: #b91c1c; }

  .footer__social a { display: inline-flex; align-items:center; justify-content:center; width: 36px; height: 36px; margin-right: .35rem; color: var(--footer-social); background: var(--footer-social-bg); border: 1px solid var(--footer-social-border); border-radius: 50%; text-decoration: none; font-size: 12px; }
  .footer__social a:hover { color: var(--footer-social-hover); background: var(--footer-social-bg-hover); border-color: var(--footer-social-border-hover); }

  .footer__middle { display:flex; gap: 1rem; align-items:center; justify-content: space-between; border-top: 1px solid var(--footer-border); border-bottom: 1px solid var(--footer-border); padding: .75rem 0; margin-top: 1.5rem; }
  .footer__links { display:flex; gap: .75rem; flex-wrap: wrap; }
  .footer__links a { color: var(--footer-link); text-decoration: none; }
  .footer__links a:hover { color: var(--footer-link-hover); }
  .footer__selectors { display:flex; gap: .75rem; align-items:center; }
  .footer__selector label { display:none; }
  .footer__selector select { padding: .4rem .6rem; border: 1px solid var(--footer-input-border); background: var(--footer-input-bg); color: var(--footer-input-text); border-radius: var(--style-border-radius-inputs, 4px); }
  .footer__shop-follow a { color: var(--footer-link); text-decoration: none; }

  .footer__bottom { display:flex; gap: 1rem; align-items:center; justify-content: space-between; padding-top: .75rem; }
  .footer__copyright { color: var(--footer-copyright); }

  .footer__payment { display: flex; gap: .5rem; align-items:center; flex-wrap: wrap; }

  .back-to-top { position: fixed; right: 1rem; bottom: 1rem; width: 40px; height: 40px; border-radius: 50%; border: 1px solid var(--btt-border); background: var(--btt-bg); color: var(--btt-color); cursor: pointer; display: grid; place-items:center; }
  .back-to-top:hover { color: var(--btt-color-hover); background: var(--btt-bg-hover); border-color: var(--btt-border-hover); }
{% endstylesheet %}

<script>
  (function(){
    const btn = document.querySelector('#shopify-section-{{ section.id }} .back-to-top');
    if (!btn) return;
    btn.addEventListener('click', () => window.scrollTo({ top: 0, behavior: 'smooth' }));
  })();
  </script>

{% schema %}
{
  "name": "Footer",
  "settings": [
    { "type": "header", "content": "Logo" },
    { "type": "image_picker", "id": "logo", "label": "Logo image" },
    { "type": "range", "id": "logo_width", "label": "Logo width", "min": 60, "max": 300, "step": 4, "default": 140 },

    { "type": "header", "content": "General" },
    { "type": "richtext", "id": "text", "label": "Text" },
    { "type": "text", "id": "copyright_text", "label": "Copyright text", "info": "Use %date% for current year" },
    { "type": "link_list", "id": "quick_links", "label": "Quick links" },
    { "type": "checkbox", "id": "show_payment_icons", "label": "Show payment icons", "default": true },
    { "type": "checkbox", "id": "show_back_to_top", "label": "Show back to top button", "default": true },

    { "type": "header", "content": "Country/Region selector" },
    { "type": "checkbox", "id": "show_country_selector", "label": "Show country/region selector", "default": false },

    { "type": "header", "content": "Language selector" },
    { "type": "checkbox", "id": "show_language_selector", "label": "Show language selector", "default": false },

    { "type": "header", "content": "Follow on Shop" },
    { "type": "checkbox", "id": "enable_follow_on_shop", "label": "Enable Follow on Shop", "default": false },

    { "type": "header", "content": "Colors" },
    { "type": "color", "id": "bg", "label": "Section background" },
    { "type": "color", "id": "heading_color", "label": "Heading" },
    { "type": "color", "id": "text_color", "label": "Text" },
    { "type": "color", "id": "copyright_color", "label": "Copyright text" },
    { "type": "color", "id": "link_color", "label": "Link" },
    { "type": "color", "id": "link_hover_color", "label": "Link (hover)" },
    { "type": "color", "id": "input_bg", "label": "Input background" },
    { "type": "color", "id": "input_border", "label": "Input border" },
    { "type": "color", "id": "input_text", "label": "Input color" },
    { "type": "color", "id": "social_icon_color", "label": "Social icon color" },
    { "type": "color", "id": "social_icon_bg", "label": "Social icon background" },
    { "type": "color", "id": "social_border_color", "label": "Social border color" },
    { "type": "color", "id": "social_icon_color_hover", "label": "Social icon color (hover)" },
    { "type": "color", "id": "social_icon_bg_hover", "label": "Social icon background (hover)" },
    { "type": "color", "id": "social_border_color_hover", "label": "Social border color (hover)" },
    { "type": "color", "id": "border_color", "label": "Border color" },
    { "type": "color", "id": "btt_color", "label": "Back to top button color" },
    { "type": "color", "id": "btt_bg", "label": "Back to top button background" },
    { "type": "color", "id": "btt_border", "label": "Back to top border" },
    { "type": "color", "id": "btt_color_hover", "label": "Back to top button color (hover)" },
    { "type": "color", "id": "btt_bg_hover", "label": "Back to top button background (hover)" },
    { "type": "color", "id": "btt_border_hover", "label": "Back to top border (hover)" }
  ],
  "max_blocks": 4,
  "blocks": [
    {
      "type": "about",
      "name": "About",
      "limit": 1,
      "settings": [
        { "type": "text", "id": "heading", "label": "Heading" },
        { "type": "richtext", "id": "text", "label": "Text" },
        { "type": "checkbox", "id": "show_social", "label": "Show media links", "default": true }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "settings": [
        { "type": "checkbox", "id": "show", "label": "Show", "default": true },
        { "type": "text", "id": "heading", "label": "Heading", "default": "Subscribe to our newsletter" },
        { "type": "text", "id": "placeholder", "label": "Placeholder text", "default": "Your email" },
        { "type": "text", "id": "button_label", "label": "Button label", "default": "Subscribe" },
        { "type": "select", "id": "button_style", "label": "Button style", "default": "primary", "options": [ {"value":"primary","label":"Primary"}, {"value":"secondary","label":"Secondary"} ] },
        { "type": "text", "id": "success_message", "label": "Success message", "default": "Thanks for subscribing!" },
        { "type": "checkbox", "id": "enable_gdpr", "label": "Enable GDPR", "default": false },
        { "type": "richtext", "id": "gdpr_text", "label": "GDPR text" }
      ]
    },
    {
      "type": "menu",
      "name": "Menu",
      "limit": 3,
      "settings": [
        { "type": "text", "id": "heading", "label": "Heading" },
        { "type": "link_list", "id": "menu", "label": "Menu" }
      ]
    }
  ]
}
{% endschema %}
