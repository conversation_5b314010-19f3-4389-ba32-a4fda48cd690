{% comment %}
  Main Register Section
  - Static section for customers/register template
  - Mirrors main-login styling with images, button style, colors, and offsets
{% endcomment %}

{%- liquid
  assign big_image = section.settings.big_image
  assign small_image = section.settings.small_image
  assign button_style = section.settings.button_style | default: 'primary'

  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="register color-{{ section.settings.color_scheme | default: 'background-1' }}" data-section="{{ section.id }}">
  <div class="register__inner page-width">
    {% render 'breadcrumbs' %}

    <div class="register__grid">
      <div class="register__form">
        <h1 class="register__heading h2">{{ 'customers.register.title' | t | default: 'Create account' }}</h1>

        {% form 'create_customer' %}
          {% if form.errors %}
            <div class="register__errors" role="alert">{{ form.errors | default_errors }}</div>
          {% endif %}

          <div class="field">
            <label for="RegisterFirstName-{{ section.id }}">{{ 'customers.register.first_name' | t | default: 'First name' }}</label>
            <input type="text" name="customer[first_name]" id="RegisterFirstName-{{ section.id }}" autocomplete="given-name">
          </div>

          <div class="field">
            <label for="RegisterLastName-{{ section.id }}">{{ 'customers.register.last_name' | t | default: 'Last name' }}</label>
            <input type="text" name="customer[last_name]" id="RegisterLastName-{{ section.id }}" autocomplete="family-name">
          </div>

          <div class="field">
            <label for="RegisterEmail-{{ section.id }}">{{ 'customers.register.email' | t | default: 'Email' }}</label>
            <input type="email" name="customer[email]" id="RegisterEmail-{{ section.id }}" autocomplete="email" required>
          </div>

          <div class="field">
            <label for="RegisterPassword-{{ section.id }}">{{ 'customers.register.password' | t | default: 'Password' }}</label>
            <input type="password" name="customer[password]" id="RegisterPassword-{{ section.id }}" autocomplete="new-password" required>
          </div>

          <div class="register__actions">
            {% assign btn_class = 'button button--primary' %}
            {% if button_style == 'secondary' %}
              {% assign btn_class = 'button button--secondary' %}
            {% elsif button_style == 'outline' or button_style == 'secondary_outline' %}
              {% assign btn_class = 'button button--secondary button--outline' %}
            {% elsif button_style == 'primary_outline' %}
              {% assign btn_class = 'button button--primary button--outline' %}
            {% endif %}
            <button type="submit" class="{{ btn_class }}">{{ 'customers.register.submit' | t | default: 'Create' }}</button>
            <a class="register__link" href="{{ routes.account_login_url }}">{{ 'customers.login.title' | t | default: 'Login' }}</a>
          </div>
        {% endform %}
      </div>

      <div class="register__media">
        {% if big_image or small_image %}
          <div class="register__media-grid{% if big_image and small_image %} has-both{% endif %}">
            {% if big_image %}
              <div class="register__media-big">
                {{ big_image | image_url: width: 2000 | image_tag: widths: '800, 1200, 1600, 2000', sizes: '(min-width: 990px) 50vw, 100vw', loading: 'lazy', decoding: 'async', alt: '' }}
              </div>
            {% endif %}
            {% if small_image %}
              <div class="register__media-small">
                {{ small_image | image_url: width: 1200 | image_tag: widths: '600, 800, 1000, 1200', sizes: '(min-width: 990px) 25vw, 100vw', loading: 'lazy', decoding: 'async', alt: '' }}
              </div>
            {% endif %}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</section>

{% style %}
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; } }

  #shopify-section-{{ section.id }} .register {
    background: {{ section.settings.section_bg | default: 'transparent' | color_background }};
    color: {{ section.settings.text_color | default: 'inherit' }};
  }
  #shopify-section-{{ section.id }} .register__inner { padding: 1.5rem 1rem; max-width: var(--page-width); margin: 0 auto; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} .register__inner { padding: 2rem 1.25rem; } }

  #shopify-section-{{ section.id }} .register__grid { display: grid; gap: 1.25rem; align-items: start; }
  @media (min-width: 990px){ #shopify-section-{{ section.id }} .register__grid { grid-template-columns: 1fr 1fr; } }

  #shopify-section-{{ section.id }} .register__heading { margin: 0 0 .75rem; }
  #shopify-section-{{ section.id }} .register__actions { display: flex; gap: .75rem; align-items: center; flex-wrap: wrap; }
  #shopify-section-{{ section.id }} .register__link { text-decoration: underline; }
  #shopify-section-{{ section.id }} .register__errors { color: #c00; margin-bottom: .75rem; }

  #shopify-section-{{ section.id }} .register__media-grid { display: grid; gap: .75rem; }
  #shopify-section-{{ section.id }} .register__media-grid.has-both { grid-template-rows: 2fr 1fr; }
  #shopify-section-{{ section.id }} .register__media-big img,
  #shopify-section-{{ section.id }} .register__media-small img { width: 100%; height: auto; display: block; border-radius: 10px; }
{% endstyle %}

{% schema %}
{
  "name": "Main register",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "image_picker", "id": "big_image", "label": "Big image" },
    { "type": "image_picker", "id": "small_image", "label": "Small image" },
    { "type": "select", "id": "button_style", "label": "Button style", "default": "primary", "options": [
      { "value": "primary", "label": "Primary" },
      { "value": "secondary", "label": "Secondary" },
      { "value": "primary_outline", "label": "Primary with border" },
      { "value": "secondary_outline", "label": "Secondary with border" },
      { "value": "outline", "label": "Outline (secondary)" }
    ] },

    { "type": "header", "content": "Colors" },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "background-1" },
    { "type": "color_background", "id": "section_bg", "label": "Section background" },
    { "type": "color", "id": "text_color", "label": "Text" },

    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "presets": [ { "name": "Main register" } ]
}
{% endschema %}
