{% comment %}
  Main Login Section
  - Static section for customers/login template
  - Supports big/small images, button style, colors, and layout offsets
  - Includes breadcrumbs via 'breadcrumbs' snippet if present
{% endcomment %}

{%- liquid
  assign big_image = section.settings.big_image
  assign small_image = section.settings.small_image
  assign button_style = section.settings.button_style | default: 'primary'

  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="login color-{{ section.settings.color_scheme | default: 'background-1' }}" data-section="{{ section.id }}">
  <div class="login__inner page-width">
    {% render 'breadcrumbs' %}

    <div class="login__grid">
      <div class="login__form">
        <h1 class="login__heading h2">{{ 'customers.login.title' | t | default: 'Login' }}</h1>

        {% form 'customer_login' %}
          {% if form.errors %}
            <div class="login__errors" role="alert">{{ form.errors | default_errors }}</div>
          {% endif %}

          <div class="field">
            <label for="CustomerEmail">{{ 'customers.login.email' | t | default: 'Email' }}</label>
            <input type="email" name="customer[email]" id="CustomerEmail" autocomplete="email" required>
          </div>

          <div class="field">
            <label for="CustomerPassword">{{ 'customers.login.password' | t | default: 'Password' }}</label>
            <input type="password" name="customer[password]" id="CustomerPassword" autocomplete="current-password" required>
          </div>

          <div class="login__actions">
            {% assign btn_class = 'button button--primary' %}
            {% if button_style == 'secondary' %}
              {% assign btn_class = 'button button--secondary' %}
            {% elsif button_style == 'outline' or button_style == 'secondary_outline' %}
              {% assign btn_class = 'button button--secondary button--outline' %}
            {% elsif button_style == 'primary_outline' %}
              {% assign btn_class = 'button button--primary button--outline' %}
            {% endif %}
            <button type="submit" class="{{ btn_class }}">{{ 'customers.login.submit' | t | default: 'Sign in' }}</button>
            <a class="login__link" href="{{ routes.account_register_url }}">{{ 'customers.login.create_account' | t | default: 'Create account' }}</a>
            <a class="login__link" href="#recover" data-show-recover>{{ 'customers.login.forgot_password' | t | default: 'Forgot your password?' }}</a>
          </div>
        {% endform %}

        <div id="RecoverPasswordForm-{{ section.id }}" class="login__recover" hidden>
          <h2 class="h3">{{ 'customers.password.recover' | t | default: 'Recover password' }}</h2>
          {% form 'recover_customer_password' %}
            {% if form.posted_successfully? %}
              <p class="login__status" role="status">{{ 'customers.password.success' | t | default: 'We sent you an email with a link to reset your password.' }}</p>
            {% endif %}
            {% if form.errors %}
              <div class="login__errors" role="alert">{{ form.errors | default_errors }}</div>
            {% endif %}
            <div class="field">
              <label for="RecoverEmail-{{ section.id }}">{{ 'customers.password.email' | t | default: 'Email' }}</label>
              <input type="email" name="email" id="RecoverEmail-{{ section.id }}" autocomplete="email" required>
            </div>
            <div class="login__actions">
              {% assign btn_class = 'button button--primary' %}
              {% if button_style == 'secondary' %}
                {% assign btn_class = 'button button--secondary' %}
              {% elsif button_style == 'outline' or button_style == 'secondary_outline' %}
                {% assign btn_class = 'button button--secondary button--outline' %}
              {% elsif button_style == 'primary_outline' %}
                {% assign btn_class = 'button button--primary button--outline' %}
              {% endif %}
              <button type="submit" class="{{ btn_class }}">{{ 'customers.password.submit' | t | default: 'Send reset link' }}</button>
              <a href="{{ routes.account_login_url }}" class="login__link" data-hide-recover>{{ 'general.close' | t | default: 'Close' }}</a>
            </div>
          {% endform %}
        </div>
      </div>

      <div class="login__media">
        {% if big_image or small_image %}
          <div class="login__media-grid{% if big_image and small_image %} has-both{% endif %}">
            {% if big_image %}
              <div class="login__media-big">
                {{ big_image | image_url: width: 2000 | image_tag: widths: '800, 1200, 1600, 2000', sizes: '(min-width: 990px) 50vw, 100vw', loading: 'lazy', decoding: 'async', alt: '' }}
              </div>
            {% endif %}
            {% if small_image %}
              <div class="login__media-small">
                {{ small_image | image_url: width: 1200 | image_tag: widths: '600, 800, 1000, 1200', sizes: '(min-width: 990px) 25vw, 100vw', loading: 'lazy', decoding: 'async', alt: '' }}
              </div>
            {% endif %}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</section>

<script>
(() => {
  const root = document.getElementById('shopify-section-{{ section.id }}');
  if (!root) return;
  const showBtn = root.querySelector('[data-show-recover]');
  const hideBtn = root.querySelector('[data-hide-recover]');
  const loginForm = root.querySelector('form[action*="/account/login"]')?.closest('form');
  const recover = root.querySelector('#RecoverPasswordForm-{{ section.id }}');
  const show = () => { if (loginForm) loginForm.hidden = true; if (recover) recover.hidden = false; };
  const hide = () => { if (recover) recover.hidden = true; if (loginForm) loginForm.hidden = false; };
  showBtn?.addEventListener('click', (e)=>{ e.preventDefault(); show(); });
  hideBtn?.addEventListener('click', (e)=>{ e.preventDefault(); hide(); });
})();
</script>

{% style %}
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; } }

  #shopify-section-{{ section.id }} .login {
    background: {{ section.settings.section_bg | default: 'transparent' | color_background }};
    color: {{ section.settings.text_color | default: 'inherit' }};
  }
  #shopify-section-{{ section.id }} .login__inner { padding: 1.5rem 1rem; max-width: var(--page-width); margin: 0 auto; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} .login__inner { padding: 2rem 1.25rem; } }

  #shopify-section-{{ section.id }} .login__grid { display: grid; gap: 1.25rem; align-items: start; }
  @media (min-width: 990px){ #shopify-section-{{ section.id }} .login__grid { grid-template-columns: 1fr 1fr; } }

  #shopify-section-{{ section.id }} .login__heading { margin: 0 0 .75rem; }
  #shopify-section-{{ section.id }} .login__actions { display: flex; gap: .75rem; align-items: center; flex-wrap: wrap; }
  #shopify-section-{{ section.id }} .login__link { text-decoration: underline; }
  #shopify-section-{{ section.id }} .login__errors { color: #c00; margin-bottom: .75rem; }
  #shopify-section-{{ section.id }} .login__status { color: #0a0; margin-bottom: .5rem; }

  #shopify-section-{{ section.id }} .login__media-grid { display: grid; gap: .75rem; }
  #shopify-section-{{ section.id }} .login__media-grid.has-both { grid-template-rows: 2fr 1fr; }
  #shopify-section-{{ section.id }} .login__media-big img,
  #shopify-section-{{ section.id }} .login__media-small img { width: 100%; height: auto; display: block; border-radius: 10px; }
{% endstyle %}

{% schema %}
{
  "name": "Main login",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "image_picker", "id": "big_image", "label": "Big image" },
    { "type": "image_picker", "id": "small_image", "label": "Small image" },
    { "type": "select", "id": "button_style", "label": "Button style", "default": "primary", "options": [
      { "value": "primary", "label": "Primary" },
      { "value": "secondary", "label": "Secondary" },
      { "value": "primary_outline", "label": "Primary with border" },
      { "value": "secondary_outline", "label": "Secondary with border" },
      { "value": "outline", "label": "Outline (secondary)" }
    ] },

    { "type": "header", "content": "Colors" },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "background-1" },
    { "type": "color_background", "id": "section_bg", "label": "Section background" },
    { "type": "color", "id": "text_color", "label": "Text" },

    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "presets": [ { "name": "Main login" } ]
}
{% endschema %}
