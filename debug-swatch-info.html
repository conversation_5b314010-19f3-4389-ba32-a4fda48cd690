<!-- TEMPORARY DEBUG - Add this to the very top of card-product.liquid -->
{%- comment -%} === DEBUG INFO === {%- endcomment -%}
<div style="background: yellow; padding: 5px; font-size: 10px; margin: 5px;">
  <strong>DEBUG - Product: {{ product.title }}</strong><br>
  show_swatches: {{ show_swatches }}<br>
  Options count: {{ product.options.size }}<br>
  {% for option in product.options %}
    Option {{ forloop.index }}: "{{ option.name }}" ({{ option.values.size }} values)<br>
  {% endfor %}
  color_option found: {{ color_option.name | default: 'NONE' }}<br>
  Variants count: {{ product.variants.size }}
</div>
{%- comment -%} === END DEBUG === {%- endcomment -%}
