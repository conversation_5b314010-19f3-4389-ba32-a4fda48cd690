{% comment %}
  Password page content
  Renders optional heading, password message, and password form.
  Docs: https://shopify.dev/docs/storefronts/themes/architecture/templates/password
{% endcomment %}

{%- liquid
  assign heading = section.settings.heading
  if heading == blank
    assign heading = 'password.title' | t
  endif
-%}

{% style %}
  .password-content { padding: 2rem 0 3rem; }
  .password-content__inner { max-width: 34rem; margin: 0 auto; display: grid; gap: 1rem; }
  .password-content__title { text-align: center; font-size: clamp(1.25rem, 3vw, 1.75rem); font-weight: 600; }
  .password-content__message { text-align: center; color: rgba(var(--color-foreground), 0.8); }
  .password-form { display: grid; gap: 0.75rem; }
  .password-form label { font-weight: 600; }
  .password-form input[type="password"] { padding: 0.75rem; border: 1px solid rgba(0,0,0,.15); background: var(--color-background); color: currentcolor; }
  .password-form button { padding: 0.75rem 1rem; cursor: pointer; }
  .password-form .errors { color: #b00020; }

  /* Newsletter block */
  .password-newsletter { margin-top: 1.5rem; padding-top: 1.5rem; border-top: 1px solid rgba(0,0,0,.08); }
  .password-newsletter__title { text-align: center; font-weight: 600; font-size: clamp(1rem, 2.4vw, 1.25rem); }
  .password-newsletter__text { text-align: center; color: rgba(var(--color-foreground), 0.8); }
  .password-newsletter__form { display: grid; gap: 0.75rem; grid-template-columns: 1fr auto; align-items: center; }
  .password-newsletter__form input[type="email"] { padding: 0.75rem; border: 1px solid rgba(0,0,0,.15); min-width: 0; }
  .password-newsletter__form button { padding: 0.75rem 1rem; }
  .password-newsletter__status { text-align: center; }
  @media (max-width: 599px) {
    .password-newsletter__form { grid-template-columns: 1fr; }
  }
{% endstyle %}

<section class="password-content" data-section="{{ section.id }}">
  <div class="password-content__inner page-width">
    {% if section.settings.show_heading %}
      <h2 class="password-content__title">{{ heading }}</h2>
    {% endif %}

    {% if section.settings.show_message and shop.password_message %}
      <div class="password-content__message rte">{{ shop.password_message }}</div>
    {% endif %}

    {% form 'storefront_password', class: 'password-form' %}
      {% if form.errors %}
        <div class="errors">{{ form.errors | default_errors }}</div>
      {% endif %}

      <label for="password-input">{{ 'password.password' | t }}</label>
      <input type="password" name="password" id="password-input" placeholder="{{ 'password.password' | t }}">

      <button type="submit">{{ 'password.enter' | t }}</button>
    {% endform %}

    {%- if section.blocks.size > 0 -%}
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when 'newsletter' -%}
            <div class="password-newsletter" {{ block.shopify_attributes }}>
              {%- if block.settings.heading != blank -%}
                <h3 class="password-newsletter__title">{{ block.settings.heading | escape }}</h3>
              {%- endif -%}
              {%- if block.settings.text != blank -%}
                <div class="password-newsletter__text rte">{{ block.settings.text }}</div>
              {%- endif -%}

              {% form 'customer', class: 'password-newsletter__form' %}
                {% if form.posted_successfully? %}
                  <p class="password-newsletter__status" role="status">{{ block.settings.success_message | default: 'Thanks for subscribing!' }}</p>
                {% endif %}
                {% if form.errors %}
                  <div class="password-newsletter__status" role="alert">{{ form.errors | default_errors }}</div>
                {% endif %}
                <input type="hidden" name="contact[tags]" value="newsletter,password-page">
                <input type="email" name="contact[email]" placeholder="{{ block.settings.placeholder | default: 'Email address' }}" autocomplete="email" required>
                <button type="submit">{{ block.settings.button_label | default: 'Subscribe' }}</button>
                {%- if block.settings.enable_gdpr and block.settings.gdpr_text != blank -%}
                  <p class="password-newsletter__gdpr">{{ block.settings.gdpr_text }}</p>
                {%- endif -%}
              {% endform %}
            </div>
        {%- endcase -%}
      {%- endfor -%}
    {%- endif -%}
  </div>
  </section>

{% schema %}
{
  "name": "Password content",
  "settings": [
    { "type": "checkbox", "id": "show_heading", "label": "Show heading", "default": false },
    { "type": "text", "id": "heading", "label": "Heading (optional)", "default": "Opening soon" },
    { "type": "checkbox", "id": "show_message", "label": "Show password message", "default": true }
  ],
  "blocks": [
    {
      "type": "newsletter",
      "name": "Newsletter signup",
      "limit": 1,
      "settings": [
        { "type": "text", "id": "heading", "label": "Heading", "default": "Stay in the loop" },
        { "type": "richtext", "id": "text", "label": "Subtext", "default": "<p>We’ll let you know when we launch.</p>" },
        { "type": "text", "id": "placeholder", "label": "Email placeholder", "default": "Email address" },
        { "type": "text", "id": "button_label", "label": "Button label", "default": "Subscribe" },
        { "type": "text", "id": "success_message", "label": "Success message", "default": "Thanks for subscribing!" },
        { "type": "checkbox", "id": "enable_gdpr", "label": "Show GDPR note", "default": false },
        { "type": "richtext", "id": "gdpr_text", "label": "GDPR note", "default": "<p>You can unsubscribe at any time.</p>" }
      ]
    }
  ]
}
{% endschema %}
