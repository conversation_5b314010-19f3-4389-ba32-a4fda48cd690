/**
 * Product Card Hover Overlay System
 * Handles color swatch display on product card hover
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('🎆 DOM Content Loaded - starting initialization');
  initVariantDisplay();
});

let initializationCount = 0;

function initVariantDisplay() {
  initializationCount++;
  console.log('🚀 Initializing variant display... (attempt', initializationCount, ')');
  
  // Check if we're in hover overlay mode - look for multiple possible selectors
  const sections = document.querySelectorAll('[data-swatch-mode]');
  console.log('🔍 Found sections with swatch mode:', sections);
  
  let section = null;
  let isHoverMode = false;
  
  sections.forEach(s => {
    console.log('🔍 Section:', s, 'Mode:', s.dataset.swatchMode);
    if (s.dataset.swatchMode === 'hover_overlay') {
      section = s;
      isHoverMode = true;
    }
  });
  
  console.log('🔍 Mode check result:', {
    section: section,
    isHoverMode: isHoverMode,
    totalSections: sections.length
  });
  
  if (!isHoverMode) {
    console.log('❌ Not in hover mode, exiting');
    return;
  }
  
  // Find all product cards
  const cards = document.querySelectorAll('.card-wrapper');
  
  console.log('📍 Found', cards.length, 'cards');
  cards.forEach((card, i) => {
    console.log(`Card ${i}:`, card, 'Has swatches:', !!card.querySelector('.card__swatches'));
  });
  
  if (cards.length === 0) {
    console.log('❌ No cards found, exiting');
    return;
  }
  
  // Setup hover overlay for each card
  cards.forEach((card, index) => {
    // Check if overlay already exists to prevent duplicates (more robust check)
    const existingOverlay = card.querySelector('.card-hover-overlay');
    if (existingOverlay) {
      console.log('🔄 Overlay already exists for card', index, '- skipping');
      return;
    }
    
    // Find existing variant options in this card
    const swatchContainer = card.querySelector('.card__swatches');
    
    console.log('🔍 Debug for card', index, ':', {
      card: card,
      swatchContainer: swatchContainer,
      swatchContainerHTML: swatchContainer?.innerHTML,
      swatchContainerEmpty: !swatchContainer?.innerHTML.trim(),
      swatchContainerVisible: swatchContainer ? window.getComputedStyle(swatchContainer).display !== 'none' : false,
      allSwatchContainersInCard: card.querySelectorAll('.card__swatches').length,
      cardClasses: Array.from(card.classList)
    });
    
    // Create color swatches HTML
    let colorSwatchesHTML = '';
    if (swatchContainer && swatchContainer.innerHTML.trim()) {
      // Clone the swatches before they might get hidden
      const swatchClone = swatchContainer.cloneNode(true);
      colorSwatchesHTML = `<div class="overlay-colors">${swatchClone.outerHTML}</div>`;
      console.log('✅ Created color swatches HTML for card', index, 'Content length:', colorSwatchesHTML.length);
    } else {
      console.log('❌ No swatches found for card', index);
    }
    
    // Get product data from card
    const productHandle = card.dataset.productHandle;
    const productId = card.querySelector('[data-product-id]')?.dataset.productId;
    
    // Create size options from actual product variants
    let sizeOptionsHTML = '';
    const sizeContainer = card.querySelector('.size-selector, .variant-selector');
    if (sizeContainer) {
      const sizeButtons = sizeContainer.querySelectorAll('input[type="radio"], option');
      if (sizeButtons.length > 0) {
        sizeOptionsHTML = '<div class="overlay-sizes">';
        sizeButtons.forEach(button => {
          const value = button.value || button.textContent.trim();
          const variantId = button.dataset.variantId || '';
          sizeOptionsHTML += `<button class="size-btn" data-variant-id="${variantId}" data-size="${value}">${value}</button>`;
        });
        sizeOptionsHTML += '</div>';
      }
    } else {
      // Fallback to common sizes
      sizeOptionsHTML = `
        <div class="overlay-sizes">
          <button class="size-btn" data-size="XS">XS</button>
          <button class="size-btn" data-size="S">S</button>
          <button class="size-btn" data-size="M">M</button>
          <button class="size-btn" data-size="L">L</button>
          <button class="size-btn" data-size="XL">XL</button>
          <button class="size-btn" data-size="XXL">XXL</button>
        </div>
      `;
    }

    // Create overlay element
    const overlay = document.createElement('div');
    overlay.className = 'card-hover-overlay';
    overlay.innerHTML = `
      <div class="card-hover-overlay__content">
        ${colorSwatchesHTML}
        ${sizeOptionsHTML}
        <button class="overlay-quick-add" data-product-handle="${productHandle}" data-product-id="${productId}">QUICK ADD</button>
      </div>
    `;
    
    console.log('🎨 Created overlay for card', index, ':', {
      overlay: overlay,
      innerHTML: overlay.innerHTML,
      colorSwatchesHTML: colorSwatchesHTML,
      sizeOptionsHTML: sizeOptionsHTML,
      swatchesInOverlay: overlay.querySelectorAll('.swatch').length,
      overlayColors: overlay.querySelector('.overlay-colors'),
      cardSwatches: overlay.querySelector('.card__swatches')
    });
    
    // Find card media container and add overlay
    const mediaContainer = card.querySelector('.card__media, .media');
    console.log('🖼️ Media container for card', index, ':', mediaContainer);
    
    if (mediaContainer) {
      // Make sure media container is positioned relatively
      const computedStyle = window.getComputedStyle(mediaContainer);
      console.log('🔍 Media container position:', computedStyle.position);
      
      if (computedStyle.position === 'static') {
        mediaContainer.style.position = 'relative';
        console.log('🔧 Set media container position to relative');
      }
      
      mediaContainer.appendChild(overlay);
      console.log('✅ Overlay appended to media container for card', index);
      
      // Verify overlay is in DOM
      const overlayInDOM = mediaContainer.querySelector('.card-hover-overlay');
      console.log('🔍 Overlay in DOM:', !!overlayInDOM, overlayInDOM);
      
      // Fallback: Create swatches from product JSON if none exist
      if (!overlay.querySelector('.overlay-colors')) {
        console.log('🔄 No swatches found, creating fallback for card', index);
        
        // Try to infer handle if missing
        let handle = card.dataset.productHandle;
        if (!handle) {
          const href = card.querySelector('a[href*="/products/"]')?.getAttribute('href') || '';
          const m = href.match(/\/products\/([^/?#]+)/);
          if (m) handle = m[1];
        }
        
        if (handle) {
          console.log('🔍 Creating swatches for product handle:', handle);
          
          // Create placeholder container to keep layout stable
          const colorsWrap = document.createElement('div');
          colorsWrap.className = 'overlay-colors';
          const overlayContent = overlay.querySelector('.card-hover-overlay__content');
          const sizesContainer = overlay.querySelector('.overlay-sizes');
          overlayContent.insertBefore(colorsWrap, sizesContainer);
          
          // Build swatches from product JSON
          fetch(`/products/${handle}.js`)
            .then(r => r.json())
            .then(p => {
              console.log('📊 Product data received:', p);
              
              const colorIdx = p.options.findIndex(o => /color|colour/i.test(o));
              if (colorIdx === -1) {
                console.log('❌ No color option found for product');
                return;
              }
              
              console.log('🎨 Found color option at index:', colorIdx);
              
              const swatchContainer = document.createElement('div');
              swatchContainer.className = 'card__swatches';
              swatchContainer.setAttribute('role', 'group');
              swatchContainer.setAttribute('aria-label', 'Color options');
              
              const seen = new Set();
              p.variants.forEach(v => {
                const color = v.options[colorIdx];
                if (!color || seen.has(color)) return;
                seen.add(color);
                
                const btn = document.createElement('button');
                const cls = color.toLowerCase().replace(/[^a-z0-9]+/g, '-');
                btn.type = 'button';
                btn.className = `swatch swatch--${cls}`;
                btn.setAttribute('aria-label', color);
                btn.setAttribute('title', color);
                btn.dataset.variantId = v.id;
                btn.dataset.colorName = color;
                
                if (v.featured_image?.src) {
                  btn.dataset.src = v.featured_image.src;
                  btn.dataset.mediaId = v.featured_image.id;
                }
                
                const span = document.createElement('span');
                span.className = 'visually-hidden';
                span.textContent = color;
                btn.appendChild(span);
                
                swatchContainer.appendChild(btn);
              });
              
              colorsWrap.appendChild(swatchContainer);
              console.log('✅ Created', seen.size, 'swatches from product JSON');
              
              // Wire up the freshly inserted swatches
              setupVariantSelection(overlay, card);
            })
            .catch(err => {
              console.error('❌ Error fetching product data:', err);
            });
        } else {
          console.log('❌ No product handle found for fallback swatches');
        }
      }
      
      // Add hover event listeners
      card.addEventListener('mouseenter', function() {
        console.log('🐭 Mouse enter card', index);
        overlay.style.opacity = '1';
        overlay.style.pointerEvents = 'auto';
        console.log('🔆 Overlay shown for card', index, 'opacity:', overlay.style.opacity);
      });
      
      card.addEventListener('mouseleave', function() {
        console.log('🐭 Mouse leave card', index);
        overlay.style.opacity = '0';
        overlay.style.pointerEvents = 'none';
        console.log('🔅 Overlay hidden for card', index);
      });
      
      // Add variant selection functionality (only for cards with existing swatches)
      if (overlay.querySelector('.overlay-colors')) {
        setupVariantSelection(overlay, card);
      }
      
      console.log('✅ Complete setup for card', index, 'with overlay');
    } else {
      console.log('❌ No media container found for card', index);
    }
  });
  
  // Mark overlays as ready so CSS can hide original swatches
  if (section) {
    section.setAttribute('data-overlays-ready', 'true');
    console.log('✅ Marked section as overlays ready');
  }
  
  console.log('🎆 Overlay initialization complete!');
}

function setupVariantSelection(overlay, card) {
  let selectedColor = null;
  let selectedSize = null;
  let selectedVariantId = null;
  
  // Handle color swatch clicks
  const colorSwatches = overlay.querySelectorAll('.swatch');
  colorSwatches.forEach(swatch => {
    swatch.addEventListener('click', function() {
      // Remove active state from other swatches
      colorSwatches.forEach(s => s.classList.remove('swatch--active'));
      
      // Add active state to clicked swatch
      this.classList.add('swatch--active');
      
      selectedColor = this.dataset.colorName;
      // Don't set selectedVariantId here - we need to find it based on color + size combination
      
      console.log('🎨 Color selected:', selectedColor);
      
      // Update main card image if available
      const newImageSrc = this.dataset.src;
      const newImageSrcset = this.dataset.srcset;
      if (newImageSrc) {
        const mainImage = card.querySelector('.card-media__primary');
        if (mainImage) {
          mainImage.src = newImageSrc;
          if (newImageSrcset) {
            mainImage.srcset = newImageSrcset;
          }
        }
      }
      
      findCorrectVariantId();
      updateQuickAddButton();
    });
  });
  
  // Handle size button clicks
  const sizeButtons = overlay.querySelectorAll('.size-btn');
  sizeButtons.forEach(button => {
    button.addEventListener('click', function() {
      // Remove active state from other buttons
      sizeButtons.forEach(btn => btn.classList.remove('size-btn--active'));
      
      // Add active state to clicked button
      this.classList.add('size-btn--active');
      
      selectedSize = this.dataset.size;
      console.log('📏 Size selected:', selectedSize);
      
      // Don't use the variant ID from button - we need to find correct combination
      findCorrectVariantId();
      updateQuickAddButton();
    });
  });
  
  // Handle quick add button click
  const quickAddBtn = overlay.querySelector('.overlay-quick-add');
  quickAddBtn.addEventListener('click', function() {
    console.log('🔥 Quick add clicked!');
    console.log('Selected variant ID:', selectedVariantId);
    console.log('Selected color:', selectedColor);
    console.log('Selected size:', selectedSize);
    
    const productHandle = this.dataset.productHandle;
    const productId = this.dataset.productId;
    
    let variantToAdd = selectedVariantId;
    
    if (!variantToAdd) {
      // Try to find variant ID from the original product card
      const firstAvailableVariant = card.querySelector('[data-variant-id]:not([data-sold-out="true"])');
      if (firstAvailableVariant) {
        variantToAdd = firstAvailableVariant.dataset.variantId;
        console.log('🔍 Using fallback variant:', variantToAdd);
      }
    }
    
    if (!variantToAdd && productId) {
      // Last resort - use product ID to find first variant
      fetch(`/products/${productHandle}.js`)
        .then(response => response.json())
        .then(productData => {
          console.log('📋 Product data:', productData);
          if (productData.variants && productData.variants.length > 0) {
            const firstVariant = productData.variants.find(v => v.available) || productData.variants[0];
            addToCart(firstVariant.id);
          }
        })
        .catch(error => {
          console.error('❌ Error fetching product data:', error);
          showAddToCartError('Could not load product variants');
        });
    } else {
      addToCart(variantToAdd);
    }
  });
  
  function findCorrectVariantId() {
    if (!selectedColor && !selectedSize) {
      selectedVariantId = null;
      return;
    }
    
    // Get product handle for API call
    const productHandle = card.dataset.productHandle;
    if (!productHandle) {
      console.warn('⚠️ No product handle found');
      return;
    }
    
    // Fetch product data to find correct variant
    fetch(`/products/${productHandle}.js`)
      .then(response => response.json())
      .then(productData => {
        console.log('📋 Product variants:', productData.variants);
        
        // Find variant that matches selected color and size
        const matchingVariant = productData.variants.find(variant => {
          const colorMatch = !selectedColor || 
            variant.option1?.toLowerCase() === selectedColor.toLowerCase() ||
            variant.option2?.toLowerCase() === selectedColor.toLowerCase() ||
            variant.option3?.toLowerCase() === selectedColor.toLowerCase();
            
          const sizeMatch = !selectedSize || 
            variant.option1?.toLowerCase() === selectedSize.toLowerCase() ||
            variant.option2?.toLowerCase() === selectedSize.toLowerCase() ||
            variant.option3?.toLowerCase() === selectedSize.toLowerCase();
            
          return colorMatch && sizeMatch;
        });
        
        if (matchingVariant) {
          selectedVariantId = matchingVariant.id;
          console.log(`✅ Found matching variant: ${matchingVariant.title} (ID: ${selectedVariantId})`);
          console.log(`   Available: ${matchingVariant.available}`);
        } else {
          console.warn('⚠️ No matching variant found for:', { selectedColor, selectedSize });
          selectedVariantId = null;
        }
        
        updateQuickAddButton();
      })
      .catch(error => {
        console.error('❌ Error fetching product variants:', error);
      });
  }

  function updateQuickAddButton() {
    const quickAddBtn = overlay.querySelector('.overlay-quick-add');
    if (selectedColor && selectedSize && selectedVariantId) {
      quickAddBtn.textContent = `ADD ${selectedSize} ${selectedColor}`;
      quickAddBtn.disabled = false;
    } else if (selectedColor || selectedSize) {
      quickAddBtn.textContent = 'SELECT SIZE & COLOR';
      quickAddBtn.disabled = true;
    } else {
      quickAddBtn.textContent = 'QUICK ADD';
      quickAddBtn.disabled = false;
    }
  }
}

function addToCart(variantId) {
  console.log('🛒 Adding to cart:', variantId);
  
  if (!variantId) {
    console.error('❌ No variant ID provided');
    showAddToCartError('No variant selected');
    return;
  }
  
  const formData = {
    items: [{
      id: parseInt(variantId),
      quantity: 1
    }]
  };
  
  console.log('📦 Cart form data:', formData);
  
  fetch('/cart/add.js', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(formData)
  })
  .then(response => {
    console.log('📡 Cart API response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('✅ Cart API response data:', data);
    
    if (data.items && data.items.length > 0) {
      // Success - item was added
      // Update cart drawer or show success message
      if (window.cartDrawer && typeof window.cartDrawer.refresh === 'function') {
        window.cartDrawer.refresh();
      }
      
      // Dispatch cart update event
      document.dispatchEvent(new CustomEvent('cart:updated', { detail: data }));
      
      // Show success feedback
      showAddToCartSuccess();
    } else if (data.message) {
      // Error from Shopify API
      console.error('❌ Cart API error:', data.message);
      showAddToCartError(data.message);
    } else {
      console.error('❌ Unknown cart response:', data);
      showAddToCartError('Unknown error');
    }
  })
  .catch(error => {
    console.error('❌ Network error adding to cart:', error);
    showAddToCartError('Network error');
  });
}

function showAddToCartSuccess() {
  // Simple success notification - you can customize this
  const notification = document.createElement('div');
  notification.textContent = 'Added to cart!';
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 1rem 2rem;
    border-radius: 4px;
    z-index: 9999;
    font-weight: 600;
  `;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    notification.remove();
  }, 3000);
}

function showAddToCartError(message = 'Error adding to cart') {
  // Simple error notification
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc3545;
    color: white;
    padding: 1rem 2rem;
    border-radius: 4px;
    z-index: 9999;
    font-weight: 600;
    max-width: 300px;
  `;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    notification.remove();
  }, 5000);
}
