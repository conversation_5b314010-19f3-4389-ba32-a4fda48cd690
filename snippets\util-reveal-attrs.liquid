{%- comment -%}
  util-reveal-attrs
  Outputs data attributes for TEXT fade-up animation (separate from generic reveal).

  Usage:
    <h2 {% render 'util-reveal-attrs', enabled: true, delay: '100ms' %}>Title</h2>

  Params (all optional):
    enabled: boolean (defaults to settings.enable_global_text_reveal)
    duration: string e.g. '1200ms' (defaults from settings.text_reveal_speed)
    delay: string e.g. '80ms'
    ease: string e.g. 'cubic-bezier(...)'
    translate: string e.g. '12px' (defaults from settings.text_reveal_translate)
{%- endcomment -%}
{%- liquid
  assign enabled_param = enabled | default: nil
  assign is_enabled = enabled_param
  if is_enabled == nil
    assign is_enabled = settings.enable_global_text_reveal
  endif

  if is_enabled
    assign duration_val = duration
    if duration_val == blank
      case settings.text_reveal_speed
        when 'fast'   
          assign duration_val = '600ms'
        when 'normal' 
          assign duration_val = '900ms'
        else          
          assign duration_val = '1200ms'
      endcase
    endif

    assign translate_val = translate
    if translate_val == blank
      case settings.text_reveal_translate
        when 'short'  
          assign translate_val = '6px'
        when 'long'   
          assign translate_val = '20px'
        else          
          assign translate_val = '12px'
      endcase
    endif
  endif
-%}
{%- if is_enabled -%}
data-text-reveal
data-text-reveal-duration="{{ duration_val }}"
{%- if delay != blank -%} data-text-reveal-delay="{{ delay }}"{%- endif -%}
{%- if ease != blank -%} data-text-reveal-ease="{{ ease }}"{%- endif -%}
data-text-reveal-translate="{{ translate_val }}"
{%- endif -%}
