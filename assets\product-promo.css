/* Product Promo Popup */
.ppp__backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,.35);
  opacity: 0;
  pointer-events: none;
  transition: opacity .2s ease;
}

.ppp[open] .ppp__backdrop { opacity: 1; pointer-events: auto; }

.ppp__dialog {
  position: fixed;
  max-width: min(420px, 90vw);
  background: var(--ppp-bg, rgb(var(--color-background)));
  color: var(--ppp-fg, rgb(var(--color-foreground)));
  border-radius: 12px;
  border: 1px solid rgba(0,0,0,.08);
  box-shadow: none;
  transform: translateY(1rem);
  opacity: 0;
  transition: transform .25s ease, opacity .25s ease;
  z-index: 60;
}

/* Position modifiers */
.ppp__dialog--pos-bottom_right { bottom: 1rem; right: 1rem; left: auto; top: auto; }
.ppp__dialog--pos-bottom_left { bottom: 1rem; left: 1rem; right: auto; top: auto; }
.ppp__dialog--pos-top_right { top: 1rem; right: 1rem; left: auto; bottom: auto; }
.ppp__dialog--pos-top_left { top: 1rem; left: 1rem; right: auto; bottom: auto; }

.ppp__dialog--shadow { box-shadow: 0 10px 30px rgba(0,0,0,.18); }

.ppp[open] .ppp__dialog { opacity: 1; transform: translateY(0); }

.ppp__close {
  position: absolute;
  top: .25rem;
  right: .5rem;
  background: transparent;
  border: none;
  color: var(--ppp-close, currentColor);
  font-size: 1.25rem;
  line-height: 1;
  cursor: pointer;
}

.ppp__content { padding: 1rem 1rem 1.25rem; }
.ppp__content--text { text-align: left; }
.ppp__icon { margin-bottom: .5rem; }
.ppp__icon img { width: 48px; height: auto; }
.ppp__heading { margin: .25rem 0 .5rem; font-size: 1.125rem; }
.ppp__text { opacity: .85; }
.ppp__actions { margin-top: .75rem; }
.ppp__btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: .5rem;
  padding: .6rem 1rem;
  border-radius: 999px;
  background: var(--ppp-btn-bg, rgb(var(--color-foreground)));
  color: var(--ppp-btn-fg, rgb(var(--color-background)));
  border: 1px solid var(--ppp-btn-bd, transparent);
  cursor: pointer;
}
.ppp__btn:hover {
  background: var(--ppp-btn-bg-hover, var(--ppp-btn-bg, rgb(var(--color-foreground))));
  color: var(--ppp-btn-fg-hover, var(--ppp-btn-fg, rgb(var(--color-background))));
  border-color: var(--ppp-btn-bd-hover, var(--ppp-btn-bd, transparent));
}

.ppp__product { display: grid; grid-template-columns: 96px 1fr; gap: .75rem; align-items: center; }
.ppp__product-media img { width: 96px; height: 96px; object-fit: cover; border-radius: 8px; }
.ppp__product-title { margin: 0 0 .25rem; font-size: 1rem; }
.ppp__product-desc { opacity: .85; font-size: .9rem; }

@media (max-width: 620px) {
  .ppp__dialog { left: .75rem; right: .75rem; }
  .ppp__dialog--pos-bottom_right,
  .ppp__dialog--pos-bottom_left { bottom: .75rem; top: auto; }
  .ppp__dialog--pos-top_right,
  .ppp__dialog--pos-top_left { top: .75rem; bottom: auto; }
}
