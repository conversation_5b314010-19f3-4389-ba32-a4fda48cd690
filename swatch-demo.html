<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Swatch Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 2rem;
            background: #f8f8f8;
        }
        
        .demo-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin: 2rem 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 300px;
        }
        
        .demo-image {
            width: 100%;
            height: 200px;
            background: #e0e0e0;
            border-radius: 4px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        
        .demo-title {
            font-size: 1rem;
            margin: 0 0 0.5rem 0;
            font-weight: normal;
        }
        
        .demo-price {
            font-size: 1rem;
            color: #666;
            margin: 0 0 0.75rem 0;
        }
        
        .card__swatches {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 0.75rem 0 0 0;
            padding: 0;
            justify-content: flex-start;
            align-items: center;
        }
        
        .swatch {
            display: block;
            width: 0.875rem;
            height: 0.875rem;
            min-width: 0.875rem;
            min-height: 0.875rem;
            border-radius: 50%;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            background-color: #ccc;
            flex-shrink: 0;
            aspect-ratio: 1 / 1;
            box-sizing: border-box;
        }
        
        .swatch:hover {
            transform: scale(1.1);
            box-shadow: 0 0 0 2px white, 0 0 0 4px #333;
        }
        
        .swatch-more {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 0.875rem;
            height: 0.875rem;
            min-width: 0.875rem;
            min-height: 0.875rem;
            border-radius: 50%;
            background-color: rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.2);
            font-size: 0.625rem;
            font-weight: 500;
            color: rgba(0,0,0,0.8);
            line-height: 1;
        }
        
        /* Color mappings */
        .swatch--apricot-orange { background-color: #ff8040 !important; }
        .swatch--black { background-color: #000000 !important; }
        .swatch--pink { background-color: #f8bbd9 !important; }
        .swatch--orange { background-color: #ff8040 !important; }
        .swatch--purple { background-color: #9333ea !important; }
        .swatch--white { background-color: #ffffff !important; border: 2px solid #e0e0e0 !important; }
    </style>
</head>
<body>
    <h1>Color Swatch Demo</h1>
    <p>This demonstrates how the color swatches will appear on your product cards:</p>
    
    <div class="demo-card">
        <div class="demo-image">Product Image</div>
        <h3 class="demo-title">Effortless Seamless Shorts</h3>
        <div class="demo-price">$70.00 CAD</div>
        <div class="card__swatches">
            <button class="swatch swatch--apricot-orange" title="Apricot Orange"></button>
            <button class="swatch swatch--black" title="Black"></button>
            <button class="swatch swatch--pink" title="Pink"></button>
            <button class="swatch swatch--orange" title="Orange"></button>
            <button class="swatch swatch--purple" title="Purple"></button>
            <div class="swatch-more">+2</div>
        </div>
    </div>
    
    <div class="demo-card">
        <div class="demo-image">Product Image</div>
        <h3 class="demo-title">Another Product Example</h3>
        <div class="demo-price">$55.00 CAD</div>
        <div class="card__swatches">
            <button class="swatch swatch--black" title="Black"></button>
            <button class="swatch swatch--white" title="White"></button>
            <button class="swatch swatch--purple" title="Purple"></button>
        </div>
    </div>
    
    <h2>Features:</h2>
    <ul>
        <li>✅ Color swatches appear by default on product cards</li>
        <li>✅ Hover effects for interactive feedback</li>
        <li>✅ Proper color mapping for common color names</li>
        <li>✅ "+N" indicator for additional colors</li>
        <li>✅ Responsive design that works on mobile</li>
        <li>✅ Hover preview changes product image (when implemented)</li>
    </ul>
</body>
</html>
