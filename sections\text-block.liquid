{%- comment -%}
  Text Block Section

  Purpose: Simple editable text section which allows merchants to add
  a heading, subheading and rich text paragraph, and choose background
  and text colors. Includes alignment and padding options.
{%- endcomment -%}

{% style %}
  .text-block {
    padding: 3rem 0;
  }

  .text-block__inner {
    padding: 2rem 1rem;
  }

  .text-block__container {
    max-width: var(--page-width);
    margin: 0 auto;
    text-align: left;
  }

  .text-block__heading {
    margin: 0 0 0.5rem;
    font-size: clamp(1.5rem, 3.5vw, 2.5rem);
    line-height: 1.1;
  }

  .text-block__subheading {
    margin: 0 0 1rem;
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: rgba(var(--color-foreground), 0.8);
  }

  .text-block__content {
    font-size: 1rem;
    line-height: 1.6;
    margin-top: 0.5rem;
  }

  /* Alignment helpers */
  .text-block--align-center .text-block__container { text-align: center; }
  .text-block--align-right .text-block__container { text-align: right; }

  @media (max-width: 749px) {
    .text-block { padding: 2rem 0; }
    .text-block__inner { padding: 1.25rem 0.5rem; }
  }
{% endstyle %}

<section class="text-block section {% if section.settings.text_alignment == 'center' %}text-block--align-center{% elsif section.settings.text_alignment == 'right' %}text-block--align-right{% endif %}" data-section="{{ section.id }}">
  <div class="text-block__inner" style="background-color: {{ section.settings.background_color | escape }}; color: {{ section.settings.text_color | escape }};">
    <div class="text-block__container page-width">
      {%- if section.settings.show_heading and section.settings.heading != blank -%}
        <h2 class="text-block__heading" {% render 'util-reveal-attrs', enabled: section.settings.enable_text_reveal, delay: '0ms' %}>{{ section.settings.heading | escape }}</h2>
      {%- endif -%}

      {%- if section.settings.show_subheading and section.settings.subheading != blank -%}
        <h3 class="text-block__subheading" {% render 'util-reveal-attrs', enabled: section.settings.enable_text_reveal, delay: '100ms' %}>{{ section.settings.subheading | escape }}</h3>
      {%- endif -%}

      {%- if section.settings.content != blank -%}
        <div class="text-block__content rte" {% render 'util-reveal-attrs', enabled: section.settings.enable_text_reveal, delay: '200ms' %}>{{ section.settings.content }}</div>
      {%- endif -%}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Text block",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "checkbox",
      "id": "show_heading",
      "label": "Show heading",
      "default": true
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
  "default": "Your heading here",
  "info": "Main heading text"
    },
    {
      "type": "checkbox",
      "id": "show_subheading",
      "label": "Show subheading",
      "default": true
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading",
  "default": "Your subheading here",
  "info": "Smaller subheading or eyebrow"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Paragraph content",
  "default": "<p>Add paragraph text or rich content</p>",
  "info": "Add paragraph text or rich content"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "default": "left",
      "options": [
        { "value": "left", "label": "Left" },
        { "value": "center", "label": "Center" },
        { "value": "right", "label": "Right" }
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#111111"
    },
    {
      "type": "range",
      "id": "vertical_padding",
      "label": "Vertical padding",
      "min": 0,
      "max": 8,
      "step": 1,
      "default": 3,
      "info": "Padding (rem) above and below the block."
    },
    {
      "type": "header",
      "content": "Animations"
    },
    {
      "type": "checkbox",
      "id": "enable_text_reveal",
      "label": "Enable text reveal (fade-up)",
      "default": false,
      "info": "Uses global defaults under Theme settings → Effects."
    }
  ],
  "presets": [
    {
      "name": "Text block",
      "category": "Text"
    }
  ]
}
{% endschema %}
