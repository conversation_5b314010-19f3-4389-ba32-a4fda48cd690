{% comment %}
  This section is used in the blog template to render the blog page listing all
  articles within a blog.

  https://shopify.dev/docs/storefronts/themes/architecture/templates/blog
{% endcomment %}

<h1>{{ blog.title }}</h1>

{% paginate blog.articles by 5 %}
  {% for article in blog.articles %}
    <div>
      {% if article.image %}
        <div class="blog-card__media{% if section.settings.enable_image_zoom %} zoom-on-scroll{% endif %}"{% if section.settings.enable_image_zoom %} data-zoom-on-scroll{% endif %}{% if section.settings.enable_image_reveal %} data-reveal-on-scroll{% endif %}>
          {{ article.image | image_url: width: 1200 | image_tag: class: 'blog-card__image', alt: article.title, loading: 'lazy', decoding: 'async', widths: '600, 900, 1200', sizes: '(min-width: 990px) 50vw, 100vw' }}
        </div>
      {% endif %}
      <h2>
        {{ article.title | link_to: article.url }}
      </h2>
      {% assign date = article.published_at | time_tag: format: 'date' %}
      <p>{{ 'blog.article_metadata_html' | t: date: date, author: article.author }}</p>
      <p>{{ article.excerpt }}</p>
    </div>
  {% endfor %}

  {%- if paginate.pages > 1 -%}
    {{- paginate | default_pagination -}}
  {%- endif -%}
{% endpaginate %}

{% schema %}
{
  "name": "t:general.blog",
  "settings": [
    { "type": "checkbox", "id": "enable_image_zoom", "label": "Enable image zoom-on-scroll", "default": true },
    { "type": "checkbox", "id": "enable_image_reveal", "label": "Enable image reveal-on-scroll", "default": true }
  ]
}
{% endschema %}
