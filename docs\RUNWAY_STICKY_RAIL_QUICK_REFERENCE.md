# Runway Sticky Rail - Quick Reference

## TL;DR

The sticky rail uses **manual position toggling** (absolute → fixed → released) with scroll-based calculations. The rail height matches the media gallery, inner content scrolls, and the release point is computed dynamically.

## Three States

```
┌─────────────────────────────────────────────────────────┐
│ 1. NATURAL (position: absolute; top: 0)                │
│    - Before scrolling                                   │
│    - Rail at top of section                             │
└─────────────────────────────────────────────────────────┘
                        ↓ scroll down
┌─────────────────────────────────────────────────────────┐
│ 2. FIXED (position: fixed; top: var(--header-h))       │
│    - While scrolling through content                    │
│    - Rail sticks under header                           │
│    - .is-fixed class added                              │
└─────────────────────────────────────────────────────────┘
                        ↓ scroll to anchor
┌─────────────────────────────────────────────────────────┐
│ 3. RELEASED (position: absolute; top: var(--release-top))│
│    - At release anchor                                  │
│    - Rail pinned to computed position                   │
│    - .is-released class added                           │
└─────────────────────────────────────────────────────────┘
```

## Key CSS

```css
/* Rail container: matches media height */
.product__info {
  height: var(--runway-slider-h);
}

/* Inner content: scrollable */
.product-info {
  height: 100%;
  overflow: auto;
}

/* States */
.product__info.is-fixed { position: fixed; top: var(--header-h); }
.product__info.is-released { position: absolute; top: var(--release-top); }
```

## Key JavaScript

```javascript
// Find where to release
const anchor = findReleaseAnchor(); // [data-rail-end] or heuristic

// Compute release position
const releaseTop = anchorBottom - sectionTop - railHeight;

// Toggle states on scroll
if (scrollY >= fixedStart && scrollY < fixedEnd) {
  rail.classList.add('is-fixed');
} else if (scrollY >= fixedEnd) {
  rail.classList.add('is-released');
}
```

## Merchant Control

### Explicit End Marker

```liquid
<div data-rail-end><!-- Rail releases here --></div>
```

### Mate Marker

```liquid
<div data-rail-mate><!-- Include in sticky track --></div>
```

### Auto-Detection

Automatically finds: `.product-recs`, `.shop-the-look`, `.keep-exploring`

### Inner Content Preference

The release calculation prefers the inner content of the next section (to avoid counting outer section padding). For example, when a recommendations section is present, the rail releases against `.product-recs__inner` rather than the outer `.shopify-section` wrapper. You may also add `[data-rail-bottom]` to any inner element to explicitly define the element whose bottom should be used.

## Release Formula

```
releaseTop = (anchor bottom) - (section top) - (rail height)
```

Where:
- **anchor bottom**: Bottom edge of release anchor element
- **section top**: Top edge of product section
- **rail height**: `--runway-slider-h` (viewport - header - fold)

## Common Issues

### Panel doesn't release

**Check:**
1. Is there a release anchor? (console: `document.querySelector('[data-rail-end]')`)
2. Is `--release-top` computed? (console: `getComputedStyle(section).getPropertyValue('--release-top')`)
3. Is the anchor tall enough? (anchor bottom must be > section top + rail height)

### Content cut off at bottom

**Check:**
1. Is `.product-info` scrollable? (should have `overflow: auto`)
2. Is rail height correct? (should match `--runway-slider-h`)
3. Is inner content taller than container? (console: `inner.scrollHeight > inner.offsetHeight`)

### Panel jumps or flickers

**Check:**
1. Is `requestAnimationFrame` being used? (should be)
2. Are event listeners passive? (should be `{ passive: true }`)
3. Is state caching working? (should only update when mode changes)

### Panel shifts laterally when transitioning states

**Fixed:** The panel now uses a pixel-measured `--rail-right` variable (set by JavaScript) for all three states. This eliminates sub-pixel rounding differences between absolute/fixed positioning that caused the lateral "jump". See `docs/RUNWAY_PANEL_LATERAL_SHIFT_FIX.md` for details.

## Performance

- ✅ `requestAnimationFrame` for smooth updates
- ✅ Passive event listeners
- ✅ State caching (only updates when mode changes)
- ✅ MutationObserver for dynamic content
- ✅ Efficient anchor detection (stops at first match)

## Files

- **CSS**: `sections/product.liquid` lines ~205-293
- **JS**: `sections/product.liquid` lines ~3345-3464
- **Docs**:
  - `docs/RUNWAY_STICKY_RAIL_IMPLEMENTATION.md` (full implementation details)
  - `docs/RUNWAY_PANEL_LATERAL_SHIFT_FIX.md` (lateral shift fix)

## Testing Commands

```javascript
// Check current state
const rail = document.querySelector('.product__info');
console.log('Fixed:', rail.classList.contains('is-fixed'));
console.log('Released:', rail.classList.contains('is-released'));

// Check computed values
const section = document.querySelector('.product');
console.log('Release top:', getComputedStyle(section).getPropertyValue('--release-top'));
console.log('Rail height:', rail.offsetHeight);

// Check anchor
const findAnchor = () => {
  const marked = document.querySelector('[data-rail-end]');
  if (marked) return marked;
  const section = document.querySelector('.product').closest('.shopify-section');
  return section?.nextElementSibling || section;
};
console.log('Anchor:', findAnchor()?.className);

// Force update
window.dispatchEvent(new Event('resize'));
```

## Why This Approach?

**Pros:**
- Works with existing layout system
- Full control over release behavior
- No HTML restructuring needed
- Supports floating right-side panel

**Cons:**
- More JavaScript than native CSS sticky
- Manual state management
- Requires scroll calculations

**Alternative (not used):**
- Native CSS `position: sticky` would be simpler
- But requires HTML restructuring (scope container)
- Harder to position to the right side
- Less control over exact release point

## Next Steps

1. Test with actual product content
2. Verify release behavior with different section combinations
3. Test with dynamic content (async recommendations)
4. Add `[data-rail-end]` or `[data-rail-mate]` markers if needed
5. Adjust scrollbar styling to match brand

---

For full implementation details, see `docs/RUNWAY_STICKY_RAIL_IMPLEMENTATION.md`
