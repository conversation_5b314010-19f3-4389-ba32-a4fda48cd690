{% comment %}
  Best Sellers Section

  - Displays a list of top products with optional numbers (desktop only)
  - Each item can override the displayed title and image
  - Colors are configurable for background, text, borders, and active state
  - Supports container width and layout offsets
{% endcomment %}

{%- liquid
  assign heading = section.settings.heading
  assign show_numeric = section.settings.show_numeric | default: true

  assign container_width = section.settings.container_width | default: 'inherit'
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0

  assign color_bg = section.settings.color_bg | default: '#ffffff'
  assign color_text = section.settings.color_text | default: '#111111'
  assign color_border = section.settings.color_border | default: 'rgba(0,0,0,.15)'
  assign color_active_text = section.settings.color_active_text | default: color_text
  assign color_active_border = section.settings.color_active_border | default: color_border
-%}

<section class="best-sellers" data-section="{{ section.id }}" data-show-numeric="{{ show_numeric }}">
  <div class="best-sellers__inner{% if container_width != 'inherit' %} best-sellers__inner--custom{% endif %} page-width"
       {% if container_width == 'narrow' %}style="--container-max: 90rem;"{% endif %}
       {% if container_width == 'wide' %}style="--container-max: 110rem;"{% endif %}
  >
    {% if heading %}
      <header class="best-sellers__header">
        <h2 class="best-sellers__title h2">{{ heading }}</h2>
      </header>
    {% endif %}

    <ul class="best-sellers__list" role="list">
      {%- assign rendered = 0 -%}
      {% for block in section.blocks %}
        {% if block.type == 'item' %}
          {% assign p = block.settings.product %}
          {% if p %}
            {% assign title = block.settings.heading | default: p.title %}
            {% assign img = block.settings.image | default: p.featured_media %}
            <li class="best-sellers__item" {{ block.shopify_attributes }}>
              <a class="best-sellers__link{% if forloop.first %} is-active{% endif %}"
                 href="{{ p.url }}"
                 aria-label="{{ title | escape }}">
                <span class="best-sellers__left">
                  <span class="best-sellers__num" aria-hidden="true">{{ forloop.index }}</span>
                  <span class="best-sellers__thumb">
                    {% if img %}
                      {% if img.preview_image %}
                        {{ img | image_url: width: 120 | image_tag: widths: '80, 100, 120', sizes: '60px', alt: title, loading: 'lazy' }}
                      {% else %}
                        {{ img | image_url: width: 120 | image_tag: widths: '80, 100, 120', sizes: '60px', alt: title, loading: 'lazy' }}
                      {% endif %}
                    {% endif %}
                  </span>
                </span>
                <span class="best-sellers__meta">
                  <span class="best-sellers__heading">{{ title }}</span>
                </span>
              </a>
            </li>
            {%- assign rendered = rendered | plus: 1 -%}
          {% endif %}
        {% endif %}
      {% endfor %}
      {% if rendered == 0 %}
        <li class="best-sellers__empty">No products selected. Add Best seller items in the section.</li>
      {% endif %}
    </ul>
  </div>
</section>

<script>
  (() => {
    const root = document.getElementById('shopify-section-{{ section.id }}');
    if (!root) return;
    const links = root.querySelectorAll('.best-sellers__link');
    if (!links.length) return;
    const setActive = (el) => {
      links.forEach(a => a.classList.toggle('is-active', a === el));
    };
    links.forEach((a) => {
      a.addEventListener('mouseenter', () => setActive(a));
      a.addEventListener('focus', () => setActive(a));
    });
  })();
</script>

{% style %}
  /* Offsets */
  #shopify-section-{{ section.id }} {
    margin-top: {{ mobile_offset_top }}px;
    margin-bottom: {{ mobile_offset_bottom }}px;
  }
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} {
      margin-top: {{ desktop_offset_top }}px;
      margin-bottom: {{ desktop_offset_bottom }}px;
    }
  }

  #shopify-section-{{ section.id }} .best-sellers {
    --bs-bg: {{ color_bg }};
    --bs-text: {{ color_text }};
    --bs-border: {{ color_border }};
    --bs-active-text: {{ color_active_text }};
    --bs-active-border: {{ color_active_border }};
    background: var(--bs-bg);
    color: var(--bs-text);
  }

  .best-sellers__inner.page-width {
    max-width: var(--container-max, var(--page-width));
    margin: 0 auto;
    padding-left: var(--page-margin, 1rem);
    padding-right: var(--page-margin, 1rem);
  }

  .best-sellers__header { margin-bottom: var(--heading-gap); text-align: left; }
  .best-sellers__title { margin: 0; }

  .best-sellers__list { display: grid; gap: .5rem; grid-template-columns: 1fr; }
  @media (min-width: 990px) {
    .best-sellers__list { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  }

  .best-sellers__item { list-style: none; }
  .best-sellers__link {
    display: flex; align-items: center; gap: .75rem; padding: .625rem .75rem; text-decoration: none;
    color: inherit; border: 1px solid var(--bs-border); border-radius: 6px;
    transition: color .2s ease, border-color .2s ease, background-color .2s ease;
  }
  .best-sellers__link:hover, .best-sellers__link:focus-visible { border-color: var(--bs-active-border); color: var(--bs-active-text); }
  .best-sellers__link.is-active { border-color: var(--bs-active-border); color: var(--bs-active-text); }

  .best-sellers__left { display: inline-flex; align-items: center; gap: .5rem; }
  .best-sellers__num { display: none; font-weight: 700; min-width: 1.25rem; text-align: right; opacity: .8; }
  @media (min-width: 990px) {
    [data-show-numeric="true"] .best-sellers__num { display: inline-block; }
  }

  .best-sellers__thumb { width: 40px; height: 40px; display: inline-flex; align-items: center; justify-content: center; overflow: hidden; border-radius: 4px; }
  .best-sellers__thumb img { width: 100%; height: auto; display: block; }

  .best-sellers__heading { font-weight: 600; }
  .best-sellers__empty { opacity: .7; padding: .75rem; border: 1px dashed var(--bs-border); border-radius: 6px; }
{% endstyle %}

{% schema %}
{
  "name": "Best sellers",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "text", "id": "heading", "label": "Heading", "default": "Best sellers" },
    { "type": "checkbox", "id": "show_numeric", "label": "Show numeric (desktop)", "default": true },

    { "type": "header", "content": "Colors" },
    { "type": "color", "id": "color_bg", "label": "Background", "default": "#ffffff" },
    { "type": "color", "id": "color_text", "label": "Text", "default": "#111111" },
    { "type": "color", "id": "color_border", "label": "Border", "default": "#e5e5e5" },
    { "type": "color", "id": "color_active_text", "label": "Link active (text)", "default": "#111111" },
    { "type": "color", "id": "color_active_border", "label": "Link active (border)", "default": "#111111" },

    { "type": "header", "content": "Layout" },
    {
      "type": "select",
      "id": "container_width",
      "label": "Container width",
      "default": "inherit",
      "options": [
        { "value": "inherit", "label": "Inherit" },
        { "value": "narrow", "label": "Narrow" },
        { "value": "wide", "label": "Wide" }
      ]
    },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "blocks": [
    {
      "type": "item",
      "name": "Best seller",
      "limit": 12,
      "settings": [
        { "type": "product", "id": "product", "label": "Product" },
        { "type": "text", "id": "heading", "label": "Heading (optional)" },
        { "type": "image_picker", "id": "image", "label": "Image (optional)" }
      ]
    }
  ],
  "presets": [
    { "name": "Best sellers", "blocks": [ { "type": "item" }, { "type": "item" }, { "type": "item" } ] }
  ]
}
{% endschema %}
