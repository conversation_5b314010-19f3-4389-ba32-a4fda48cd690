# Enhanced Announcement Bar Features

## Overview
The announcement bar section has been enhanced with advanced features for marketplace themes:

## Key Features

### 1. Multiple Announcements (Blocks)
- Support for up to 5 announcement blocks
- Each announcement can have different text, links, and colors
- Individual link color override per announcement

### 2. Auto-Rotation
- **Auto-rotate announcements**: Toggle to enable/disable automatic cycling
- **Rotation speed**: 2-10 seconds (default: 5 seconds)
- **Pause on hover**: Auto-rotation pauses when user hovers over the announcement
- **Manual navigation**: Click navigation dots to jump to specific announcements

### 3. Visual Navigation
- **Navigation dots**: Show/hide navigation dots for manual control
- **Active state**: Current announcement is highlighted
- **Smooth transitions**: Fade-in animation between announcements

### 4. Individual Announcement Settings
Each announcement block includes:
- **Text**: Announcement message (textarea)
- **Link toggle**: Enable/disable link functionality
- **Link label**: Call-to-action text
- **Link URL**: Destination URL
- **Link color**: Override link color (optional)

### 5. Global Settings
- **Background color**: Global background for all announcements
- **Text color**: Global text color
- **Alignment**: Left, center, or right alignment
- **Dismissible**: Allow users to close the announcement bar
- **Mobile visibility**: Show/hide on mobile devices

### 6. Marketplace-Ready Features
- **Editor-driven**: All settings configurable via theme editor
- **No hardcoded values**: Fully customizable for merchants
- **Responsive design**: Mobile-optimized with proper breakpoints
- **Accessibility**: ARIA labels and keyboard navigation support
- **Performance**: Optimized JavaScript with event cleanup

## Technical Implementation

### CSS Architecture
- Uses theme's existing CSS grid system (`.full-width` utility)
- CSS variables for consistent spacing (`--page-width`, `--page-margin`)
- Mobile-first responsive design
- Smooth animations with CSS keyframes

### JavaScript Features
- Vanilla JavaScript (no dependencies)
- Auto-rotation with configurable timing
- Event-driven navigation (click, hover)
- Cookie-based dismissal state
- Proper cleanup on section removal

### Schema Structure
- Organized settings with headers for logical grouping
- Range inputs with proper validation
- Color pickers with sensible defaults
- Checkbox toggles with helpful info text
- Block-based architecture for scalability

## Usage Instructions

1. **Add the section**: Include "Announcement Bar" in your theme
2. **Configure global settings**: Set colors, alignment, and rotation preferences
3. **Add announcements**: Use the "Add block" button to create multiple announcements
4. **Customize each announcement**: Set unique text, links, and colors per announcement
5. **Preview and test**: Verify auto-rotation and mobile responsiveness

## Browser Support
- Modern browsers with CSS Grid support
- Graceful fallbacks for older browsers
- Mobile Safari compatibility
- Touch device optimization
