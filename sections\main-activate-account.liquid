{% comment %}
  Main Activate Account Section
  - Static section for customers/activate_account template
  - Adds breadcrumbs, layout offsets, and consistent button styles
{% endcomment %}

{%- liquid
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="activate color-{{ section.settings.color_scheme | default: 'background-1' }}" data-section="{{ section.id }}">
  <div class="activate__inner page-width">
    {% render 'breadcrumbs' %}

    <h1 class="h2">{{ 'customers.activate.title' | t | default: 'Activate account' }}</h1>
    <p>{{ 'customers.activate.subtext' | t | default: 'Create your password to activate your account.' }}</p>

    {% form 'activate_customer_password' %}
      {% if form.errors %}
        <div role="alert">{{ form.errors | default_errors }}</div>
      {% endif %}

      <div class="field">
        <label for="ActivatePassword-{{ section.id }}">{{ 'customers.activate.password' | t | default: 'Password' }}</label>
        <input type="password" name="customer[password]" id="ActivatePassword-{{ section.id }}" autocomplete="new-password" required>
      </div>
      <div class="field">
        <label for="ActivatePasswordConfirm-{{ section.id }}">{{ 'customers.activate.password_confirm' | t | default: 'Confirm password' }}</label>
        <input type="password" name="customer[password_confirmation]" id="ActivatePasswordConfirm-{{ section.id }}" autocomplete="new-password" required>
      </div>

      <div style="display:flex; gap:.75rem; align-items:center; flex-wrap:wrap;">
        <button type="submit" class="button button--primary">{{ 'customers.activate.submit' | t | default: 'Activate' }}</button>
        <button name="decline" class="button button--secondary">{{ 'customers.activate.cancel' | t | default: 'Decline' }}</button>
      </div>
    {% endform %}
  </div>
</section>

{% style %}
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; } }

  #shopify-section-{{ section.id }} .activate__inner { max-width: var(--page-width); margin: 0 auto; padding: 1.5rem 1rem; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} .activate__inner { padding: 2rem 1.25rem; } }
{% endstyle %}

{% schema %}
{
  "name": "Main activate account",
  "settings": [
    { "type": "header", "content": "Colors" },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "background-1" },
    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "presets": [ { "name": "Main activate account" } ]
}
{% endschema %}
