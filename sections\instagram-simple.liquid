{%- comment -%}
  Simple Instagram Display Section
  Manual approach - merchants upload images and add Instagram links
  Much simpler than app integration, fully controlled
{%- endcomment -%}

{{ 'section-instagram-simple.css' | asset_url | stylesheet_tag }}

<section class="instagram-simple section">
  <div class="page-width">
    {%- if section.settings.heading != blank -%}
      <h2 class="section-heading">{{ section.settings.heading | escape }}</h2>
    {%- endif -%}
    
    <div class="instagram-posts">
      {%- for block in section.blocks -%}
        {%- if block.type == 'instagram_post' -%}
          <div class="instagram-post" {{ block.shopify_attributes }}>
            {%- if block.settings.image != blank -%}
              <div class="instagram-post__image-wrapper{% if section.settings.enable_image_zoom %} zoom-on-scroll{% endif %}"{% if section.settings.enable_image_zoom %} data-zoom-on-scroll{% endif %}{% if section.settings.enable_image_reveal %} data-reveal-on-scroll{% endif %}>
                {%- if block.settings.instagram_url != blank -%}
                  <a href="{{ block.settings.instagram_url }}" target="_blank" rel="noopener" class="instagram-post__link">
                {%- endif -%}
                
                {{ block.settings.image 
                  | image_url: width: 600 
                  | image_tag: 
                    class: 'instagram-post__image',
                    alt: block.settings.caption | default: 'Instagram post',
                    loading: 'lazy'
                }}
                
                {%- if block.settings.instagram_url != blank -%}
                  <div class="instagram-post__overlay">
                    <svg class="instagram-icon" width="24" height="24" viewBox="0 0 24 24" fill="white">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                    </svg>
                    <span>View on Instagram</span>
                  </div>
                  </a>
                {%- endif -%}
              </div>
              
              {%- if block.settings.caption != blank -%}
                <div class="instagram-post__caption">
                  <p>{{ block.settings.caption | escape }}</p>
                </div>
              {%- endif -%}
            {%- endif -%}
          </div>
        {%- endif -%}
      {%- endfor -%}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Instagram feed (simple)",
  "tag": "section", 
  "class": "section",
  "settings": [
    { "type": "checkbox", "id": "enable_image_reveal", "label": "Enable image reveal-on-scroll", "default": true },
    { "type": "checkbox", "id": "enable_image_zoom", "label": "Enable image zoom-on-scroll", "default": true },
    {
      "type": "text",
      "id": "heading", 
      "label": "Heading",
      "default": "Follow us on Instagram"
    }
  ],
  "blocks": [
    {
      "type": "instagram_post",
      "name": "Instagram post",
      "settings": [
    { "type": "checkbox", "id": "enable_image_reveal", "label": "Enable image reveal-on-scroll", "default": true },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "url",
          "id": "instagram_url",
          "label": "Instagram post URL",
          "info": "Link to the original Instagram post (optional)"
        },
        {
          "type": "text",
          "id": "caption",
          "label": "Caption",
          "info": "Optional caption text"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Instagram feed (simple)"
    }
  ]
}
{% endschema %}



