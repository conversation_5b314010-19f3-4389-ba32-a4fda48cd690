{% comment %}
  Main Addresses Section
  - Static section for customers/addresses template
  - Adds breadcrumbs, layout offsets, and consistent button styles
{% endcomment %}

{%- liquid
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="addresses color-{{ section.settings.color_scheme | default: 'background-1' }}" data-section="{{ section.id }}">
  <div class="addresses__inner page-width">
    {% render 'breadcrumbs' %}

    <h1 class="h2">{{ 'customers.addresses.title' | t | default: 'Addresses' }}</h1>
    <p><a href="{{ routes.account_url }}">{{ 'customers.addresses.back' | t | default: 'Back to account' }}</a></p>

    <div style="margin: 1rem 0;">
      <button class="button button--primary" type="button" onclick="document.getElementById('AddressNewForm').hidden=false;">{{ 'customers.addresses.add_new' | t | default: 'Add a new address' }}</button>
    </div>

    <div id="AddressNewForm" hidden>
      <h2 class="h3">{{ 'customers.addresses.new' | t | default: 'New address' }}</h2>
      {% form 'customer_address', customer.new_address %}
        {{ form.errors | default_errors }}
        <div class="field"><label for="AddressFirstNameNew">{{ 'customers.addresses.first_name' | t | default: 'First name' }}</label><input id="AddressFirstNameNew" type="text" name="address[first_name]" autocomplete="given-name" value="{{ form.first_name }}"></div>
        <div class="field"><label for="AddressLastNameNew">{{ 'customers.addresses.last_name' | t | default: 'Last name' }}</label><input id="AddressLastNameNew" type="text" name="address[last_name]" autocomplete="family-name" value="{{ form.last_name }}"></div>
        <div class="field"><label for="AddressCompanyNew">{{ 'customers.addresses.company' | t | default: 'Company' }}</label><input id="AddressCompanyNew" type="text" name="address[company]" autocomplete="organization" value="{{ form.company }}"></div>
        <div class="field"><label for="AddressAddress1New">{{ 'customers.addresses.address1' | t | default: 'Address line 1' }}</label><input id="AddressAddress1New" type="text" name="address[address1]" autocomplete="address-line1" value="{{ form.address1 }}"></div>
        <div class="field"><label for="AddressAddress2New">{{ 'customers.addresses.address2' | t | default: 'Address line 2' }}</label><input id="AddressAddress2New" type="text" name="address[address2]" autocomplete="address-line2" value="{{ form.address2 }}"></div>
        <div class="field"><label for="AddressCityNew">{{ 'customers.addresses.city' | t | default: 'City' }}</label><input id="AddressCityNew" type="text" name="address[city]" autocomplete="address-level2" value="{{ form.city }}"></div>
        <div class="field"><label for="AddressCountryNew">{{ 'customers.addresses.country' | t | default: 'Country' }}</label><select id="AddressCountryNew" name="address[country]">{{ country_option_tags }}</select></div>
        <div class="field"><label for="AddressProvinceNew">{{ 'customers.addresses.province' | t | default: 'Province/State' }}</label><input id="AddressProvinceNew" type="text" name="address[province]" value="{{ form.province }}"></div>
        <div class="field"><label for="AddressZipNew">{{ 'customers.addresses.zip' | t | default: 'Postal/ZIP code' }}</label><input id="AddressZipNew" type="text" name="address[zip]" autocomplete="postal-code" value="{{ form.zip }}"></div>
        <div class="field"><label for="AddressPhoneNew">{{ 'customers.addresses.phone' | t | default: 'Phone' }}</label><input id="AddressPhoneNew" type="tel" name="address[phone]" autocomplete="tel" value="{{ form.phone }}"></div>
        <div class="field"><label><input type="checkbox" name="address[default]" value="1"> {{ 'customers.addresses.set_default' | t | default: 'Set as default address' }}</label></div>
        <button class="button button--primary" type="submit">{{ 'customers.addresses.save' | t | default: 'Save' }}</button>
        <button class="button button--secondary" type="reset" onclick="this.closest('div#AddressNewForm').hidden=true; return false;">{{ 'general.close' | t | default: 'Close' }}</button>
      {% endform %}
    </div>

    <div style="display:grid; gap:1rem; margin-top: 1rem;">
      {% for address in customer.addresses %}
        <div style="border:1px solid #eee; border-radius:8px; padding:1rem;">
          {% if address == customer.default_address %}
            <strong>{{ 'customers.addresses.default' | t | default: 'Default' }}</strong>
          {% endif %}
          <div style="margin:.5rem 0;">{{ address | format_address }}</div>
          <div style="display:flex; gap:.5rem; flex-wrap:wrap;">
            <button class="button button--secondary" type="button" onclick="document.getElementById('EditAddress_{{ address.id }}').hidden=false;">{{ 'customers.addresses.edit' | t | default: 'Edit' }}</button>
            <form method="post" action="/account/addresses/{{ address.id }}" onsubmit="return confirm('Delete this address?');">
              <input type="hidden" name="_method" value="delete">
              <button class="button button--secondary" type="submit">{{ 'customers.addresses.delete' | t | default: 'Delete' }}</button>
            </form>
          </div>
          <div id="EditAddress_{{ address.id }}" hidden style="margin-top: .75rem;">
            <h3 class="h4">{{ 'customers.addresses.edit' | t | default: 'Edit address' }}</h3>
            {% form 'customer_address', address %}
              {{ form.errors | default_errors }}
              <div class="field"><label for="AddressFirstName_{{ address.id }}">{{ 'customers.addresses.first_name' | t | default: 'First name' }}</label><input id="AddressFirstName_{{ address.id }}" type="text" name="address[first_name]" autocomplete="given-name" value="{{ form.first_name | default: address.first_name }}"></div>
              <div class="field"><label for="AddressLastName_{{ address.id }}">{{ 'customers.addresses.last_name' | t | default: 'Last name' }}</label><input id="AddressLastName_{{ address.id }}" type="text" name="address[last_name]" autocomplete="family-name" value="{{ form.last_name | default: address.last_name }}"></div>
              <div class="field"><label for="AddressCompany_{{ address.id }}">{{ 'customers.addresses.company' | t | default: 'Company' }}</label><input id="AddressCompany_{{ address.id }}" type="text" name="address[company]" autocomplete="organization" value="{{ form.company | default: address.company }}"></div>
              <div class="field"><label for="AddressAddress1_{{ address.id }}">{{ 'customers.addresses.address1' | t | default: 'Address line 1' }}</label><input id="AddressAddress1_{{ address.id }}" type="text" name="address[address1]" autocomplete="address-line1" value="{{ form.address1 | default: address.address1 }}"></div>
              <div class="field"><label for="AddressAddress2_{{ address.id }}">{{ 'customers.addresses.address2' | t | default: 'Address line 2' }}</label><input id="AddressAddress2_{{ address.id }}" type="text" name="address[address2]" autocomplete="address-line2" value="{{ form.address2 | default: address.address2 }}"></div>
              <div class="field"><label for="AddressCity_{{ address.id }}">{{ 'customers.addresses.city' | t | default: 'City' }}</label><input id="AddressCity_{{ address.id }}" type="text" name="address[city]" autocomplete="address-level2" value="{{ form.city | default: address.city }}"></div>
              <div class="field"><label for="AddressCountry_{{ address.id }}">{{ 'customers.addresses.country' | t | default: 'Country' }}</label><select id="AddressCountry_{{ address.id }}" name="address[country]">{{ country_option_tags }}</select></div>
              <div class="field"><label for="AddressProvince_{{ address.id }}">{{ 'customers.addresses.province' | t | default: 'Province/State' }}</label><input id="AddressProvince_{{ address.id }}" type="text" name="address[province]" value="{{ form.province | default: address.province }}"></div>
              <div class="field"><label for="AddressZip_{{ address.id }}">{{ 'customers.addresses.zip' | t | default: 'Postal/ZIP code' }}</label><input id="AddressZip_{{ address.id }}" type="text" name="address[zip]" autocomplete="postal-code" value="{{ form.zip | default: address.zip }}"></div>
              <div class="field"><label for="AddressPhone_{{ address.id }}">{{ 'customers.addresses.phone' | t | default: 'Phone' }}</label><input id="AddressPhone_{{ address.id }}" type="tel" name="address[phone]" autocomplete="tel" value="{{ form.phone | default: address.phone }}"></div>
              <div class="field"><label><input type="checkbox" name="address[default]" value="1" {% if address == customer.default_address %}checked{% endif %}> {{ 'customers.addresses.set_default' | t | default: 'Set as default address' }}</label></div>
              <button class="button button--primary" type="submit">{{ 'customers.addresses.update' | t | default: 'Update' }}</button>
              <button class="button button--secondary" type="reset" onclick="this.closest('div[id^=EditAddress_]').hidden=true; return false;">{{ 'general.close' | t | default: 'Close' }}</button>
            {% endform %}
          </div>
        </div>
      {% endfor %}
    </div>
  </div>
</section>

{% style %}
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; } }

  #shopify-section-{{ section.id }} .addresses__inner { max-width: var(--page-width); margin: 0 auto; padding: 1.5rem 1rem; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} .addresses__inner { padding: 2rem 1.25rem; } }
{% endstyle %}

{% schema %}
{
  "name": "Main addresses",
  "settings": [
    { "type": "header", "content": "Colors" },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "background-1" },
    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "presets": [ { "name": "Main addresses" } ]
}
{% endschema %}
