{% comment %}
  Password page header.
  Displays store logo/name and optional heading.
{% endcomment %}

{%- liquid
  assign heading = section.settings.heading
  if heading == blank
    assign heading = 'password.title' | t
  endif
-%}

{% style %}
  .password-header {
    padding: 2rem 0;
    text-align: {{ section.settings.alignment }};
  }
  .password-header__inner { display: grid; gap: 1rem; }
  .password-header__logo { display: inline-block; line-height: 0; }
  .password-header__title { font-size: clamp(1.25rem, 3vw, 2rem); font-weight: 600; }
  .password-header__shop-name { font-weight: 600; font-size: clamp(1rem, 2.5vw, 1.25rem); }
{% endstyle %}

<header class="password-header" data-section="{{ section.id }}">
  <div class="password-header__inner page-width">
    {% if section.settings.logo != blank %}
      <span class="password-header__logo" style="--w: {{ section.settings.logo_width | default: 160 }}px;">
        <img src="{{ section.settings.logo | image_url: width: section.settings.logo_width }}"
             alt="{{ shop.name | escape }}"
             width="{{ section.settings.logo_width }}"
             height="auto"
             loading="lazy">
      </span>
    {% else %}
      <div class="password-header__shop-name">{{ shop.name }}</div>
    {% endif %}

    {% if section.settings.show_heading %}
      <h1 class="password-header__title">{{ heading }}</h1>
    {% endif %}
  </div>
</header>

{% schema %}
{
  "name": "Password header",
  "settings": [
    { "type": "checkbox", "id": "show_heading", "label": "Show heading", "default": true },
    { "type": "text", "id": "heading", "label": "Heading (optional)", "default": "Opening soon" },
    { "type": "image_picker", "id": "logo", "label": "Logo (optional)" },
    { "type": "range", "id": "logo_width", "label": "Logo width", "min": 60, "max": 400, "step": 10, "default": 160 },
    {
      "type": "select",
      "id": "alignment",
      "label": "Alignment",
      "default": "center",
      "options": [
        { "value": "left", "label": "Left" },
        { "value": "center", "label": "Center" },
        { "value": "right", "label": "Right" }
      ]
    }
  ]
}
{% endschema %}
