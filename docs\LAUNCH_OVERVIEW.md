# Theme Launch Guide (Overview)

This short guide points to the detailed launch checklist and highlights the critical setup steps to make the theme production‑ready.

- Complete checklist: see docs/LAUNCH_CHECKLIST.md
- Use this overview for quick navigation and to share with stakeholders.

## Quick Setup (Pages & Links)

1) Create pages and assign templates (Online Store → Pages):
- FAQ → template: `page.faq`
- Contact Us → template: `page.contact`
- Shipping & Returns → template: `page.shipping-returns`

2) Footer quick links (Theme editor → Footer):
- Enable and assign: FAQ, Contact Us, Shipping & Returns
- Optionally enable Account link (auto routes to login/account)

3) Customer accounts (Online Store → Preferences → Customer accounts):
- Enable accounts if needed; templates are available at:
  - Login `/account/login`, Register `/account/register`, Account `/account`, Addresses `/account/addresses`, Order (from orders list)

## Must‑Do Checks

- Search page settings: filters, grid toggle, pagination mode
- Contact page: Google Maps API key + query or image fallbacks
- FAQ page: categories + Q&A blocks, deep links/copy link
- Translations: update `locales/en.default.json` for any custom labels
- Linting: `shopify theme check`
- Preview on a dev theme: `shopify theme push` → preview URL

## Useful URLs (after setup)

- Pages: `/pages/faq`, `/pages/contact`, `/pages/shipping-returns`
- Customer: `/account/login`, `/account/register`, `/account`, `/account/addresses`
- System: `/search`, `/password` (when locked), any 404 for error page

## References

- Detailed steps and validation: docs/LAUNCH_CHECKLIST.md
- Shopify theme dev docs: SHOPIFY_THEME_DEV_SOLUTION.md, SHOPIFY_REQUIREMENTS.md
