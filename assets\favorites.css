/* Favorites Toast Notifications */
.favorites-toast {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 9999;
  background: rgb(var(--color-foreground));
  color: rgb(var(--color-background));
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  max-width: 300px;
  pointer-events: none;
}

.favorites-toast--show {
  transform: translateX(0);
  opacity: 1;
}

.favorites-toast__content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.favorites-toast__icon {
  flex-shrink: 0;
  color: #e53e3e;
}

.favorites-toast__text {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.2;
}

/* Mobile responsive */
@media screen and (max-width: 749px) {
  .favorites-toast {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
    transform: translateY(-100%);
  }

  .favorites-toast--show {
    transform: translateY(0);
  }

  .favorites-toast__content {
    gap: 0.5rem;
  }

  .favorites-toast__text {
    font-size: 0.8125rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .favorites-toast {
    background: rgb(var(--color-background));
    color: rgb(var(--color-foreground));
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1);
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .favorites-toast {
    transition: opacity 0.2s ease;
  }
}
