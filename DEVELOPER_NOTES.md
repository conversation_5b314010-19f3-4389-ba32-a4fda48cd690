# Skeleton Theme - Developer Notes

## 🔧 Technical Implementation Overview

This document contains technical notes for developers working on the Skeleton theme.

---

## ⚡ Conditional Settings with `visible_if`

### Implementation Details

The theme uses Shopify's new `visible_if` conditional settings feature for improved UX.

#### Key Patterns

**Sticky Dependencies:**
```json
"visible_if": "{{ section.settings.sticky_mode != 'none' }}"
```
Applied to: Animation section, Visual Effects section, Layout section

**Mode-Specific Controls:**
```json
"visible_if": "{{ sectio### Implementation Evolution
**Phase 1: Basic Hover System** - Initial two-layer crossfade with decode-first approach  
**Phase 2: Size Pop Elimination** - Fixed responsive sizing mismatches (50vw vs 100vw)  
**Phase 3: Comprehensive Normalization** - Unified layout architecture for complete consistency

### Final Architecture: Normalized Card System

#### 1. Unified Layout Control
```css
/* Single controlling box */
.card__media .media {
  position: relative;
  aspect-ratio: var(--card-aspect, 1 / 1);
  overflow: hidden;
}

/* Base + preview fill identically */
.card__media .media .card-media__primary,
.card__media .media .card-media__preview {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: var(--card-object-position, center center);
}
```

#### 2. Configurable Focal Point System
```liquid
<div class="card__media"
     style="--card-aspect: 1/1; --card-object-position: {{ product.metafields.theme.card_object_position | default: 'center center' }};">
```

**Benefits:**
- Per-product focal point control via metafields
- Consistent crop rules across all cards
- CSS variable-driven customization
- No inline dimensions on images

#### 3. True Overlay-Only Preview
```liquid
<img class="card-media__preview" alt="" aria-hidden="true" hidden>
```

**JavaScript Implementation:**
```js
// Preview starts empty, populated only on hover
this.previewImg.removeAttribute('srcset');
this.previewImg.removeAttribute('sizes');
this.previewImg.removeAttribute('src');

// On hover: decode-first, then fade in
if (variant.srcset) this.previewImg.srcset = variant.srcset;
if (variant.sizes) this.previewImg.sizes = variant.sizes;
this.previewImg.src = variant.src;

// On revert: fade out and clear for guaranteed empty state
this.previewImg.style.opacity = '0';
setTimeout(() => {
  this.previewImg.hidden = true;
  this.previewImg.removeAttribute('srcset');
  this.previewImg.removeAttribute('sizes');
  this.previewImg.removeAttribute('src');
}, 180);
```

### Key Technical Achievements

#### Eliminated All Visual Inconsistencies
- **Size Pops**: Fixed by using identical `srcset`/`sizes` across base, preview, and swatches
- **Focal Point Shifts**: Unified `object-position` ensures same crop on hover
- **Layout Jumps**: Absolute positioning in controlled aspect-ratio boxes
- **Candidate Mismatches**: Grid-consistent responsive sources throughout

#### Responsive Sizing Fixes
```liquid
{%- if columns <= 2 -%}
  {%- assign sizes_attr = '(min-width: 990px) 50vw, 100vw' -%}  <!-- Fixed: was 100vw, 100vw -->
{%- elsif columns == 3 -%}
  {%- assign sizes_attr = '(min-width: 990px) 33vw, 50vw' -%}
{%- endif -%}
```

#### Intelligent Default Media Selection
```liquid
# Determine card_media based on merchant variant order
assign card_media = product.featured_media
if color_option and color_option.values.size > 0
  assign first_color = color_option.values | first
  # Select first available variant of first color by position
  for v in product.variants
    if v.available and v_color == first_color and v.position < chosen_pos
      assign chosen_variant = v
    endif
  endfor
  if chosen_variant and chosen_variant.featured_media
    assign card_media = chosen_variant.featured_media
  endif
endif
```

### Performance Optimizations

#### Decode-First Loading
```js
const probe = new Image();
if (variant.srcset) probe.srcset = variant.srcset;
if (variant.sizes) probe.sizes = variant.sizes;
probe.src = variant.src;

if (probe.decode) {
  probe.decode().then(apply).catch(() => { probe.onload = apply; });
} else {
  probe.onload = apply;
}
```

#### Race Condition Protection
```js
const id = ++this.requestId;
const apply = () => {
  if (this.requestId !== id) return; // Newer hover cancelled this one
  // Apply decoded sources to preview...
};
```

#### Smart Change Detection
```js
const current = this.baseImg.currentSrc || this.baseImg.src || '';
const normalize = (u) => u ? u.replace(/([?&])width=\d+/, '$1').replace(/[?&]$/, '') : u;
if (normalize(current) === normalize(variant.src)) return; // Skip identical
```

### Quality Assurance Results

#### Cross-Browser Compatibility
- ✅ Modern browsers: Full HTMLImageElement.decode() support
- ✅ Legacy browsers: Graceful fallback to onload events
- ✅ Mobile Safari: Proper touch/hover detection
- ✅ Performance: Hardware-accelerated animations

#### Responsive Grid Testing
- ✅ 2-column layout: No size pops, consistent 50vw sizing
- ✅ 3-column layout: Proper 33vw candidate selection
- ✅ 4+ column layout: Standard calc(100vw / 4) behavior
- ✅ Mobile breakpoints: Smooth transitions between layouts

#### User Experience Validation
- ✅ **Lululemon-quality smoothness**: Buttery crossfades with no flicker
- ✅ **Visual consistency**: Every card behaves identically
- ✅ **No layout thrashing**: Fixed aspect ratios prevent reflow
- ✅ **Hover intent**: 50ms delay prevents accidental triggers
- ✅ **Touch device optimization**: Hover disabled on coarse pointers

### Implementation Files

#### Core Components
- `snippets/card-product.liquid`: Normalized markup with CSS variables
- `assets/variant-hover.js`: Sophisticated crossfade system
- `snippets/color-swatch-mapping.liquid`: Color-to-CSS mappings
- `templates/collection.json`: Swatch enablement settings

#### Technical Specifications
```liquid
<!-- Responsive image setup -->
{%- assign candidate_widths = '400,600,800,1000,1200,1400' | split: ',' -%}
{%- capture variant_srcset -%}
  {%- for w in candidate_widths -%}
    {{ media | image_url: width: w }} {{ w }}w{% unless forloop.last %}, {% endunless %}
  {%- endfor -%}
{%- endcapture -%}

<!-- Swatch data attributes -->
data-src="{{ media | image_url: width: image_width }}"
data-srcset="{{ variant_srcset | strip }}"
data-sizes="{{ sizes_attr | strip }}"
```

### Lessons Learned

#### Critical Success Factors
1. **Unified Layout Architecture**: Single controlling box eliminates inconsistencies
2. **Grid-Consistent Sources**: Same `sizes`/`widths` prevents candidate mismatches  
3. **Overlay-Only Approach**: Base image stability eliminates visual jumps
4. **CSS Variable Control**: Merchant-friendly customization without code changes
5. **Standards-Based Implementation**: Modern APIs with graceful fallbacks

#### Anti-Patterns Avoided
- ❌ Mixing `min-height` with `height` (unpredictable sizing)
- ❌ Inline dimensions on images (breaks responsive behavior)
- ❌ Base image swapping during hover (causes candidate re-selection)
- ❌ Hardcoded focal points (prevents merchant customization)
- ❌ Per-swatch revert handlers (causes thrashing between swatches)

### Future Maintenance

#### Extensibility Points
- `--card-aspect`: Change from square to other ratios
- `--card-object-position`: Per-product focal point control
- `product.metafields.theme.card_object_position`: Merchant customization
- Candidate widths array: Adjust for performance/quality balance

#### Monitoring Checklist
- [ ] Network tab: One request per hover, no upgrade fetches
- [ ] DevTools Elements: Preview sources match swatch data
- [ ] Visual testing: No size pops across grid breakpoints
- [ ] Performance: Smooth 60fps transitions on target devices
- [ ] Accessibility: Focus management and keyboard navigation

### Status: Production Ready ✅
The normalized card system provides enterprise-level hover functionality with Lululemon-quality smoothness. All visual inconsistencies have been eliminated through comprehensive architectural improvements and grid-aware responsive image handling.
```
Applied to: Scroll distance, animation duration, reduced motion

**Cascading Dependencies:**
```json
"visible_if": "{{ section.settings.sticky_mode != 'none' and section.settings.shadow_when_stuck == true }}"
```
Applied to: Shadow strength dropdown

**Resource Dependencies:**
```json
"visible_if": "{{ section.settings.logo != blank }}"
"visible_if": "{{ block.settings.promo_image != blank }}"
```

### Benefits
- Cleaner merchant interface
- Reduced configuration errors
- Professional theme editor experience
- Easier maintenance and support

---

## 🎬 Hero Banner Architecture

### Conditional Settings Implementation

**Media Mode Dependencies:**
```json
"visible_if": "{{ section.settings.media_mode == 'image' }}"
"visible_if": "{{ section.settings.media_mode == 'video' }}"
```

**Cascading Logic:**
```json
"visible_if": "{{ section.settings.media_mode == 'image' and section.settings.use_mobile_image == true }}"
"visible_if": "{{ section.settings.media_mode == 'video' and section.settings.autoplay_video == false }}"
"visible_if": "{{ section.settings.show_overlay == true }}"
```

### Performance Optimization

**Eager Loading Strategy:**
- `loading="eager"` and `fetchpriority="high"` only when `is_primary_hero` is true
- Responsive image optimization with `picture` element
- Art direction with separate mobile images

**Video Performance:**
- Poster images for faster first paint
- Autoplay compliance with muted attribute
- Reduced motion detection via JavaScript

### CSS Grid Layout System

**3-Layer Stack:**
```css
.hero__media, .hero__overlay, .hero__content { grid-area: 1 / 1; }
```

**9-Point Positioning:**
- CSS Grid with `align-content` and `justify-items`
- Responsive content width with `ch` units
- Mobile-first button layouts

### Accessibility Features

**Reduced Motion Support:**
- JavaScript detection of `prefers-reduced-motion`
- Automatic video pause for motion-sensitive users
- CSS transition disabling

**Semantic HTML:**
- Configurable heading tags (H1/H2/P)
- Proper alt text fallback hierarchy
- ARIA attributes for media elements

### Bug: horizontal white gutter around hero media and how we fixed it

- Symptom: The hero image/video showed narrow white margins on the left and right sides (not the expected full-bleed look).
- Root cause: The hero section was rendering inside the site container without the full-bleed pattern used elsewhere; the media layer was constrained by the section's static width, so viewport-wide media didn't reach the edges.
- Fix applied: Updated `sections/hero-banner.liquid` CSS to use the theme's standard full-bleed pattern and theme variables — specifically:
  - `.hero-banner { width: 100vw; margin-left: calc(-50vw + 50%); }` to make the media span the viewport.
  - `.hero__content { max-width: var(--page-width); margin: 0 auto; padding: 2rem var(--page-margin); }` to keep text/buttons aligned to the site's page width and gutters.
  - No hard-coded pixel values were introduced; the change relies entirely on existing CSS variables (`--page-width`, `--page-margin`) and the calc full-bleed pattern.
- Outcome: Media is now full-bleed across the viewport while content remains aligned to the site container. This preserves responsiveness and avoids horizontal scroll; added as a minimal, localized change in `sections/hero-banner.liquid`.

### Bug: hero banner height presets not responding to changes

- Symptom: When changing Banner height setting in the theme editor (Small, Medium, Large, etc.), the visible hero area remained the same size regardless of the selection.
- Root cause: CSS used `min-height` instead of `height`, which only sets a minimum constraint. Tall images/content could expand the section beyond the specified viewport height, making changes appear ineffective.
- Technical details:
  - `min-height: 60vh` allows growth → unpredictable visible area
  - `height: 60vh` forces exact height → predictable visible area matching setting
- Fix applied: Updated `sections/hero-banner.liquid` to:
  - Change all `.hero--height-*` selectors from `min-height` to `height`
  - Added custom height option with range slider (20-100vh)
  - Added header subtraction option using dynamic `--header-height` variable
  - Media uses `object-fit: cover` and `overflow: hidden` to crop appropriately
- Outcome: The visible hero area now always matches the selected viewport percentage (45vh, 60vh, 80vh, etc.), making height controls reliable and predictable. Header JS automatically sets `--header-height` for accurate subtraction calculations when needed.

---

## 🎨 Color Scheme System

### Architecture Overview

**Modern Shopify Color Management:**
- `color_scheme_group` in `settings_schema.json`
- Dynamic CSS variable generation
- Component-level scheme application
- Professional theme editor UX

### Schema Implementation

**Color Scheme Group Definition:**
```json
{
  "type": "color_scheme_group",
  "id": "color_schemes",
  "role": {
    "background": "background",
    "text": "text",
    "primary_button": "solid_button_background",
    "on_primary_button": "solid_button_label",
    "secondary_button": "background",
    "on_secondary_button": "outline_button_label",
    "primary_button_border": "solid_button_background",
    "secondary_button_border": "outline_button_label",
    "links": "accent_1",
    "icons": "text"
  }
}
```

**Color Properties:**
- `background` / `background_gradient`
- `text` (primary text color)
- `accent_1` / `accent_2` (links, highlights)
- `outline_button_label` / `solid_button_background` / `solid_button_label`

### CSS Variable Generation

**Snippet: `css-variables.liquid`**
```liquid
{% for scheme in settings.color_schemes %}
  .color-{{ scheme.id }} {
    --color-background: {{ scheme.settings.background }};
    --color-background-rgb: {{ scheme.settings.background | color_to_rgb | remove: 'rgb(' | remove: ')' }};
    --gradient-background: {{ scheme.settings.background_gradient | default: scheme.settings.background }};
    --color-foreground: {{ scheme.settings.text }};
    --color-accent-1: {{ scheme.settings.accent_1 }};
    --color-button: {{ scheme.settings.solid_button_background }};
    --color-button-label: {{ scheme.settings.solid_button_label }};
  }
{% endfor %}
```

### Default Color Schemes

**Scheme 1 (Light):**
- Background: `#ffffff`
- Text: `#121212`
- Accent: `#334fb4`
- Professional, clean appearance

**Scheme 2 (Neutral):**
- Background: `#f3f3f3`
- Text: `#2e2a39`
- Accent: `#476154`
- Warm, earth-tone palette

**Scheme 3 (Dark):**
- Background: `#121212`
- Text: `#ffffff`
- Accent: `#6b7280`
- High contrast, modern dark mode

### Implementation Patterns

**Section-Level Application:**
```liquid
<section class="hero-banner color-{{ section.settings.color_scheme }}">
```

**CSS Variable Usage:**
```css
.hero-banner {
  background: var(--color-background);
  color: var(--color-foreground);
}

.button--primary {
  background: var(--color-button);
  color: var(--color-button-label);
}
```

**RGB Variants for Transparency:**
```css
.overlay {
  background: rgba(var(--color-background-rgb), 0.8);
}
```

### Theme Editor Experience

**Merchant Benefits:**
- Intuitive color customization
- Real-time preview
- Consistent application across sections
- Professional color harmony

**Developer Benefits:**
- Centralized color management
- Automatic CSS variable generation
- Easy section integration
- Maintainable color system

### Performance Considerations

**CSS Generation:**
- Variables generated once per page load
- Scoped to component classes
- No runtime color calculations
- Minimal CSS output

**Best Practices:**
- Use RGB variants for transparency effects
- Maintain contrast ratios for accessibility
- Test all schemes with actual content
- Provide meaningful default values

---

## 🚀 Development Workflow

### Current Status (August 25, 2025)

**Active Development Server:**
- Local: `http://127.0.0.1:9292`
- Preview: `https://curlybrak.myshopify.com/?preview_theme_id=153920864468`
- Theme Editor: `https://curlybrak.myshopify.com/admin/themes/153920864468/editor?hr=9292`

**Recently Implemented:**
1. **Color Scheme System** - Complete implementation with 3 default schemes
2. **Hero Banner Section** - With conditional settings and performance optimization
3. **Conditional Settings** - Using Shopify's new `visible_if` feature
4. **Documentation Consolidation** - From 4 separate files to 2 focused documents

### Development Commands

**Theme Validation:**
```bash
shopify theme check
```
Status: ✅ All 41 files pass with no offenses

**Development Server:**
```bash
shopify theme dev
```
Current: ✅ Running on port 9292

**File Structure:**
```
config/
├── settings_schema.json    # Color schemes + conditional settings
├── settings_data.json      # 3 default color schemes
sections/
├── hero-banner.liquid      # Complete hero with conditional UI
snippets/
├── css-variables.liquid    # Dynamic color scheme CSS generation
layout/
├── theme.liquid           # Color scheme classes applied
```

### Testing Checklist

**Color Schemes:**
- [ ] Test all 3 schemes in theme editor
- [ ] Verify CSS variables generate correctly
- [ ] Check contrast ratios for accessibility
- [ ] Test gradient background support

**Hero Banner:**
- [ ] Test image/video mode switching
- [ ] Verify conditional settings hide/show correctly
- [ ] Test all 9 content positions
- [ ] Check performance with eager loading
- [ ] Test reduced motion compliance

**Conditional Settings:**
- [ ] Header sticky mode dependencies
- [ ] Hero banner media mode cascades
- [ ] Overlay settings visibility logic

### Known Issues

**Markdown Linting:**
- Multiple MD032/MD031 errors in documentation files
- Non-blocking: Affects documentation formatting only
- Resolution: Add blank lines around lists and code blocks

**Development Notes:**
- Color scheme system fully functional
- Hero banner ready for marketplace
- Conditional settings improve merchant UX
- Documentation needs formatting cleanup

---

## 🏗️ Header Architecture

### Sticky Header System

**CSS Implementation:**
- `position: sticky` for "always" mode
- JavaScript with `IntersectionObserver` for "on-scroll-up" mode
- CSS variables for dynamic theming
- Hardware-accelerated animations

**JavaScript Features:**
- Vanilla JS (no dependencies)
- Modern APIs: IntersectionObserver, ResizeObserver
- Passive scroll listeners for performance
- Accessibility: Focus management, keyboard support

### Navigation System

**Block-Based Architecture:**
- `simple_link`: Basic dropdown menus
- `megamenu`: Multi-column with promotional content
- Legacy fallback for backwards compatibility

**Mobile Navigation:**
- Hamburger menu with backdrop
- `<details>` elements for native dropdowns
- Touch-friendly interaction areas
- Smooth CSS animations

---

## 🎨 CSS Architecture

### Methodology
- **BEM naming**: `.header__element--modifier`
- **CSS Variables**: Scoped to components
- **Mobile-first**: `min-width` media queries
- **Single-level nesting**: Maximum specificity control

### Performance Optimizations
- Hardware acceleration: `transform3d(0,0,0)`
- Minimal reflows: `transform` over `top/left`
- Passive event listeners
- Efficient selector specificity (0 1 0)

### Browser Support
- Modern browsers: Full feature support
- Progressive enhancement: Graceful fallbacks
- Mobile Safari: Rubber-banding compatibility

---

## 📱 Mobile-First Design

### Breakpoints
- **Mobile**: < 990px
- **Desktop**: ≥ 990px
- **Touch targets**: 45px minimum

### Responsive Strategy
- Flexbox layout system
- Adaptive navigation patterns
- Touch-optimized interactions
- Safe area support (iOS notch)

---

## 🔍 Schema Validation

### Theme Check Compliance
All settings pass `shopify theme check` validation:
- Proper JSON schema structure
- Valid setting types and ranges
- Appropriate default values
- Accessible labels and descriptions

### Setting Types Used
- `header`: Visual grouping
- `paragraph`: Informational content
- `range`: Numeric inputs with constraints
- `select`: Dropdown options
- `checkbox`: Boolean toggles
- `image_picker`: Asset selection
- `link_list`: Navigation menus
- `text`: Short text inputs
- `url`: Link inputs

---

## 🚀 Performance Considerations

### JavaScript
- Event delegation for efficiency
- Debounced scroll handlers
- Minimal DOM queries
- Early returns for performance

### CSS
- Constrained animations to `transform` and `opacity`
- CSS containment where appropriate
- Efficient selector patterns
- Hardware acceleration hints

### Assets
- Optimized SVG icons
- Font loading optimization
- Critical CSS separation
- Asset preloading where beneficial

---

## 📐 Code Organization

### File Structure
```
sections/header.liquid    # Main header component
snippets/css-variables.liquid  # Theme variables
assets/critical.css      # Critical path CSS
```

### Liquid Patterns
- Defensive coding: Always check for blank values
- Early returns: Reduce nesting complexity
- Variable assignment: Clear, descriptive names
- Comments: Explain complex logic

### JavaScript Patterns
- Module pattern: Avoid global scope pollution
- Private methods: `#methodName` convention
- Event-driven: Custom elements for communication
- Async/await: Modern promise handling

---

## 🎯 Extension Points

### Adding New Settings
1. Add to schema with appropriate `visible_if` conditions
2. Update CSS variables in `{% stylesheet %}` tags
3. Add Liquid logic to handle new setting
4. Test across all conditional states

### Navigation Blocks
1. Create new block type in schema
2. Add corresponding `{% case %}` in Liquid
3. Implement mobile navigation variant
4. Add appropriate styling

### Sticky Modes
1. Add new option to `sticky_mode` select
2. Implement JavaScript behavior
3. Add CSS classes for styling
4. Update conditional logic for related settings

---

## 🐛 Debugging Notes

### Common Issues
- **Z-index conflicts**: Adjust header z-index setting
- **Mobile menu not closing**: Check event listeners
- **Sticky not working**: Verify CSS variables are set
- **Animation not smooth**: Check hardware acceleration

### Development Tools
- `shopify theme dev`: Local development
- `shopify theme check`: Validation
- Browser DevTools: Performance profiling
- Accessibility testing: Screen readers, keyboard nav

### Testing Checklist
- [ ] All sticky modes function correctly
- [ ] Mobile navigation works on touch devices
- [ ] Conditional settings show/hide properly
- [ ] Performance: No layout thrashing
- [ ] Accessibility: Keyboard and screen reader support
- [ ] Cross-browser: Modern browser compatibility

---

## 📋 Maintenance

### Regular Tasks
- Monitor theme check updates
- Test new Shopify features
- Update documentation for new features
- Performance audits

### Version Control
- Semantic versioning
- Clear commit messages
- Feature branch workflow
- Changelog maintenance

---

---

## 📨 Newsletter Popup — Inert Logic Postmortem (2025-08)

### Summary
- Symptom: Popup appears but is not clickable, close button/backdrop don’t work, input can’t receive focus/typing.
- Root cause: When opening the modal, we inerted all <body> children except the custom element itself or its .shopify-section wrapper. In many themes, the popup’s section is nested inside a higher container (e.g., #MainContent) which is the actual direct child of <body>. Inerting that container also inerted the popup subtree, making it “dead”.

### Root Cause Details
- In Shopify, sections often live under a top-level <body> child (e.g., #MainContent, drawers, etc.).
- Our first attempt excluded only `.shopify-section` which can be several levels down.
- Result: the real <body> child (the host container) was inerted → popup lost interactivity.

### Final Fix
- In `assets/newsletter-popup.js` open(): determine the host container as the top-level body child that contains the popup, then inert/hide all other body children.
- Snippet:

```js
// Find the top-level <body> child that contains this popup and keep it interactive
let hostRoot = this.closest('body > *');
if (!hostRoot) hostRoot = document.body; // ultra-rare fallback

if (HAS_INERT) {
  Array.from(document.body.children).forEach(el => {
    if (el === hostRoot) return;
    if (!el.hasAttribute('data-nlp-inert')) {
      el.inert = true;
      el.setAttribute('data-nlp-inert', '1');
    }
  });
} else {
  Array.from(document.body.children).forEach(el => {
    if (el === hostRoot) return;
    if (!el.hasAttribute('data-nlp-hidden')) {
      el.setAttribute('aria-hidden', 'true');
      el.setAttribute('data-nlp-hidden', '1');
    }
  });
}
```

- Also scoped backdrop opacity CSS var to the component in the clean variant:

```js
this.style.setProperty('--backdrop', this.dataset.overlayOpacity || '40');
```

### Why this is robust
- Theme-agnostic: Works regardless of whether the popup sits under #MainContent, custom wrappers, or other top-level containers.
- Supports multiple popups (still guards against multiple open dialogs) and maintains background interactivity rules.

### Verification Steps
1. In console: `document.querySelector('newsletter-popup')?.open();`
2. Inspect: `[...document.body.children].map(el => [el.id || el.tagName, el.hasAttribute('data-nlp-inert')]);`
   - Expect: All body children inerted except the one that contains the popup.
3. Interactions: backdrop click, ✕ button, Escape key close; input accepts typing; Tab traps focus.

### Gotchas to avoid in future
- Don’t inert `.shopify-section` or the custom element alone; always compute the actual top-level host under `body`.
- If you add new top-level overlays (drawers, modals), ensure z-index and inert interactions don’t conflict.
- Keep ARIA fallbacks for environments without `inert` support.

### Status
- Implemented in `assets/newsletter-popup.js` and aligned `assets/newsletter-popup-clean.js` for parity.
- Liquid/markup unchanged.



## 🎯 Collection Card Color Swatches — Comprehensive Normalization (2025-08)

### Symptoms
- Hovering color swatches on collection cards caused a brief size “pop” (bigger → back).
- First swatch occasionally triggered a no-op crossfade that looked like a pop.
- Reverting on card-leave sometimes looked like a second snap.

### Root Causes
1) Preview layer had sources at render time, so the browser chose a candidate independent of the base image before any hover.
2) Hover logic sometimes “committed” the preview to the base, causing another candidate selection and visual jump.
3) Same-image hovers (first swatch matching the base) still crossfaded due to naive change detection.

### Final Approach
- Keep preview empty at render; JS owns its sources.
- Use an overlay-only crossfade on hover; do not swap the base image during hover.
- Bind hover to label.swatch only, not hidden inputs.
- Real change detection: compare against baseImg.currentSrc and bail if identical.
- Small grace on pointerleave to avoid mid-card revert when moving between dots.

### Implemented Changes
- File: `assets/variant-hover.js`
  - Bind only to `.swatch[data-variant-id]`.
  - Ensure preview starts empty and clear width/height on show:
    ```js
    this.previewImg.removeAttribute('width');
    this.previewImg.removeAttribute('height');
    this.previewImg.hidden = false;
    this.previewImg.style.opacity = '0';
    requestAnimationFrame(() => { this.previewImg.style.opacity = '1'; });
    ```
  - Compare to the real rendered source and no-op when same:
    ```js
    const current = this.baseImg.currentSrc || this.baseImg.src || '';
    const normalize = (u) => u ? u.replace(/([?&])width=\d+/, '$1').replace(/[?&]$/, '') : u;
    if ((variant.src && normalize(current) === normalize(variant.src)) ||
        (variant.srcset && this.baseImg.srcset === variant.srcset)) return;
    ```
  - Do not commit hovered sources into the base during hover; only show/hide the preview.
  - Add a small revert delay (80ms) on card-leave.

- File: `snippets/card-product.liquid`
  - Preview tag renders empty:
    ```liquid
    <img class="card-media__preview" alt="" aria-hidden="true" hidden>
    ```
  - Variant srcset for swatches mirrors the card’s widths_attr to avoid candidate mismatch.

### Optional (merchant-controlled default)
- To align the initial card image with the first color swatch in the merchant’s variant order, compute `card_media` in Liquid and render the base from it. This is kept separate from hover logic to avoid regressions. Suggested pattern:
  ```liquid
  assign card_media = product.featured_media
  if color_option and color_option.values.size > 0
    assign first_color = color_option.values | first
    assign chosen_variant = nil
    assign chosen_pos = 9999
    for v in product.variants
      assign v_color = v.option1
      if color_option.position == 2
        assign v_color = v.option2
      elsif color_option.position == 3
        assign v_color = v.option3
      endif
      if v.available and v_color == first_color and v.position < chosen_pos
        assign chosen_variant = v
        assign chosen_pos = v.position
      endif
    endfor
    if chosen_variant and chosen_variant.featured_media
      assign card_media = chosen_variant.featured_media
    endif
  endif
  ```

### QA Checklist
- Hover second swatch: smooth fade, no size jump.
- Hover first swatch (same as base): no fade.
- Move between dots inside the card: no revert until pointer leaves card; one fade back on leave.
- At different grid breakpoints (2, 3, 4+ columns), behavior remains stable.

### Notes
- Keep widths_attr/sizes identical for base and preview sources.
- Avoid width/height attributes on preview; let CSS control sizing.
- If you change how the default card image is chosen, keep it in Liquid; do not re-introduce base-swapping in JS.

## 🔮 Future Improvements

### Potential Enhancements
- Color scheme settings
- Typography controls
- Search integration
- Cart drawer integration
- Wishlist functionality

### Technical Debt
- Refactor legacy navigation fallback
- Optimize JavaScript bundle
- Enhance accessibility features
- Add more unit tests

---

## 🖼️ Collection Media Highlights — Mixed Content Grid System (2025-09)

### Feature Overview
Implementation of sophisticated 2-column media blocks that can be inserted at specific positions within product grids on collection pages. Provides luxury e-commerce sites the ability to break up product listings with branded content, videos, or promotional images.

### Business Requirements Met
- **Editor-driven customization**: No hard-coding, all content managed through theme editor
- **Professional-grade implementation**: Enterprise-level code quality with proper error handling
- **Responsive design**: Works seamlessly across all screen sizes and breakpoints
- **Height consistency**: Media cards match product card heights exactly for perfect grid alignment

### Technical Implementation

#### Schema Architecture
```json
{
  "type": "media_highlight",
  "name": "Media highlight",
  "settings": [
    {
      "type": "range",
      "id": "grid_position",
      "min": 0,
      "max": 50,
      "step": 1,
      "label": "Grid position",
      "default": 0,
      "info": "Position in the grid where this media should appear. 0 = end of collection. This media block will span 2 columns."
    }
  ]
}
```

#### Files Created/Modified

**`snippets/card-media.liquid` (NEW)**
- 164-line comprehensive media card component
- Supports images and videos with responsive sizing
- Overlay content with customizable positioning  
- Accessibility features with proper alt text and ARIA labels
- Uses same aspect ratio system as product cards for height consistency

**`sections/collection.liquid` (ENHANCED)**
- Advanced grid positioning logic for media insertion
- 95-line schema with comprehensive editor controls
- Responsive CSS grid system with dynamic column spanning
- Proper Liquid syntax (converted from liquid blocks to standard tags)

#### Grid Positioning Logic
```liquid
{%- comment -%} Check if media block should be inserted before this product {%- endcomment -%}
{%- assign media_block_to_insert = null -%}
{%- for block in section.blocks -%}
  {%- if block.type == 'media_highlight' -%}
    {%- assign target_position = block.settings.grid_position | default: 0 -%}
    {%- if target_position == grid_index and media_block_to_insert == null -%}
      {%- assign media_block_to_insert = block -%}
      {%- assign grid_index = grid_index | plus: 2 -%}
      {%- break -%}
    {%- endif -%}
  {%- endif -%}
{%- endfor -%}
```

#### Height Consistency Solution
The key breakthrough was ensuring media cards use the same aspect ratio as product cards:

```liquid
{%- comment -%} Use the same aspect ratio as product cards for consistent heights {%- endcomment -%}
{%- assign card_aspect = card_image_aspect_ratio | default: '3/4' -%}

{%- case card_aspect -%}
  {%- when '1/1' -%}
    {%- assign aspect_value = '1 / 1' -%}
  {%- when '3/4' -%}
    {%- assign aspect_value = '3 / 4' -%}
  {%- when '4/5' -%}
    {%- assign aspect_value = '4 / 5' -%}
  {%- else -%}
    {%- assign aspect_value = '3 / 4' -%}
{%- endcase -%}
```

#### CSS Variables Integration
```css
.media-card__container {
  position: relative;
  width: 100%;
  aspect-ratio: var(--card-aspect, 3 / 4);
  overflow: hidden;
}

.media-card__image-element,
.media-card__video-element {
  object-position: var(--card-object-position, center);
}
```

### Critical Bug Fixes

#### 1. JSON Schema Unit Property Issue
**Problem**: "Unknown tag 'media'" error during upload
**Root Cause**: Empty `"unit": ""` property in range settings is invalid in Shopify
**Solution**: Removed empty unit property from grid_position range setting
```json
{
  "type": "range",
  "id": "grid_position",
  "min": 0,
  "max": 50,
  "step": 1,
  "label": "Grid position",
  "default": 0
  // "unit": "" ← REMOVED: Empty string not allowed
}
```

#### 2. Liquid Syntax Parameter Naming
**Problem**: `media:` parameter name interpreted as Liquid tag
**Root Cause**: Reserved word conflict in Liquid templating
**Solution**: Renamed parameter from `media:` to `media_settings:`
```liquid
{%- render 'card-media', 
    media_settings: block.settings,
    card_image_aspect_ratio: section.settings.card_image_aspect_ratio -%}
```

#### 3. Liquid Block vs Standard Syntax
**Problem**: Complex render statements failing in liquid blocks
**Root Cause**: Multi-parameter render statements require standard Liquid syntax
**Solution**: Converted from `{%- liquid` blocks to standard Liquid tags
```liquid
{%- assign target_position = block.settings.grid_position | default: 0 -%}
{%- if target_position > total_products or target_position == 0 -%}
  {%- render 'card-media', 
      media_settings: block.settings,
      card_image_aspect_ratio: section.settings.card_image_aspect_ratio -%}
{%- endif -%}
```

### Editor Controls Available
- **Media Content**: Image/video selection with alt text
- **Positioning**: Precise grid position control (1-50)
- **Overlay Content**: Heading, description, and call-to-action button
- **Styling**: Background colors, text colors, content alignment
- **Layout**: Uses collection's aspect ratio setting for height consistency

### Performance Optimizations
- ✅ Lazy loading for media content
- ✅ Responsive image srcsets with proper sizes
- ✅ Hardware-accelerated CSS animations
- ✅ Proper video optimization with poster images

### Theme Check Compliance
- ✅ Passes Shopify Theme Check validation
- ✅ Only expected warnings for remote video assets
- ✅ Proper accessibility attributes
- ✅ Mobile-first responsive design

### Usage Instructions
1. Navigate to collection template in theme editor
2. Add "Media highlight" block
3. Set grid position (1-50, or 0 for end of collection)
4. Upload image or video
5. Configure overlay content and styling
6. Media block will span 2 columns and match product card heights

### QA Verification Steps
- [ ] Media blocks appear at correct grid positions
- [ ] Heights match surrounding product cards exactly
- [ ] Responsive behavior works across all breakpoints
- [ ] Overlay content displays properly
- [ ] Video controls function correctly
- [ ] Accessibility features work with screen readers

### Future Enhancement Opportunities
- Support for additional media types (GIFs, external videos)
- Animation options for overlay content
- Advanced positioning controls (span variations)
- A/B testing integration for content optimization

### Technical Lessons Learned
1. **Schema Validation**: Empty unit properties cause upload failures
2. **Parameter Naming**: Avoid reserved words in Liquid render parameters  
3. **Syntax Complexity**: Multi-parameter renders need standard Liquid syntax
4. **Height Consistency**: Shared CSS variables ensure perfect grid alignment
5. **Editor Experience**: Comprehensive schema creates professional UX

### Status: Production Ready ✅
The mixed content grid system provides enterprise-level functionality for sophisticated collection page layouts. All critical bugs resolved, height consistency achieved, and full editor control implemented.
