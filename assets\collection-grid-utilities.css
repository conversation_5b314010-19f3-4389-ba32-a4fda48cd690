/* Collection Grid Color Swatch Utilities */

/* Card Hover Overlay Styles */
.card-hover-overlay {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: auto;
  background: rgba(255, 255, 255, 0.95);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.25s ease;
  pointer-events: none;
  padding: 1rem;
}

.card-hover-overlay:hover {
  opacity: 1;
  pointer-events: auto;
}

.card-hover-overlay__content {
  background: transparent;
  border-radius: 0;
  padding: 0.5rem 0;
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Color swatches in overlay - Match screenshot style */
.overlay-colors {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  order: 1;
  margin-bottom: 0.25rem;
}

.overlay-colors .card__swatches {
  display: flex;
  gap: 0.5rem;
  margin: 0;
  justify-content: center;
}

.overlay-colors .swatch {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.overlay-colors .swatch:hover {
  transform: scale(1.15);
  border-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.overlay-colors .swatch {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.overlay-colors .swatch:hover {
  transform: scale(1.15);
  border-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Size options in overlay - Match screenshot */
.overlay-sizes {
  display: flex;
  justify-content: center;
  gap: 0.375rem;
  flex-wrap: wrap;
  order: 2;
  margin-bottom: 0.25rem;
}

.size-btn {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 0.4rem 0.6rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 400;
  font-size: 0.8rem;
  transition: all 0.2s ease;
  min-width: 2rem;
  color: #333;
}

.size-btn:hover {
  background: #f5f5f5;
  border-color: rgba(0, 0, 0, 0.3);
}

/* Quick add button in overlay - Match screenshot */
.overlay-quick-add {
  background: black;
  color: white;
  border: none;
  padding: 0.6rem 1.5rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  order: 3;
}

.overlay-quick-add:hover {
  background: #333;
}

/* Show overlay on card hover */
.collection-product-grid .card-wrapper:hover .card-hover-overlay,
.collection-product-grid .card:hover .card-hover-overlay {
  opacity: 1;
  pointer-events: auto;
}

/* Hide original swatches when in hover overlay mode (but not in overlay) */
.collection-product-grid[data-swatch-mode="hover_overlay"] .card__content .card__swatches {
  display: none;
}

/* Show swatches when they're copied to overlay */
.overlay-colors .card__swatches {
  display: flex;
  visibility: visible;
  pointer-events: auto;
}

/* Show swatches below card when in below_card mode */
.collection-product-grid[data-swatch-mode="below_card"] .card__swatches {
  position: relative !important;
  left: auto !important;
  display: flex !important;
  opacity: 1 !important;
  pointer-events: auto !important;
  visibility: visible !important;
}

/* Disable overlay in below_card mode */
.collection-product-grid[data-swatch-mode="below_card"] .card-hover-overlay {
  display: none !important;
}

/* Responsive overlay */
@media screen and (max-width: 749px) {
  .card-hover-overlay__content {
    padding: 1rem;
    max-width: 90%;
  }
  
  .overlay-colors .swatch {
    width: 1.5rem;
    height: 1.5rem;
  }
  
  .size-btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.875rem;
    min-width: 2rem;
  }
  
  .overlay-quick-add {
    padding: 0.6rem 1.2rem;
    font-size: 0.875rem;
  }
}

/* Responsive swatch sizing for collection grids */
.collection-product-grid .swatch {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* Collection grid specific swatch hover effects */
.collection-product-grid .swatch:hover {
  transform: scale(1.15);
  z-index: 1;
  position: relative;
}

/* Improved swatch visibility on mobile */
@media screen and (max-width: 749px) {
  .collection-product-grid .card__swatches {
    gap: 0.75rem;
    margin: 1rem 0 0.5rem 0;
  }
  
  .collection-product-grid .swatch {
    width: 1rem;
    height: 1rem;
    min-width: 1rem;
    min-height: 1rem;
  }
  
  .collection-product-grid .swatch-more {
    width: 1rem;
    height: 1rem;
    font-size: 0.6875rem;
  }
}

/* Ensure proper card spacing when swatches are present */
.collection-product-grid .card__content {
  padding-bottom: 1.25rem;
}

/* Better positioning for sold-out indicator in grid */
.collection-product-grid .swatch-sold-out-indicator {
  margin-top: 0.25rem;
  text-align: center;
}

/* Collection grid specific color improvements */
.collection-product-grid .swatch--apricot-orange {
  background-color: #ff8040 !important;
}

/* Make sure swatches don't wrap awkwardly in narrow cards */
.collection-product-grid .card__swatches {
  max-width: 100%;
}

/* Loading state for better UX */
.collection-product-grid [data-enable-hover-preview="true"] .card-media__preview {
  transition: opacity 0.25s ease;
}

/* Ensure preview image appears on top */
.card-media__preview {
  z-index: 1;
  position: relative;
}

/* Debug styling for preview image */
.card-media__preview[style*="opacity: 1"] {
  border: 2px solid lime !important;
  box-sizing: border-box;
}

/* Force preview image to be properly positioned and on top */
.card-media__preview {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  object-position: center !important;
  z-index: 10 !important;
}

/* Ensure primary image stays behind */
.card-media__primary {
  position: absolute !important;
  z-index: 1 !important;
}

/* Striped swatch pattern for overlay */
.overlay-colors .swatch--striped {
  background: repeating-linear-gradient(
    45deg,
    #333,
    #333 2px,
    #fff 2px,
    #fff 4px
  ) !important;
}

/* Active state for swatches */
.overlay-colors .swatch--active {
  border-color: #000 !important;
  border-width: 3px !important;
  transform: scale(1.1);
}

/* Active state for size buttons */
.size-btn--active {
  background: #000 !important;
  color: white !important;
  border-color: #000 !important;
}

/* Disabled quick add button */
.overlay-quick-add:disabled {
  background: #ccc !important;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Sold out styling for overlay swatches */
.overlay-colors .swatch--sold-out {
  opacity: 0.6;
  position: relative;
}

.overlay-colors .swatch--sold-out::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 10%;
  right: 10%;
  height: 2px;
  background: #ff0000;
  transform: rotate(-45deg);
}