{% comment %}
  Main Order Section
  - Static section for customers/order template
  - Adds breadcrumbs and layout offsets to match other customer sections
{% endcomment %}

{%- liquid
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="account account--order color-{{ section.settings.color_scheme | default: 'background-1' }}" data-section="{{ section.id }}">
  <div class="order__inner page-width">
    {% render 'breadcrumbs' %}

    <h1 class="h2">{{ 'customers.order.title' | t | default: 'Order' }} {{ order.name }}</h1>
    <p><a href="{{ routes.account_url }}">{{ 'customers.order.back' | t | default: 'Back to account' }}</a></p>

    <div class="order__grid">
      <div>
        <h2 class="h3">{{ 'customers.order.summary' | t | default: 'Summary' }}</h2>
        <p>
          {{ order.created_at | date: format: 'date' }} •
          {{ order.financial_status_label }} •
          {{ order.fulfillment_status_label }}
        </p>
      </div>

      <div>
        <h2 class="h3">{{ 'customers.order.shipping_address' | t | default: 'Shipping address' }}</h2>
        <address>{{ order.shipping_address | format_address }}</address>
      </div>

      <div>
        <h2 class="h3">{{ 'customers.order.billing_address' | t | default: 'Billing address' }}</h2>
        <address>{{ order.billing_address | format_address }}</address>
      </div>

      <div>
        <h2 class="h3">{{ 'customers.order.line_items' | t | default: 'Items' }}</h2>
        <table role="table" class="order__items">
          <thead>
            <tr>
              <th>{{ 'customers.order.product' | t | default: 'Product' }}</th>
              <th>{{ 'customers.order.sku' | t | default: 'SKU' }}</th>
              <th>{{ 'customers.order.price' | t | default: 'Price' }}</th>
              <th>{{ 'customers.order.quantity' | t | default: 'Qty' }}</th>
              <th>{{ 'customers.order.total' | t | default: 'Total' }}</th>
            </tr>
          </thead>
          <tbody>
            {% for line_item in order.line_items %}
              <tr>
                <td>{{ line_item.title }}</td>
                <td>{{ line_item.sku }}</td>
                <td>{{ line_item.price | money }}</td>
                <td>{{ line_item.quantity }}</td>
                <td>{{ line_item.line_price | money }}</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</section>

{% style %}
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; } }

  #shopify-section-{{ section.id }} .order__inner { max-width: var(--page-width); margin: 0 auto; padding: 1.5rem 1rem; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} .order__inner { padding: 2rem 1.25rem; } }

  #shopify-section-{{ section.id }} .order__grid { display: grid; gap: 1.25rem; }
  #shopify-section-{{ section.id }} .order__items { width: 100%; border-collapse: collapse; }
  #shopify-section-{{ section.id }} .order__items thead tr { text-align: left; border-bottom: 1px solid #e5e7eb; }
  #shopify-section-{{ section.id }} .order__items tbody tr { border-bottom: 1px solid #f3f4f6; }
{% endstyle %}

{% schema %}
{
  "name": "Main order",
  "settings": [
    { "type": "header", "content": "Colors" },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "background-1" },
    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "presets": [ { "name": "Main order" } ]
}
{% endschema %}
