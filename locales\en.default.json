{"404": {"title": "404", "not_found": "Page not found.", "back_to_shopping": "Back to shopping"}, "blog": {"article_comments": "Comments", "article_metadata_html": "{{ date }} by {{ author }}", "comment_form_body": "Comment", "comment_form_email": "Email", "comment_form_name": "Name", "comment_form_submit": "Post", "comment_form_title": "Add a comment"}, "cart": {"checkout": "Checkout", "title": "<PERSON><PERSON>", "update": "Update", "remove": "Remove", "item_count": "items in cart", "general": {"total": "Total", "view_cart": "View Cart", "checkout": "Checkout"}}, "customers": {"login": {"email": "Email", "password": "Password", "submit": "Sign in", "title": "<PERSON><PERSON>"}, "password": {"recover": "Recover password", "success": "We sent you an email with a link to reset your password.", "email": "Email", "submit": "Send reset link"}, "register": {"title": "Create account", "first_name": "First name", "last_name": "Last name", "email": "Email", "password": "Password", "submit": "Create"}, "reset": {"title": "Reset password", "subtext": "Enter a new password for your account.", "password": "Password", "password_confirm": "Confirm password", "submit": "Reset"}, "activate": {"title": "Activate account", "subtext": "Create your password to activate your account.", "password": "Password", "password_confirm": "Confirm password", "submit": "Activate", "cancel": "Decline"}, "addresses": {"title": "Addresses", "back": "Back to account", "add_new": "Add a new address", "new": "New address", "first_name": "First name", "last_name": "Last name", "company": "Company", "address1": "Address line 1", "address2": "Address line 2", "city": "City", "country": "Country", "province": "Province/State", "zip": "Postal/ZIP code", "phone": "Phone", "set_default": "Set as default address", "save": "Save", "default": "<PERSON><PERSON><PERSON>", "edit": "Edit address", "delete": "Delete", "update": "Update"}, "account": {"title": "Account", "details": "Account details", "view_addresses": "View addresses", "orders": "Orders", "no_orders": "You have no orders yet.", "order": "Order", "date": "Date", "payment": "Payment", "fulfillment": "Fulfillment", "total": "Total", "logout": "Log out"}, "order": {"title": "Order", "back": "Back to account", "summary": "Summary", "shipping_address": "Shipping address", "billing_address": "Billing address", "line_items": "Items", "product": "Product", "sku": "SKU", "price": "Price", "quantity": "Qty", "total": "Total"}}, "collections": {"title": "Collections", "general": {"no_matches": "No products match your search", "no_products_html": "There are no products in this collection yet."}}, "gift_card": {"add_to_apple_wallet": "Add to Apple Wallet", "card": "Gift card", "expired": "This gift card has expired", "expires_on": "Expires on {{ expires_on }}", "use_at_checkout": "Use this gift card at checkout"}, "password": {"title": "This shop is private", "password": "Password", "enter": "Enter"}, "search": {"title": "Search", "placeholder": "Search articles, pages, or products", "submit": "Search", "no_results_html": "No results found for {{ terms }}", "results_for_html": "{{ count }} results found for {{ terms }}"}, "products": {"product": {"add_to_cart": "Add to cart", "sold_out": "Sold out", "include_taxes": "Taxes included.", "quantity": "Quantity", "description": "Description", "notify_me_message": "Notify me about: {{ product_title }}", "pickup_availability": "Pickup availability", "pickup_unavailable": "Currently unavailable for pickup", "badge_new": "NEW", "color": "Color", "in_stock": "In Stock", "out_of_stock": "Out of Stock", "details": "Product Details", "size_fit": "Size & Fit", "material_care": "Material & Care", "size_guide": "Size Guide", "delivery_returns": "Delivery & Returns", "add_to_wishlist": "Add to wishlist"}, "facets": {"no_results": "No recommendations."}}, "store_location": {"get_directions": "Get directions", "copy_address": "Copy address", "copied": "Copied!"}, "slider": {"label": "Slide<PERSON>", "previous": "Previous", "next": "Next"}, "accessibility": {"unit_price_separator": "per", "close": "Close", "slider_pagination": "Slide pagination", "slide_n_of_m": "Slide {{ index }} of {{ total }}", "go_to_slide": "Go to slide"}, "general": {"close": "Close", "breadcrumbs": {"home": "Home"}, "learn_more": "Learn more", "typography": "Typography", "fonts": "Fonts", "primary": "Primary font", "layout": "Layout", "colors": "Colors", "color_schemes": "Color schemes", "header": {"logo": "Logo", "logo_width": "Logo width (px)", "logo_mobile": "Mobile logo"}, "announcement": {"title": "Announcement", "close": "Close announcement", "go_to_announcement": "Go to announcement"}, "cart": {"item_count": "items in cart"}}, "sections": {"product": {"free_standard_over": "Free standard shipping over {{ threshold }}", "easy_returns": "Easy returns"}}, "settings_schema": {"colors": {"settings": {"background": {"label": "Background"}, "background_gradient": {"label": "Background gradient"}, "text": {"label": "Text"}, "accent_1": {"label": "Accent 1"}, "accent_2": {"label": "Accent 2"}, "outline_button_label": {"label": "Outline button"}, "solid_button_background": {"label": "Solid button background"}, "solid_button_label": {"label": "Solid button label"}}}}, "labels": {"page_width": "Page width", "page_margin": "Page margin", "background": "Background", "foreground": "Foreground", "input_corner_radius": "Input corner radius"}, "options": {"page_width": {"narrow": "Narrow (1440px)", "wide": "Wide (1760px)"}}, "faq": {"link_label": "FAQ", "categories": "FAQ categories", "copy_link": "Copy link", "copied": "Copied!"}}