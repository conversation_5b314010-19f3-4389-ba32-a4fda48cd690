{%- comment -%}
  Debug helper for swatch troubleshooting
  Add this to templates to see what color options are available
{%- endcomment -%}

<div style="background: #f0f0f0; padding: 1rem; margin: 1rem 0; border: 1px solid #ccc;">
  <h4>Debug: Product Colors</h4>
  
  {% for product in collections.all.products limit: 3 %}
    <div style="margin: 0.5rem 0;">
      <strong>{{ product.title }}:</strong>
      {% for option in product.options_with_values %}
        {% assign option_name = option.name | downcase %}
        {% if option_name contains 'color' or option_name contains 'colour' %}
          <br>Color Option "{{ option.name }}": 
          {% for value in option.values %}
            <span style="background: #e0e0e0; padding: 2px 6px; margin: 2px; border-radius: 3px;">{{ value }}</span>
          {% endfor %}
        {% endif %}
      {% endfor %}
      
      {% comment %} Show first option if no color found {% endcomment %}
      {% if product.options_with_values.size == 1 %}
        {% assign first_option = product.options_with_values[0] %}
        {% unless first_option.name contains 'color' or first_option.name contains 'colour' %}
          <br>First Option "{{ first_option.name }}": 
          {% for value in first_option.values %}
            <span style="background: #ffe0e0; padding: 2px 6px; margin: 2px; border-radius: 3px;">{{ value }}</span>
          {% endfor %}
        {% endunless %}
      {% endif %}
    </div>
  {% endfor %}
</div>
