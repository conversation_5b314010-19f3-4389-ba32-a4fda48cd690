{% comment %}
  Main Reset Password Section
  - Static section for customers/reset_password template
  - Mirrors login/register styling: optional images, button style, colors, offsets
{% endcomment %}

{%- liquid
  assign big_image = section.settings.big_image
  assign small_image = section.settings.small_image
  assign button_style = section.settings.button_style | default: 'primary'

  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="reset color-{{ section.settings.color_scheme | default: 'background-1' }}" data-section="{{ section.id }}">
  <div class="reset__inner page-width">
    {% render 'breadcrumbs' %}

    <div class="reset__grid">
      <div class="reset__form">
        <h1 class="reset__heading h2">{{ 'customers.reset.title' | t | default: 'Reset password' }}</h1>
        <p class="reset__subtext">{{ 'customers.reset.subtext' | t | default: 'Enter a new password for your account.' }}</p>

        {% form 'reset_customer_password' %}
          {% if form.errors %}
            <div class="reset__errors" role="alert">{{ form.errors | default_errors }}</div>
          {% endif %}

          <div class="field">
            <label for="ResetPassword-{{ section.id }}">{{ 'customers.reset.password' | t | default: 'Password' }}</label>
            <input type="password" name="customer[password]" id="ResetPassword-{{ section.id }}" autocomplete="new-password" required>
          </div>

          <div class="field">
            <label for="ResetPasswordConfirm-{{ section.id }}">{{ 'customers.reset.password_confirm' | t | default: 'Confirm password' }}</label>
            <input type="password" name="customer[password_confirmation]" id="ResetPasswordConfirm-{{ section.id }}" autocomplete="new-password" required>
          </div>

          <div class="reset__actions">
            {% assign btn_class = 'button button--primary' %}
            {% if button_style == 'secondary' %}
              {% assign btn_class = 'button button--secondary' %}
            {% elsif button_style == 'outline' or button_style == 'secondary_outline' %}
              {% assign btn_class = 'button button--secondary button--outline' %}
            {% elsif button_style == 'primary_outline' %}
              {% assign btn_class = 'button button--primary button--outline' %}
            {% endif %}
            <button type="submit" class="{{ btn_class }}">{{ 'customers.reset.submit' | t | default: 'Reset' }}</button>
          </div>
        {% endform %}
      </div>

      <div class="reset__media">
        {% if big_image or small_image %}
          <div class="reset__media-grid{% if big_image and small_image %} has-both{% endif %}">
            {% if big_image %}
              <div class="reset__media-big">
                {{ big_image | image_url: width: 2000 | image_tag: widths: '800, 1200, 1600, 2000', sizes: '(min-width: 990px) 50vw, 100vw', loading: 'lazy', decoding: 'async', alt: '' }}
              </div>
            {% endif %}
            {% if small_image %}
              <div class="reset__media-small">
                {{ small_image | image_url: width: 1200 | image_tag: widths: '600, 800, 1000, 1200', sizes: '(min-width: 990px) 25vw, 100vw', loading: 'lazy', decoding: 'async', alt: '' }}
              </div>
            {% endif %}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</section>

{% style %}
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; } }

  #shopify-section-{{ section.id }} .reset {
    background: {{ section.settings.section_bg | default: 'transparent' | color_background }};
    color: {{ section.settings.text_color | default: 'inherit' }};
  }
  #shopify-section-{{ section.id }} .reset__inner { padding: 1.5rem 1rem; max-width: var(--page-width); margin: 0 auto; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} .reset__inner { padding: 2rem 1.25rem; } }

  #shopify-section-{{ section.id }} .reset__grid { display: grid; gap: 1.25rem; align-items: start; }
  @media (min-width: 990px){ #shopify-section-{{ section.id }} .reset__grid { grid-template-columns: 1fr 1fr; } }

  #shopify-section-{{ section.id }} .reset__heading { margin: 0 0 .5rem; }
  #shopify-section-{{ section.id }} .reset__subtext { margin: 0 0 .75rem; opacity: .85; }
  #shopify-section-{{ section.id }} .reset__actions { display: flex; gap: .75rem; align-items: center; flex-wrap: wrap; }
  #shopify-section-{{ section.id }} .reset__errors { color: #c00; margin-bottom: .75rem; }

  #shopify-section-{{ section.id }} .reset__media-grid { display: grid; gap: .75rem; }
  #shopify-section-{{ section.id }} .reset__media-grid.has-both { grid-template-rows: 2fr 1fr; }
  #shopify-section-{{ section.id }} .reset__media-big img,
  #shopify-section-{{ section.id }} .reset__media-small img { width: 100%; height: auto; display: block; border-radius: 10px; }
{% endstyle %}

{% schema %}
{
  "name": "Main reset password",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "image_picker", "id": "big_image", "label": "Big image" },
    { "type": "image_picker", "id": "small_image", "label": "Small image" },
    { "type": "select", "id": "button_style", "label": "Button style", "default": "primary", "options": [
      { "value": "primary", "label": "Primary" },
      { "value": "secondary", "label": "Secondary" },
      { "value": "primary_outline", "label": "Primary with border" },
      { "value": "secondary_outline", "label": "Secondary with border" },
      { "value": "outline", "label": "Outline (secondary)" }
    ] },

    { "type": "header", "content": "Colors" },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "background-1" },
    { "type": "color_background", "id": "section_bg", "label": "Section background" },
    { "type": "color", "id": "text_color", "label": "Text" },

    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "presets": [ { "name": "Main reset password" } ]
}
{% endschema %}
