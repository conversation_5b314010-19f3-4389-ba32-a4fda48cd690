/* Newsletter Popup styles (marketplace-grade)
   - No inline styles, CSS vars for editor-driven controls
   - Backdrop opacity via --backdrop (set by J<PERSON> from data attribute)
*/

html.no-scroll { overflow: hidden; }

.nlp { 
  position: fixed; 
  inset: 0; 
  display: none; 
  z-index: 9999; 
  padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left); 
}
.nlp[open] { display: block; }

.nlp__backdrop { 
  position: absolute; 
  inset: 0; 
  background: rgb(0 0 0 / 0.6); 
  opacity: calc(var(--backdrop, 40) / 100); 
}

.nlp__dialog {
  position: relative; 
  z-index: 1;
  max-width: 720px; 
  margin: 8vh auto; 
  background: var(--color-background);
  border-radius: 12px; 
  overflow: hidden;
  display: grid; 
  grid-template-columns: 1fr 1.2fr;
  transform: translateY(8px) scale(.98); 
  opacity: 0;
  transition: transform .2s ease, opacity .2s ease;
}

/* Image top layout */
.nlp__dialog--image_top {
  grid-template-columns: 1fr;
  max-width: 480px;
}

.nlp[open] .nlp__dialog { 
  transform: none; 
  opacity: 1; 
}

@media (max-width: 749px) {
  .nlp__dialog { 
    grid-template-columns: 1fr; 
    margin: 4vh 1rem; 
  }
}

.nlp__media { 
  min-height: 220px; 
}
.nlp__media--image_top {
  order: -1;
  min-height: 180px;
}
.nlp__media--image_left { 
  order: 0; 
}
.nlp__media--image_right { 
  order: 1; 
}
.nlp__media img { 
  width: 100%; 
  height: 100%; 
  object-fit: cover; 
  display: block; 
}

.nlp__content { 
  padding: 2rem 1.5rem; 
  display: grid; 
  gap: 1rem;
  color: var(--color-foreground, #121212);
  text-align: center;
}
.nlp__heading { 
  margin: 0;
  color: var(--color-foreground, #121212);
  font-size: 2rem;
  font-weight: 400;
  line-height: 1.2;
}
.nlp__sub { 
  opacity: 0.8;
  color: var(--color-foreground-75, rgba(18, 18, 18, 0.75));
  font-size: 0.95rem;
  line-height: 1.4;
}

.nlp__form { 
  display: grid; 
  grid-template-columns: 1fr; 
  gap: 0.75rem; 
  align-items: center;
  margin-top: 1rem;
}

.nlp__form input[type="email"] {
  padding: 1rem 1.25rem;
  border: 2px solid rgba(var(--color-foreground-rgb, 18, 18, 18), 0.2);
  border-radius: 6px;
  font-size: 1rem;
  background: var(--color-background, #ffffff);
  color: var(--color-foreground, #121212);
  text-align: center;
  min-height: 48px;
  box-sizing: border-box;
}

.nlp__form input[type="email"]::placeholder {
  color: rgba(var(--color-foreground-rgb, 18, 18, 18), 0.5);
  font-style: italic;
}

.nlp__form input[type="email"]:focus {
  outline: 2px solid var(--color-accent-1, #121212);
  outline-offset: 2px;
  border-color: var(--color-accent-1, #121212);
}

.nlp__form button {
  padding: 1rem 2rem;
  white-space: nowrap;
  background: var(--color-button, #121212);
  color: var(--color-button-label, #ffffff);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  text-transform: none;
  min-height: 48px;
  box-sizing: border-box;
}

.nlp__form button:hover {
  opacity: 0.9;
}

.nlp__form button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.nlp__close {
  position: absolute; 
  top: .5rem; 
  right: .5rem;
  font-size: 1.25rem; 
  background: transparent; 
  border: 0; 
  cursor: pointer; 
  line-height: 1;
  z-index: 2;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-foreground, #121212);
}

.nlp__close:hover {
  background: var(--color-foreground, #121212);
  color: var(--color-background, #ffffff);
  border-radius: 50%;
}

.nlp__success {
  padding: 1rem;
  background: rgba(var(--color-accent-2-rgb, 51, 75, 180), 0.1);
  color: var(--color-accent-2, #334fb4);
  border-radius: 6px;
  border: 1px solid rgba(var(--color-accent-2-rgb, 51, 75, 180), 0.2);
}

.visually-hidden {
  position: absolute; 
  width: 1px; 
  height: 1px; 
  padding: 0; 
  margin: -1px; 
  overflow: hidden;
  clip: rect(0 0 0 0); 
  white-space: nowrap; 
  border: 0;
}

@media (prefers-reduced-motion: reduce) {
  .nlp__dialog { 
    transition: none; 
  }
}
