{% comment %}
  Password page footer.
  Simple social links and copyright.
{% endcomment %}

{% style %}
  .password-footer { padding: 2rem 0 3rem; text-align: center; }
  .password-footer__inner { display: grid; gap: 0.75rem; }
  .password-footer__socials { display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; }
  .password-footer__socials a { text-decoration: none; opacity: 0.8; }
  .password-footer__socials a:hover { opacity: 1; text-decoration: underline; }
  .password-footer__copy { font-size: 0.875rem; opacity: 0.8; }
  .password-footer__heading { font-weight: 600; }
{% endstyle %}

<footer class="password-footer" data-section="{{ section.id }}">
  <div class="password-footer__inner page-width">
    {% if section.settings.show_socials %}
      {% assign has_social = false %}
      {% if settings.social_facebook or settings.social_instagram or settings.social_tiktok or settings.social_pinterest or settings.social_youtube or settings.social_twitter or settings.social_linkedin %}
        {% assign has_social = true %}
      {% endif %}
      {% if has_social %}
        {% if section.settings.social_heading != blank %}
          <div class="password-footer__heading">{{ section.settings.social_heading }}</div>
        {% endif %}
        <div class="password-footer__socials">
          {% if settings.social_facebook %}<a href="{{ settings.social_facebook }}" target="_blank" rel="noopener">Facebook</a>{% endif %}
          {% if settings.social_instagram %}<a href="{{ settings.social_instagram }}" target="_blank" rel="noopener">Instagram</a>{% endif %}
          {% if settings.social_tiktok %}<a href="{{ settings.social_tiktok }}" target="_blank" rel="noopener">TikTok</a>{% endif %}
          {% if settings.social_pinterest %}<a href="{{ settings.social_pinterest }}" target="_blank" rel="noopener">Pinterest</a>{% endif %}
          {% if settings.social_youtube %}<a href="{{ settings.social_youtube }}" target="_blank" rel="noopener">YouTube</a>{% endif %}
          {% if settings.social_twitter %}<a href="{{ settings.social_twitter }}" target="_blank" rel="noopener">Twitter/X</a>{% endif %}
          {% if settings.social_linkedin %}<a href="{{ settings.social_linkedin }}" target="_blank" rel="noopener">LinkedIn</a>{% endif %}
        </div>
      {% endif %}
    {% endif %}

    <div class="password-footer__copy">
      &copy; {{ 'now' | date: '%Y' }} {{ shop.name }}
    </div>
  </div>
</footer>

{% schema %}
{
  "name": "Password footer",
  "settings": [
    { "type": "checkbox", "id": "show_socials", "label": "Show social links", "default": true },
    { "type": "text", "id": "social_heading", "label": "Social heading", "default": "Follow us" }
  ]
}
{% endschema %}
