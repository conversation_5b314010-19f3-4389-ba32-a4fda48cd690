<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swatch Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { max-width: 300px; margin: 0 auto; }
        /* Import our swatch styles */
        .card__badge { position: relative; }
        .card__media { position: relative; }
        .card__content { padding: 10px 0; }
        .card__information { text-align: center; }
        .card__heading { font-size: 16px; margin: 10px 0; }
        .card-swatch-container { display: flex; gap: 5px; justify-content: center; margin: 10px 0; }
        .card-swatch { 
            width: 20px; 
            height: 20px; 
            border-radius: 50%; 
            border: 1px solid #ccc; 
            cursor: pointer; 
            transition: transform 0.2s;
        }
        .card-swatch:hover { transform: scale(1.1); }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>Swatch Hover Test</h2>
        
        <!-- Simulate a product card structure -->
        <div class="card">
            <div class="card__media">
                <img src="https://via.placeholder.com/300x400/FF6B35/FFFFFF?text=Orange+Product" 
                     alt="Test Product" 
                     style="width: 100%; height: auto;">
                
                <!-- Preview image layer -->
                <img class="card-preview-image" 
                     src="" 
                     alt=""
                     style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; opacity: 0; transition: opacity 0.3s ease; z-index: 2;">
            </div>
            
            <div class="card__content">
                <div class="card__information">
                    <h3 class="card__heading">Test Product with Swatches</h3>
                    
                    <!-- Color swatches -->
                    <div class="card-swatch-container" data-variant-map="1:img1:orange;2:img2:blue;3:img3:green;">
                        <button class="card-swatch" 
                                style="background-color: #FF6B35;"
                                data-variant-id="1"
                                data-color="orange"
                                data-image="https://via.placeholder.com/300x400/FF6B35/FFFFFF?text=Orange"
                                title="Orange">
                            <span class="visually-hidden">Orange</span>
                        </button>
                        
                        <button class="card-swatch" 
                                style="background-color: #4A90E2;"
                                data-variant-id="2"
                                data-color="blue"
                                data-image="https://via.placeholder.com/300x400/4A90E2/FFFFFF?text=Blue"
                                title="Blue">
                            <span class="visually-hidden">Blue</span>
                        </button>
                        
                        <button class="card-swatch" 
                                style="background-color: #7ED321;"
                                data-variant-id="3"
                                data-color="green"
                                data-image="https://via.placeholder.com/300x400/7ED321/FFFFFF?text=Green"
                                title="Green">
                            <span class="visually-hidden">Green</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load our swatch JavaScript -->
    <script>
        // Simple version of our swatch hover functionality
        document.addEventListener('DOMContentLoaded', function() {
            const swatches = document.querySelectorAll('.card-swatch');
            const previewImage = document.querySelector('.card-preview-image');
            
            console.log('Found swatches:', swatches.length);
            
            swatches.forEach(swatch => {
                swatch.addEventListener('mouseenter', function() {
                    const imageUrl = this.dataset.image;
                    const color = this.dataset.color;
                    
                    console.log('Hovering over swatch:', color, 'Image:', imageUrl);
                    
                    if (imageUrl && previewImage) {
                        previewImage.src = imageUrl;
                        previewImage.alt = color + ' variant';
                        previewImage.style.opacity = '1';
                    }
                });
                
                swatch.addEventListener('mouseleave', function() {
                    console.log('Left swatch');
                    if (previewImage) {
                        previewImage.style.opacity = '0';
                    }
                });
            });
        });
    </script>
</body>
</html>
