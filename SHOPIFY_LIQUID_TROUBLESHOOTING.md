# Shopify Liquid & Schema Troubleshooting Guide

This document covers common Shopify Liquid and schema errors encountered during theme development, along with their solutions and prevention strategies.

## Table of Contents
1. [Liquid Syntax Errors](#liquid-syntax-errors)
2. [Schema Validation Errors](#schema-validation-errors)
3. [Best Practices](#best-practices)
4. [Quick Reference](#quick-reference)

---

## Liquid Syntax Errors

### 1. "Expected end_of_string but found comparison" Error

**Problem:** Complex boolean expressions in `assign` statements or filters in compound `and`/`or` conditions.

**Examples:**
```liquid
❌ Bad:
{% assign show_title = current_collection.title != blank and section.settings.show_collection_title %}
{% if section.settings.enable_filtering and collection.filters | size > 0 %}

✅ Good:
{% assign show_title = false %}
{% if current_collection.title != blank and section.settings.show_collection_title %}
  {% assign show_title = true %}
{% endif %}

{% if section.settings.enable_filtering and collection.filters != empty %}
```

**Solution:** Break complex expressions into separate conditional statements. Avoid filters like `| size` in compound boolean expressions.

### 2. "Unknown tag 'product'" Error (Multi-line Render Issues)

**Problem:** Using multi-line `render` tags inside `{% liquid %}` blocks or missing `{%` brackets.

**Examples:**
```liquid
❌ Bad:
{% liquid %}
  render 'card-product', 
    product: product,
    show_swatches: true
%}

❌ Also Bad:
render 'card-product', 
  product: product,
  show_swatches: true

✅ Good (Option A - Single line in liquid block):
{% liquid %}
  render 'card-product', product: product, show_swatches: true
%}

✅ Good (Option B - Standard tags outside liquid block):
{% render 'card-product',
  product: product,
  show_swatches: true
%}
```

**Solution:** Either use single-line renders in `{% liquid %}` blocks or use standard `{% render %}` tags outside liquid blocks.

### 3. Array Initialization Issues

**Problem:** Using `assign products = array` which isn't guaranteed in all Shopify environments.

**Examples:**
```liquid
❌ Bad:
{% assign products = array %}
{% assign filtered_products = array %}

✅ Good:
{% assign products = '' | split: ',' %}
{% assign filtered_products = '' | split: ',' %}
```

**Solution:** Always use `'' | split: ','` for empty array initialization.

### 4. Liquid Block vs Standard Tag Mixing

**Problem:** Mixing `{% liquid %}` syntax with standard Liquid tags.

**Examples:**
```liquid
❌ Bad:
{% liquid %}
  assign count = 0
  {% if condition %}  <!-- Wrong: using {% %} inside liquid block -->
    assign count = 1
  {% endif %}
%}

✅ Good:
{% liquid %}
  assign count = 0
  if condition  <!-- Correct: no {% %} inside liquid block -->
    assign count = 1
  endif
%}
```

**Solution:** Inside `{% liquid %}` blocks, don't use `{% %}` brackets. Outside liquid blocks, always use proper brackets.

### 5. Pagination Failures and Empty Results

**Problem:** Shopify pagination returning empty `paginate.items` or incorrect counts, causing products not to display.

**Examples:**
```liquid
❌ Bad (Common pattern that fails):
{% paginate collection.products by products_per_page %}
  {% for product in paginate.items %}  <!-- Often empty/broken -->
    {% render 'card-product', card_product: product %}
  {% endfor %}
{% endpaginate %}

✅ Good (Direct iteration with limit):
{% for product in collection.products limit: products_per_page %}
  {% render 'card-product', card_product: product %}
{% endfor %}
```

**Symptoms:**
- Collection has products but grid shows empty
- Debug shows `collection.products.size: 7` but `paginate.items: 0` or incorrect count
- `paginate.total_count` returns empty

**Solution:** For basic product display, use direct collection iteration with `limit` filter instead of pagination. Implement pagination separately if needed.

---

## Schema Validation Errors

### 1. "visible_if is not a valid attribute" Error

**Problem:** Shopify's section schema doesn't support `visible_if` attributes.

**Examples:**
```json
❌ Bad:
{
  "type": "collection",
  "id": "collection",
  "label": "Collection",
  "visible_if": "{{ section.settings.display_mode == 'collection' }}"
}

✅ Good:
{
  "type": "collection",
  "id": "collection",
  "label": "Collection",
  "info": "Only used when display mode is set to 'Specific collection'"
}
```

**Solution:** Remove all `visible_if` attributes. Use Liquid conditionals in templates and optionally add `"info"` text to guide users.

### 2. Invalid Block Attributes

**Problem:** Using unsupported attributes in block definitions.

**Common Invalid Attributes:**
- `visible_if`
- `depends_on`
- `show_if`
- Custom validation attributes

**Solution:** Stick to [official Shopify schema attributes](https://shopify.dev/docs/themes/architecture/sections/section-schema).

---

## Best Practices

### 1. Liquid Code Structure

- **Prefer standard tags over liquid blocks** for complex logic
- **Use liquid blocks** for simple variable assignments and loops
- **Break complex conditions** into multiple simple statements
- **Avoid filters in boolean expressions** with `and`/`or`

### 2. Schema Design

- **Keep schemas simple** - avoid conditional logic in schema
- **Use `"info"` attributes** to provide guidance instead of `visible_if`
- **Handle conditional rendering** in Liquid templates, not schema
- **Test schema changes** frequently during development

### 3. Error Prevention

- **Use consistent syntax patterns** throughout your theme
- **Validate early and often** with `shopify theme check`
- **Break complex operations** into smaller, testable parts
- **Document non-obvious decisions** for future reference

### 4. Development Workflow

1. **Start with schema validation** - fix schema errors first
2. **Then fix Liquid syntax** - work from top to bottom of file
3. **Test individual sections** before combining
4. **Use theme check regularly** during development

---

## Quick Reference

### Common Error Patterns & Solutions

| Error Message | Common Cause | Quick Fix |
|---------------|--------------|-----------|
| "Expected end_of_string but found comparison" | Complex boolean in assign | Split into separate if statements |
| "Unknown tag 'product'" | Multi-line render in liquid block | Use single-line or move outside block |
| "visible_if is not a valid attribute" | Schema conditional logic | Remove visible_if, use Liquid conditionals |
| "Expected end_of_string but found pipe" | Filter in compound boolean | Use != empty instead of \| size > 0 |
| Products not displaying despite collection having items | Broken pagination syntax | Replace `paginate.items` with direct `collection.products limit:` |

### Safe Patterns

```liquid
<!-- Safe array initialization -->
{% assign items = '' | split: ',' %}

<!-- Safe boolean assignment -->
{% assign show_item = false %}
{% if condition1 and condition2 %}
  {% assign show_item = true %}
{% endif %}

<!-- Safe render calls -->
{% render 'snippet-name', param1: value1, param2: value2 %}

<!-- Safe collection filtering -->
{% if collection.filters != empty %}
  <!-- filter logic -->
{% endif %}

<!-- Safe product iteration (instead of broken pagination) -->
{% for product in collection.products limit: products_per_page %}
  {% render 'card-product', card_product: product %}
{% endfor %}
```

### Shopify CLI Commands for Validation

```bash
# Check for syntax errors
shopify theme check

# Check specific file
shopify theme check sections/filename.liquid

# Upload specific file for testing
shopify theme push --only "sections/filename.liquid"

# List available themes
shopify theme list
```

---

## Development Notes

**Project:** Ave Theme - Favorites Feature Implementation
**Date:** September 8-9, 2025
**Issues Resolved:**
- Complex boolean expressions in collection-product-grid.liquid
- Multi-line render calls in product-grid.liquid  
- Schema visible_if attributes in multiple sections
- Array initialization patterns
- Pagination failures causing empty product displays across multiple sections

**Key Learnings:**
- Shopify's Liquid parser is stricter than standard Liquid implementations
- Schema validation happens before Liquid parsing
- Breaking complex operations into simpler steps improves reliability
- Standard Liquid tags are more reliable than liquid blocks for complex operations
- Shopify pagination can fail silently - direct collection iteration is more reliable
- Always test product display with actual collections having products

---

*Last Updated: September 9, 2025*
*Theme: Ave Theme (Skeleton)*
*Author: Development Team*
