{%- comment -%}
  Price Display Snippet
  Renders product pricing with sale indicators
  
  Parameters:
  - product: product object
  - price_class: additional CSS class
{%- endcomment -%}

{%- liquid
  assign compare_at_price = product.compare_at_price
  assign price = product.price | default: 1999
  assign price_class = price_class | default: ''
  if settings.currency_code_enabled
    assign money_price = price | money_with_currency
    assign money_price = money_price | replace: '.00', ''
  else
    assign money_price = price | money_without_trailing_zeros
  endif

  if product.price_varies
    assign money_price = 'products.product.price.from_price_html' | t: price: money_price
  endif
-%}

<div class="price {{ price_class }}">
  <div class="price__container">
    {%- comment -%}
      Explanation of description list:
        - div.price__regular: Displayed when there are no variants on sale
        - div.price__sale: Displayed when a variant is a sale
    {%- endcomment -%}
    <div class="price__regular">
      <span class="visually-hidden visually-hidden--inline">{{ 'products.product.price.regular_price' | t }}</span>
      <span class="price-item price-item--regular">
        {{ money_price }}
      </span>
    </div>
    {%- comment -%}
      Only show the sale block when a valid compare_at_price exists and is greater
      than the actual price. This avoids rendering duplicate prices when no compare
      price is present.
    {%- endcomment -%}
    {%- if settings.show_compare_price and compare_at_price and compare_at_price > price -%}
      <div class="price__sale">
        <span class="visually-hidden visually-hidden--inline">{{ 'products.product.price.regular_price' | t }}</span>
        <span>
          <s class="price-item price-item--regular">
            {% if settings.currency_code_enabled %}
              {{ compare_at_price | money_with_currency | replace: '.00', '' }}
            {% else %}
              {{ compare_at_price | money_without_trailing_zeros }}
            {% endif %}
          </s>
        </span>

        <span class="visually-hidden visually-hidden--inline">{{ 'products.product.price.sale_price' | t }}</span>
        <span class="price-item price-item--sale price-item--last">
          {{ money_price }}
        </span>
      </div>
    {%- endif -%}
    <small class="unit-price caption{% unless product.selected_or_first_available_variant.unit_price_measurement %} hidden{% endunless %}">
      <span class="visually-hidden">{{ 'products.product.price.unit_price' | t }}</span>
      <span class="price-item price-item--last">
        <span>{{- product.selected_or_first_available_variant.unit_price | money -}}</span>
        <span aria-hidden="true">&nbsp;</span>
        <span class="visually-hidden">&nbsp;{{ 'accessibility.unit_price_separator' | t }}&nbsp;</span>
        <span>
          {%- if product.selected_or_first_available_variant.unit_price_measurement.reference_value != 1 -%}
            {{- product.selected_or_first_available_variant.unit_price_measurement.reference_value -}}
          {%- endif -%}
          {{ product.selected_or_first_available_variant.unit_price_measurement.reference_unit }}
        </span>
      </span>
    </small>
  </div>
</div>

{% style %}
  .price {
  color: rgba(var(--color-foreground), 0.6); /* muted grey */
  font-size: 1rem; /* Match product title size */
    line-height: 1.2;
  }
  
  .price__container {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    gap: 0.5rem;
  }
  
  .price__regular:empty,
  .price__sale:empty,
  .price__sale--no-compare:empty {
    display: none;
  }
  
  .price__sale {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    gap: 0.5rem;
  }
  
  .price-item--regular {
    color: rgba(var(--color-foreground), 0.75);
    font-size: 1em; /* Changed from 0.875em to 1em to match product title */
  }
  
  .price-item--sale {
    color: rgb(var(--color-base-accent-1));
    font-weight: 600;
    font-size: 1em; /* Ensure sale price also matches product title size */
  }
  
  .unit-price {
    color: rgba(var(--color-foreground), 0.7);
    font-size: 0.75em;
    margin-top: 0.25rem;
  }
  
  .visually-hidden {
    position: absolute !important;
    overflow: hidden;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
    clip: rect(0 0 0 0);
    word-wrap: normal !important;
  }
  
  .visually-hidden--inline {
    margin: 0;
    height: 1em;
  }
{% endstyle %}
