/* Instagram Feed - Simple Image Grid */
.instagram-simple {
  padding: 3rem 0;
}

.instagram-simple .section-heading {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.instagram-posts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.instagram-post {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
}

.instagram-post:hover {
  transform: translateY(-2px);
}

.instagram-post__image-wrapper {
  position: relative;
  overflow: hidden;
}

.instagram-post__image {
  width: 100%;
  height: auto;
  aspect-ratio: 1;
  object-fit: cover;
  display: block;
}

.instagram-post__link {
  display: block;
  text-decoration: none;
  color: inherit;
  position: relative;
}

.instagram-post__overlay {
  position: absolute;
  inset: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.instagram-post__link:hover .instagram-post__overlay {
  opacity: 1;
}

.instagram-icon {
  margin-bottom: 0.5rem;
}

.instagram-post__caption {
  padding: 1rem;
  background: var(--color-background, #fff);
  font-size: 0.875rem;
  line-height: 1.4;
}

.instagram-post__caption p {
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 749px) {
  .instagram-simple {
    padding: 2rem 0;
  }
  
  .instagram-simple .section-heading {
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
  }
  
  .instagram-posts {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 479px) {
  .instagram-posts {
    grid-template-columns: 1fr;
  }
}
