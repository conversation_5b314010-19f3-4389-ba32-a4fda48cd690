/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "hero": {
      "type": "hero-banner",
      "settings": {
        "media_mode": "image",
        "heading": "Welcome to our store",
        "heading_tag": "h1",
        "text": "<p>Discover amazing products with unbeatable quality and style. Shop our latest collection today.</p>",
        "button_label": "Shop now",
        "button_link": "/collections/all",
        "button2_label": "Learn more",
        "button2_link": "/pages/about",
        "banner_height": "large",
        "content_position": "center",
        "content_alignment": "center",
        "content_max_width_ch": 50,
        "button_layout": "auto",
        "show_overlay": true,
        "overlay_style": "gradient",
        "overlay_opacity": 35,
        "color_scheme": "scheme-1",
        "is_primary_hero": true
      }
    },
    "slideshow": {
      "type": "slideshow",
      "settings": {
        "show_arrows": true,
        "show_thumbnails": false,
        "show_pagination": true,
        "enable_loop": true,
        "enable_autoplay": false,
        "autoplay_speed": 5000,
        "animation_speed": 500
      },
      "blocks": {
        "slide-1": { "type": "image", "settings": { "heading": "Slide one", "text": "<p>Add your media in the editor.</p>" } },
        "slide-2": { "type": "image", "settings": { "heading": "Slide two", "text": "<p>Configure autoplay, arrows, and pagination.</p>" } }
      },
      "block_order": ["slide-1", "slide-2"]
    },
    "shop_by_brand": {
      "type": "shop-by-brand",
      "settings": { "heading": "Shop by brand" }
    },
    "best_sellers": {
      "type": "best-sellers",
      "settings": { "heading": "Best sellers", "show_numeric": true }
    },
    "shop_the_look": {
      "type": "shop-the-look",
      "settings": { "heading": "Shop the look" }
    },
    "store_location": {
      "type": "store-location",
      "settings": { "heading": "Visit our store", "map_query": "New York, NY" }
    },
    "main": {
      "type": "hello-world",
      "settings": {}
    }
  },
  "order": [
    "hero",
    "slideshow",
    "shop_by_brand",
    "best_sellers",
    "shop_the_look",
    "store_location",
    "main"
  ]
}
