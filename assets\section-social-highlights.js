/* Section: Social Media Highlights (progressive enhancement)
   - Enhances slider layout with arrow buttons and dots.
   - No external dependencies. Safe to run multiple instances. */

(function () {
  'use strict';

  function initSection(section) {
    var list = section.querySelector('.social-highlights__list--slider');
    if (!list) return;

    var items = Array.from(list.querySelectorAll('.social-post'));
    if (!items.length) return;

    var prevBtn = section.querySelector('.sh-slider__arrow--prev');
    var nextBtn = section.querySelector('.sh-slider__arrow--next');
    var dotsHost = section.querySelector('.sh-slider__dots');

    function scrollByCard(dir) {
      var card = items[0];
      var delta = card ? card.getBoundingClientRect().width + 12 /* gap */ : list.clientWidth * 0.8;
      list.scrollBy({ left: dir * delta, behavior: 'smooth' });
    }

    if (prevBtn) prevBtn.addEventListener('click', function () { scrollByCard(-1); });
    if (nextBtn) nextBtn.addEventListener('click', function () { scrollByCard(1); });

    if (dotsHost) {
      dotsHost.innerHTML = '';
      items.forEach(function (_item, idx) {
        var dot = document.createElement('button');
        dot.type = 'button';
        dot.setAttribute('aria-label', 'Go to slide ' + (idx + 1));
        dot.addEventListener('click', function () {
          var left = items[idx].offsetLeft;
          list.scrollTo({ left: left, behavior: 'smooth' });
        });
        dotsHost.appendChild(dot);
      });

      function updateDots() {
        var scrollLeft = list.scrollLeft;
        var widths = items.map(function (it) { return it.offsetLeft; });
        var active = 0;
        for (var i = 0; i < widths.length; i++) {
          if (scrollLeft + 10 >= widths[i]) active = i;
        }
        var dots = dotsHost.querySelectorAll('button');
        dots.forEach(function (d, i) {
          if (i === active) d.setAttribute('aria-current', 'true');
          else d.removeAttribute('aria-current');
        });
      }
      list.addEventListener('scroll', updateDots, { passive: true });
      // Initialize after first paint
      requestAnimationFrame(updateDots);
      window.addEventListener('resize', function () { requestAnimationFrame(updateDots); });
    }
  }

  function initAll() {
    document.querySelectorAll('.social-highlights--layout-slider').forEach(initSection);
  }

  document.addEventListener('DOMContentLoaded', initAll);
  // Editor support: re-init on section load/select when customizing theme
  document.addEventListener('shopify:section:load', function (e) {
    if (e.target && e.target.classList && e.target.classList.contains('social-highlights')) initSection(e.target);
  });
})();
