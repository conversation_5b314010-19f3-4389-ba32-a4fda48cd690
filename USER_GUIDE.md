# Skeleton Theme - User Guide

## 🎯 Welcome to Skeleton Theme

A modern, customizable Shopify theme with professional header functionality and intuitive theme editor controls.

---

## 🚀 Getting Started

### First Setup

1. **Upload your logo**: Go to Theme Editor → Header section → Logo Settings
2. **Configure navigation**: Add Simple Link or Megamenu blocks for your main navigation
3. **Set sticky behavior**: Choose how your header behaves when scrolling
4. **Customize appearance**: Adjust colors, spacing, and effects to match your brand

---

## 🖼️ Logo Configuration

### Basic Logo Setup

**Upload Logo Image:**
- Go to Header section → Logo Settings → Logo image
- Upload PNG or SVG format for best quality
- Recommended: Transparent background

**Logo Size:**
- Use the "Logo width" slider to adjust size (50-400px)
- Logo automatically scales proportionally

**Text Logo (No Image):**
- If no logo image is uploaded, your store name appears as text
- Adjust "Logo text size" to control font size (16-48px)

### Mobile Logo (Optional)

- Upload a different logo specifically for mobile devices
- Useful if your main logo is too detailed for small screens
- Only appears after you've uploaded a main logo
- Leave empty to use the same logo on all devices

---

## 🎬 Hero Banner Setup

### Creating Impactful Homepage Banners

**Media Configuration:**
- Choose between image or video backgrounds
- Upload high-quality images (recommended: 2000x1200px)
- Optional: Upload separate mobile-optimized image
- For videos: Use Shopify-hosted videos for best performance

**Content Setup:**
- Add compelling headline and subheading
- Choose appropriate heading tag (H1 for homepage)
- Configure primary and secondary call-to-action buttons
- Set content positioning (9 different positions available)

**Performance Features:**
- Enable "Homepage hero" for faster loading
- Automatic reduced motion support
- Responsive image optimization
- Video poster images for faster initial load

### Layout Options

**Height Settings:**
- Small (45vh) - Compact banner
- Medium (60vh) - Balanced height  
- Large (80vh) - Full-impact banner
- Adapt - Matches image proportions

**Content Positioning:**
- 9-point grid system for precise placement
- Text alignment options (left, center, right)
- Adjustable content width for readability
- Responsive button layouts

**Visual Enhancements:**
- Overlay options for text contrast
- Gradient or solid overlay styles
- Adjustable opacity (0-100%)
- Color scheme integration

---

## 📱 Navigation Setup

### Adding Navigation Items

**Simple Link Blocks:**
1. Click "Add block" → Simple Link
2. Select which menu to display from your Navigation settings
3. Creates dropdown menus automatically for multi-level menus

**Megamenu Blocks:**
1. Click "Add block" → Megamenu  
2. Set the main navigation title (e.g., "Shop", "Products")
3. Configure up to 3 menu columns
4. Add optional promotional content with image and link

### Managing Navigation

- **Reorder**: Drag blocks up/down to change navigation order
- **Remove**: Click block settings → Remove block
- **Edit menus**: Go to Navigation settings in your Shopify admin to modify menu content

---

## 📌 Sticky Header Behavior

### Choosing Sticky Mode

**None:**
- Header scrolls away normally as you scroll down
- Traditional website behavior

**Always (native sticky):**
- Header stays visible at top of screen while scrolling
- Recommended for most stores
- Smooth, browser-native sticky behavior

**Show on scroll up:**
- Header hides when scrolling down
- Shows when scrolling up
- Saves screen space while keeping header accessible

### Device Targeting

**All devices:** Sticky behavior on mobile and desktop
**Desktop only:** Only sticky on screens 990px and wider  
**Mobile only:** Only sticky on screens smaller than 990px

---

## ⚡ Animation & Visual Effects

### Animation Settings
*(Only visible when using "Show on scroll up" mode)*

**Scroll distance to hide/show:**
- How far to scroll before header reacts
- Higher values = less sensitive

**Animation duration:**
- Speed of hide/show animation
- 0ms = instant, 400ms = slow transition

**Reduced motion:**
- Respects users' accessibility preferences
- Recommended: Keep enabled

### Visual Enhancements
*(Only visible when sticky mode is enabled)*

**Shadow when sticky:**
- Adds subtle drop shadow for better separation
- Choose strength: None, Light, or Medium

**Solid background:**
- Ensures header has opaque background when sticky
- Prevents content from showing through

---

## 📐 Layout & Spacing

### Padding Adjustment
*(Only visible when sticky mode is enabled)*

**Padding reduction:**
- Reduces header height when sticky to save space
- 0px = no change, 20px = maximum reduction

### Z-Index Control
*(Only visible when sticky mode is enabled)*

**Header z-index:**
- Controls if header appears above other elements
- Increase if header appears behind popups or modals
- Default: 100 (suitable for most sites)

---

## 🛠️ Troubleshooting

### Common Issues

**Logo appears too large/small:**
- Adjust "Logo width" slider in Logo Settings
- For text logos, adjust "Logo text size"

**Navigation menus not showing:**
- Check that you've created menus in Navigation settings
- Ensure Simple Link or Megamenu blocks are added
- Verify menu is selected in block settings

**Sticky header not working:**
- Ensure sticky mode is not set to "None"
- Check device targeting settings match your screen size
- Try refreshing the page

**Header appears behind other elements:**
- Increase "Header z-index" in Layout & Spacing section
- Values above 100 usually resolve conflicts

### Mobile Navigation

**Menu not opening on mobile:**
- Check that navigation blocks are properly configured
- Ensure menus have content in Navigation settings
- Try clearing browser cache

**Mobile logo not showing:**
- Verify main logo is uploaded first
- Upload mobile logo in Logo Settings
- Check that image file is valid format (PNG, JPG, SVG)

---

## 🎨 Customization Tips

### Brand Consistency

1. **Use consistent colors** throughout your theme settings
2. **Upload high-quality logos** in PNG or SVG format
3. **Keep navigation simple** - 5-7 main categories work best
4. **Test on mobile** regularly during setup

### Performance Tips

1. **Optimize logo images** before uploading
2. **Use appropriate sticky settings** for your content
3. **Keep animation durations reasonable** (100-200ms)
4. **Don't overcomplicate navigation** structure

### Professional Look

1. **Enable shadow when sticky** for better visual separation
2. **Use padding reduction** to keep header compact when sticky
3. **Consider desktop-only sticky** for content-heavy sites
4. **Add promotional content** to megamenus for marketing

---

## 📞 Getting Help

### Before Contacting Support

1. **Check this guide** for common solutions
2. **Test in different browsers** to isolate issues
3. **Try disabling other apps** that might conflict
4. **Clear browser cache** and try again

### What to Include When Asking for Help

- Description of the issue
- What you were trying to accomplish
- Screenshots if visual problem
- Browser and device information
- Steps you've already tried

---

## ✅ Best Practices

### Header Setup Checklist

- [ ] Logo uploaded and sized appropriately
- [ ] Navigation blocks added and configured
- [ ] Sticky behavior chosen and tested
- [ ] Mobile experience verified
- [ ] Visual effects configured to match brand
- [ ] All settings tested across devices

### Ongoing Maintenance

- **Review navigation** quarterly as your product catalog grows
- **Update promotional content** in megamenus regularly
- **Monitor mobile experience** as you add new content
- **Keep logo files optimized** for fast loading

---

## 🎉 Congratulations!

You've successfully configured your Skeleton theme header! Your professional, customizable navigation system is now ready to help customers explore your store with ease.
