.quickadd-modal { border:0; padding:0; width:100%; max-width:none; }
.quickadd-modal::backdrop { background: rgba(0,0,0,.4); }
.quickadd-modal[open] { display:grid; place-items:center; }
.quickadd-modal__panel { position:relative; width:min(720px,92vw); max-height:90vh; overflow:auto; background:#fff; border-radius:12px; box-shadow:0 10px 40px rgba(0,0,0,.2); }
.quickadd-modal__backdrop { position:fixed; inset:0; }
.quickadd-modal__close { position:absolute; top:.75rem; right:.75rem; background:transparent; border:0; font-size:1.25rem; cursor:pointer; }
.quickadd-modal__body { padding:1rem 1.25rem 1.5rem; }

.qa-grid { display:grid; grid-template-columns: 1fr 1fr; gap:1rem; }
.qa-media img { width:100%; height:100%; object-fit:cover; aspect-ratio: 1 / 1; }
.qa-fieldset { margin:0 0 .75rem; border:0; padding:0; }
.qa-options { display:flex; flex-wrap:wrap; gap:.5rem; }
.qa-chip { display:inline-flex; align-items:center; gap:.5rem; cursor:pointer; }
.qa-chip input { appearance:none; width:1rem; height:1rem; border-radius:999px; outline:2px solid currentColor; }
.qa-chip input:checked + span { font-weight:600; }
.qa-price { margin:.75rem 0; display:flex; gap:.5rem; align-items:baseline; }
.qa-price s { color:rgba(0,0,0,.5); }
.qa-qty input { width:4.5rem; }
.qa-add[aria-busy="true"] { opacity:.7; pointer-events:none; }
@media (max-width: 720px){ .qa-grid { grid-template-columns: 1fr; } }

/* Quick Add Button Loading States */
.quick-add__button { position: relative; }
.quick-add__text { transition: opacity 0.2s ease; }
.qa-add { position: relative; }
.qa-add__text { transition: opacity 0.2s ease; }
.loading-overlay { 
  position: absolute; 
  inset: 0; 
  display: flex; 
  align-items: center; 
  justify-content: center; 
  background: inherit; 
  border-radius: inherit; 
}
.loading-overlay.hidden { display: none; }
.loading-overlay__spinner svg { 
  width: 20px; 
  height: 20px; 
  animation: spin 1s linear infinite; 
}
.spinner .path { 
  stroke: currentColor; 
  stroke-linecap: round; 
  stroke-dasharray: 90, 150; 
  stroke-dashoffset: 0; 
  animation: dash 1.5s ease-in-out infinite; 
}
@keyframes spin { 
  0% { transform: rotate(0deg); } 
  100% { transform: rotate(360deg); } 
}
@keyframes dash { 
  0% { stroke-dasharray: 1, 150; stroke-dashoffset: 0; } 
  50% { stroke-dasharray: 90, 150; stroke-dashoffset: -35; } 
  100% { stroke-dasharray: 90, 150; stroke-dashoffset: -124; } 
}
