# Runway Sticky Rail - Current Implementation

## Overview

The Runway layout features a sticky product info panel that floats on the right side of the screen while the user scrolls through the product gallery. This document describes the current implementation approach.

## Architecture

### Three-State System

The rail operates in three distinct states:

1. **Natural** (default): `position: absolute` at the top of the section
2. **Fixed** (sticky): `position: fixed` under the header while scrolling
3. **Released** (pinned): `position: absolute` at a computed release point

### Key Components

#### 1. CSS Custom Properties

```css
--runway-slider-h: calc(100vh - var(--header-h) - var(--fold-buffer))
--header-h: [dynamic header height]
--fold-buffer: [peek amount for next section]
--release-top: [computed release position]
--runway-info-w: 360px [rail width]
--pi-edge-gap: clamp(12px, 2.5vw, 22px) [gap from viewport edge]
```

#### 2. CSS Classes

```css
.product__info {
  /* Default: absolute positioning */
  position: absolute;
  height: var(--runway-slider-h);
  right: var(--pi-edge-gap);
  top: 0;
}

.product__info.is-fixed {
  /* Sticky state: fixed under header */
  position: fixed;
  top: var(--header-h, 0);
}

.product__info.is-released {
  /* Released state: pinned to computed position */
  position: absolute;
  top: var(--release-top, auto);
}
```

#### 3. Inner Content Scrolling

```css
.product-info {
  /* Inner card scrolls within fixed-height container */
  height: 100%;
  max-height: 100%;
  overflow: auto;
  overscroll-behavior: contain;
  scrollbar-gutter: stable both-edges;
}
```

## JavaScript Logic

### Core Functions

#### `getSliderH()`

Calculates the rail height to match the media gallery:

```javascript
const getSliderH = () => {
  const vh = (window.visualViewport && typeof visualViewport.height === 'number')
    ? visualViewport.height
    : window.innerHeight;
  return Math.max(200, Math.floor(vh - getHeaderH() - getVarPx(sectionRoot, '--fold-buffer')));
};
```

#### `findReleaseAnchor()`

Finds the element where the rail should stop sticking:

```javascript
const findReleaseAnchor = () => {
  // 1) Explicit merchant marker
  const marked = document.querySelector('[data-rail-end]');
  if (marked) return marked;

  // 2) Heuristic: look for common "mate" sections
  const thisShopifySection = sectionRoot.closest('.shopify-section');
  let next = thisShopifySection ? thisShopifySection.nextElementSibling : null;
  
  const candidates = [];
  let cursor = next;
  const MAX_CHECK = 6;
  let checks = 0;

  while (cursor && checks < MAX_CHECK) {
    candidates.push(cursor);
    // Stop early if we spot common rail mates
    if (cursor.querySelector('.product-recs, .shop-the-look, .keep-exploring, [data-rail-mate]'))
      break;
    cursor = cursor.nextElementSibling;
    checks++;
  }
  
  return candidates[candidates.length - 1] || next || thisShopifySection || sectionRoot;
};
```

**Detection priority:**
1. Explicit `[data-rail-end]` marker (highest priority)
2. Sections containing `.product-recs`, `.shop-the-look`, `.keep-exploring`, or `[data-rail-mate]`
3. Next `.shopify-section` after product
4. Product section itself (fallback)

#### `computeRelease()`

Calculates where the rail should release:

```javascript
const computeRelease = () => {
  const sliderH = getSliderH();
  infoWrap.style.height = sliderH + 'px';

  const sectionTop = (sectionRoot.closest('.shopify-section') || sectionRoot)
    .getBoundingClientRect().top + window.scrollY;
  
  const anchor = findReleaseAnchor();
  const rect = anchor.getBoundingClientRect();
  const anchorBottom = rect.top + window.scrollY + rect.height;

  const releaseTop = Math.max(0, Math.round(anchorBottom - sectionTop - sliderH));
  sectionRoot.style.setProperty('--release-top', releaseTop + 'px');
  
  return { releaseTop, sectionTop };
};
```

**Formula:** `releaseTop = (anchor bottom) - (section top) - (rail height)`

This ensures the rail releases when its bottom would align with the anchor's bottom.

#### `updateMode()`

Toggles between states based on scroll position:

```javascript
const updateMode = () => {
  const { releaseTop, sectionTop } = computeRelease();
  const y = window.scrollY;
  const header = getHeaderH();

  const fixedStart = sectionTop - header;
  const fixedEnd = sectionTop + releaseTop;

  let mode = 'natural';
  if (y >= fixedStart && y < fixedEnd) mode = 'fixed';
  else if (y >= fixedEnd) mode = 'released';

  if (mode !== lastMode) {
    infoWrap.classList.toggle('is-fixed', mode === 'fixed');
    infoWrap.classList.toggle('is-released', mode === 'released');
    lastMode = mode;
  }
};
```

**State transitions:**
- `y < fixedStart`: Natural (absolute at top)
- `fixedStart <= y < fixedEnd`: Fixed (sticky under header)
- `y >= fixedEnd`: Released (absolute at release point)

### Event Listeners

```javascript
const schedule = () => requestAnimationFrame(updateMode);

window.addEventListener('resize', schedule, { passive: true });
window.addEventListener('scroll', schedule, { passive: true });
document.addEventListener('runway:header-height', schedule);
window.addEventListener('load', schedule);
document.addEventListener('DOMContentLoaded', schedule);

if (window.visualViewport) {
  visualViewport.addEventListener('resize', schedule);
}

// Re-measure when content changes (e.g., async recommendations)
if ('MutationObserver' in window) {
  const mo = new MutationObserver(schedule);
  mo.observe(document.documentElement, { childList: true, subtree: true });
}
```

## Merchant Control

### Option 1: Explicit End Marker

Add `[data-rail-end]` to any element to mark the release point:

```liquid
<div class="shopify-section" data-rail-end>
  {% section 'newsletter' %}
</div>
```

### Option 2: Mate Markers

Add `[data-rail-mate]` to sections that should be included in the sticky track:

```liquid
<div class="shopify-section" data-rail-mate>
  {% section 'product-recommendations' %}
</div>
```

### Option 3: Auto-Detection (Default)

If no markers are present, the script automatically detects common sections:
- `.product-recs` (product recommendations)
- `.shop-the-look`
- `.keep-exploring`
- Any section with `[data-rail-mate]`

## Benefits

### ✅ No Height Standardization

- Panel can have any amount of content
- Sections can have any height
- Works with merchant customization
- No coordination needed between components

### ✅ No Content Cutoff

- Inner content scrolls within fixed-height container
- Styled scrollbar (subtle, luxury aesthetic)
- All content is accessible regardless of panel height

### ✅ Flexible Release Point

- Explicit control via `[data-rail-end]`
- Smart defaults via heuristics
- Works with any theme composition
- Adapts to dynamic content

### ✅ Hermès-Style Alignment

- Rail releases in sync with content sections
- No height matching required
- Adapts to what's actually present on the page

### ✅ Performance Optimized

- Uses `requestAnimationFrame` for smooth updates
- Passive event listeners
- MutationObserver for dynamic content
- Efficient state caching (only updates when mode changes)

## Why This Approach?

### Manual Position Toggling vs Native CSS Sticky

**Current approach (manual toggling):**
- Full control over release behavior
- Works with existing layout system
- Can position rail absolutely to the right
- No HTML restructuring needed

**Native CSS sticky (alternative):**
- Simpler CSS
- Less JavaScript
- But requires HTML restructuring (scope container)
- Harder to position to the right side
- Less control over exact release point

The current approach was chosen because it:
1. Works with the existing theme architecture
2. Provides precise control over release behavior
3. Doesn't require major HTML restructuring
4. Supports the floating right-side panel design

## Testing

### Visual Test

1. **Short panel content**: Panel should stick and release at anchor bottom
2. **Tall panel content**: Inner content should scroll, panel should stick and release
3. **Scroll to bottom**: Panel should be at release position
4. **Scroll back up**: Panel should stick again

### Console Checks

```javascript
// Check rail state
const rail = document.querySelector('.product__info');
console.log('Is fixed:', rail.classList.contains('is-fixed'));
console.log('Is released:', rail.classList.contains('is-released'));

// Check computed values
const section = document.querySelector('.product');
console.log('Release top:', getComputedStyle(section).getPropertyValue('--release-top'));
console.log('Rail height:', rail.offsetHeight, 'px');

// Check anchor
const anchor = document.querySelector('[data-rail-end]') || 
               document.querySelector('.product-recs');
console.log('Release anchor:', anchor?.className || 'not found');
```

## Files

- **sections/product.liquid** (lines ~205-293): CSS rules
- **sections/product.liquid** (lines ~3345-3464): JavaScript implementation

## Future Enhancements

- [ ] Support for extending scope to include following sections
- [ ] More granular merchant control over sticky behavior
- [ ] Animation transitions between states
- [ ] Accessibility improvements for scrollable panel

