/* Section: Social Media Highlights (UGC) */
/* Uses theme color schemes via color-<scheme> class on the section root. */

.social-highlights { position: relative; }
.social-highlights__header { margin-bottom: 1rem; text-align: center; }
.social-highlights__heading { margin: 0 0 .25rem; }
.social-highlights__subheading { margin: 0; opacity: .8; }

.social-highlights__container { width: 100%; }
.social-highlights__placeholder { text-align: center; opacity: .8; }

/* Grid layout */
.social-highlights__list--grid {
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(var(--sh-cols, 4), minmax(0, 1fr));
}

/* Slider layout: CSS scroll snap, no deps */
.social-highlights__list--slider {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: clamp(240px, 80vw, 420px);
  overflow-x: auto;
  gap: 12px;
  padding-inline: var(--page-gutter, 1rem);
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
}
.social-highlights__list--slider:focus-visible { outline: 2px solid currentColor; outline-offset: 2px; }

.social-post { scroll-snap-align: start; }
.social-post__media { position: relative; }
.social-post__image { display: block; width: 100%; height: auto; border-radius: var(--radius-md, 8px); }

/* Full-card tap area for post link, separate from product hotspot */
.social-post__taparea {
  position: absolute;
  inset: 0;
  z-index: 1;
  text-indent: -9999px;
  overflow: hidden;
}

/* Product hotspot */
.social-post__hotspot {
  position: absolute;
  right: .5rem;
  bottom: .5rem;
  z-index: 2;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: .25rem;
  padding: .375rem;
  border-radius: 9999px;
  background: rgb(0 0 0 / .6);
  color: white;
  text-decoration: none;
}
.social-post__hotspot:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}
.social-post__hotspot-icon { display: block; }

/* Slider controls */
.sh-slider__arrows {
  position: relative;
  margin-top: .5rem;
  display: flex;
  justify-content: center;
  gap: .5rem;
}
.sh-slider__arrow {
  appearance: none;
  border: 1px solid currentColor;
  border-radius: 9999px;
  background: transparent;
  padding: .4rem;
  cursor: pointer;
}
.sh-slider__arrow:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}
.sh-slider__dots {
  display: flex;
  justify-content: center;
  gap: .4rem;
  margin-top: .5rem;
}
.sh-slider__dots button {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  border: 0;
  background: currentColor;
  opacity: .3;
}
.sh-slider__dots button[aria-current="true"] { opacity: 1; }

/* Responsive niceties */
@media (max-width: 749px) {
  .social-highlights__list--grid {
    grid-template-columns: repeat(min(var(--sh-cols, 4), 2), minmax(0, 1fr));
  }
}
