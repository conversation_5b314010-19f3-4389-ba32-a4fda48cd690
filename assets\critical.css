/** Critical CSS for the theme. This file is included on every page. */

/* Reset styles inspired by https://www.joshwcomeau.com/css/custom-css-reset/ */
* {
  box-sizing: border-box;
  margin: 0;
}

body {
  display: flex;
  flex-direction: column;
  margin: 0;
  min-height: 100svh;
  /* Prevent tiny horizontal scrollbars from full-bleed sections (e.g., 100vw) */
  overflow-x: clip;
}

/* Reserve scrollbar gutter so 100vw-based full-bleed math doesn’t leave a sliver on wide viewports */
/* Use both-edges to prevent 1-2px nudges on Windows when scrollbar appears/disappears */
/* Reserve gutter only on the scrollbar side to keep true edge-to-edge on the left */
html { scrollbar-gutter: stable; }

html:has(dialog[scroll-lock][open], details[scroll-lock][open]) {
  overflow: hidden;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

input,
textarea,
select {
  font: inherit;
  border-radius: var(--style-border-radius-inputs);
}

select {
  background-color: var(--color-background);
  color: currentcolor;
}

dialog {
  background-color: var(--color-background);
  color: var(--color-foreground);
}

p {
  text-wrap: pretty;
}
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

p:empty {
  display: none;
}

:is(p, h1, h2, h3, h4, h5, h6):first-child,
:empty:first-child + :where(p, h1, h2, h3, h4, h5, h6) {
  margin-block-start: 0;
}

:is(p, h1, h2, h3, h4, h5, h6):last-child,
:where(p, h1, h2, h3, h4, h5, h6) + :has(+ :empty:last-child) {
  margin-block-end: 0;
}

/** Theme styles below */
body {
  font-family: var(--font-primary--family);
  background-color: var(--color-background);
  color: var(--color-foreground);
}

/* Global layout rhythm */
:root {
  /* Default vertical gap between sections */
  --section-gap: clamp(1.75rem, 3.5vw, 3rem);
  /* Optional inner padding for content sections */
  --section-inner-pad: clamp(1rem, 2.5vw, 1.75rem);
  /* Consistent gaps for elements within sections */
  --heading-gap: clamp(.75rem, 1.6vw, 1.25rem);
  --text-gap: clamp(.5rem, 1.2vw, .9rem);
  --block-gap: clamp(.75rem, 1.8vw, 1.25rem);
}

/** Section layout utilities */

/**
 * Setup a grid system for full-width layout with optional constraints
 *
 * By default, sections span the full viewport width.
 * Header and footer sections get tiny side margins for breathing room.
 * Content sections can opt into constraints when needed.
 */
.shopify-section {
  /* Default: full width with no margins */
  --content-width: 100%;
  --content-margin: 0;
  --content-grid: var(--content-width);

  /* This is required to make <img> elements work as background images */
  position: relative;
  display: grid;
  grid-template-columns: var(--content-grid);
  width: 100%;
}

/* Header and footer sections get padding instead of margins for more robust spacing */
.shopify-section-group-header-group .shopify-section,
.shopify-section-group-footer-group .shopify-section {
  --header-footer-padding: 2rem;
  padding-left: var(--header-footer-padding);
  padding-right: var(--header-footer-padding);
  grid-template-columns: 1fr;
}

/* Ensure header section group has proper padding */
.shopify-section-group-header-group .shopify-section {
  padding-left: 2rem;
  padding-right: 2rem;
}

/* Ensure header section group has proper padding */
.shopify-section-group-header-group .shopify-section {
  padding-left: 2rem;
  padding-right: 2rem;
}

/* Ensure header inner container respects section padding */
.shopify-section-group-header-group .shopify-section .header__inner {
  max-width: calc(100% - 4rem); /* Account for 2rem padding on each side */
  margin: 0 auto;
  padding-left: 0;
  padding-right: 0;
  grid-column: 1; /* Ensure it spans the correct grid column */
}

/* Ensure header inner container has proper width */
.header__inner {
  width: 100%;
  max-width: 100%;
}

/* Ensure header inner container has proper padding */
.header__inner {
  padding: 0 2rem; /* Add 2rem padding on each side */
  width: calc(100% - 4rem); /* Account for 2rem padding on each side */
  margin: 0 auto;
  max-width: calc(100% - 4rem); /* Ensure it doesn't exceed container width */
}

/* Ensure header section group has proper padding */
.shopify-section-group-header-group .shopify-section {
  padding-left: 2rem;
  padding-right: 2rem;
}

/* Child elements, by default, span the full available space */
.shopify-section > * {
  grid-column: 1 / -1;
}

/* For header/footer sections, content spans the full padded area */
.shopify-section-group-header-group .shopify-section > *,
.shopify-section-group-footer-group .shopify-section > * {
  grid-column: 1;
}

/* Utility class to add traditional page-width constraints when needed */
.shopify-section > .page-width,
.page-width {
  max-width: min(var(--page-width, 90rem), calc(100% - var(--page-margin, 1rem) * 2));
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--page-margin, 1rem);
  padding-right: var(--page-margin, 1rem);
}

/* Utility class for full-width elements (now redundant but kept for compatibility) */
.shopify-section > .full-width {
  grid-column: 1 / -1;
  width: 100%;
}

/* Consistent top-of-page spacing: first content section never adds extra gap */
.main-content > .shopify-section:first-child { margin-top: 0; }

/* Natural flow mode: zero extra spacing at the very top */
body.header--natural-flow .main-content > .shopify-section:first-child {
  margin-top: 0;
}

/* Even with announcement bar present, neutral state keeps zero extra gap */
body.header--natural-flow:has(.announcement-bar) .main-content > .shopify-section:first-child {
  margin-top: 0;
}

/* No automatic padding - let JavaScript handle it explicitly when needed */

/* Override for natural flow mode (sticky: none) */
body.header--natural-flow #MainContent {
  padding-top: 0 !important;
}

/* Overlay mode: header floats, do not push content down */
body.header--overlay #MainContent { padding-top: 0; }

/* Section spacing - consistent gaps between sections */
#MainContent .shopify-section + .shopify-section { margin-top: var(--section-gap); }

/* Remove top margin from hero banner sections */
#MainContent .shopify-section + .shopify-section.hero-banner {
  margin-top: 0;
}


/* Add spacing after any section that contains a hero */
#MainContent .shopify-section.hero-host + .shopify-section,
#MainContent .shopify-section:has(> .hero-banner) + .shopify-section { margin-top: var(--section-gap); }

/* First content section should sit flush under the header */
#MainContent > .shopify-section:first-child { margin-top: 0 !important; }
#MainContent > .shopify-section:first-child > .page-width { padding-top: 0; }

/* Apply gentle vertical padding to most section containers (low specificity so section styles can override) */
.shopify-section > .page-width { padding-top: var(--section-inner-pad); padding-bottom: var(--section-inner-pad); }

/* Typographic rhythm inside sections */
#MainContent .shopify-section :is(h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6) { margin-top: 0; margin-bottom: var(--heading-gap); }
#MainContent .shopify-section :is(p, .rte p) { margin-top: 0; margin-bottom: var(--text-gap); }
#MainContent .shopify-section :is(ul, ol) { margin-top: 0; margin-bottom: var(--text-gap); }
#MainContent .shopify-section :is(h1,h2,h3,h4,h5,h6,p,ul,ol):last-child { margin-bottom: 0; }

/* Common section header wrappers */
#MainContent .shopify-section :is(.sbb__header, .best-sellers__header, .stl__header, .product-recs__header, .collection-product-grid__header, .product-grid__header, .social-highlights__header) { margin-bottom: var(--heading-gap); }

/* Normalize list/grid gaps across common sections */
#MainContent .brand-grid,
#MainContent .best-sellers__list,
#MainContent .store-location__list,
#MainContent .product-grid__list,
#MainContent .product-recs__list { gap: var(--block-gap); }

/* Remove gap between announcement bar and first section when header is overlay */
body.header--overlay .announcement-bar__inner {
  padding-bottom: 0;
}
body.header--overlay .main-content > .shopify-section:first-child {
  margin-top: 0;
}

/* Kill any hairline from borders/shadows when in overlay mode */
body.header--overlay .header {
  border-bottom: 0;
  box-shadow: none;
}

/* Prevent top-margin "leak" from the hero's first child */
body.header--overlay .hero-banner,
body.header--overlay .hero-banner > *:first-child {
  margin-top: 0;
}

/* Link styles aligned with button focus/hover */
.rte a { text-decoration: underline; text-underline-offset: 2px; }
.rte a:hover { background: var(--button-hover-overlay-color, color-mix(in oklab, var(--color-foreground) var(--button-hover-mix), transparent)); border-radius: 2px; }
a:focus-visible { outline: 2px solid var(--button-focus-ring-color, color-mix(in oklab, var(--color-foreground) var(--button-focus-ring-mix), transparent)); outline-offset: 2px; }

/* Reduced spacing on mobile for better proportion */
@media screen and (max-width: 749px) {
  #MainContent .shopify-section + .shopify-section { margin-top: var(--section-gap); }

  /* Remove top margin from hero banner sections on mobile */
  #MainContent .shopify-section + .shopify-section.hero-banner {
    margin-top: 0;
  }

  .shopify-section.hero-host + .shopify-section,
  .shopify-section:has(> .hero-banner) + .shopify-section { margin-top: var(--section-gap); }

  /* Natural flow mode mobile: zero extra spacing at the top */
  body.header--natural-flow .main-content > .shopify-section:first-child {
    margin-top: 0;
  }

  body.header--natural-flow:has(.announcement-bar) .main-content > .shopify-section:first-child {
    margin-top: 0;
  }


  /* Remove gap between announcement bar and first section when header is overlay on mobile */
  body.header--overlay .announcement-bar__inner {
    padding-bottom: 0;
  }
  body.header--overlay .main-content > .shopify-section:first-child {
    margin-top: 0;
  }

  /* Kill any hairline from borders/shadows when in overlay mode on mobile */
  body.header--overlay .header {
    border-bottom: 0;
    box-shadow: none;
  }

  /* Prevent top-margin "leak" from the hero's first child on mobile */
  body.header--overlay .hero-banner,
  body.header--overlay .hero-banner > *:first-child {
    margin-top: 0;
  }
}

/* No spacing for full-bleed sections like callout banners and hero banners */
.shopify-section > .full-width.callout-banner,
.shopify-section.hero-banner {
  margin-top: 0;
  margin-bottom: 0;
}

/* Ensure hero host sections have no side padding that could create gutters */
.shopify-section.hero-host { padding-left: 0; padding-right: 0; }


/* Optional compact mode: remove spacing between all sections */
body.no-section-spacing .shopify-section + .shopify-section { margin-top: 0; }

/* Gentle internal padding for homepage content sections */
.template-index .shopify-section > .page-width { padding-top: var(--section-inner-pad); padding-bottom: var(--section-inner-pad); }
.template-index .shopify-section:has(> .hero-banner) > .page-width { padding-top: 0; padding-bottom: 0; }

/* ------------------------------------------------------------
   Homepage polish (template: index)
   Light-touch spacing and typography tweaks without layout changes
------------------------------------------------------------- */
.template-index .page-width { --page-margin: 1.25rem; }
@media (min-width: 990px) {
  .template-index .page-width { --page-margin: 1.5rem; }
}

/* Stronger section headings on the homepage */
.template-index .h2 { font-weight: 800; letter-spacing: -0.01em; }

/* Softer defaults for lists and empty states */
.template-index .best-sellers__empty { opacity: .75; }
