{%- comment -%}
  Product Promo Popup Section

  - Two layouts: Text or Product
  - Cookie-based frequency control
  - Customizable colors and shadow
  - Product layout can open Quick View (Quick Add modal)
{%- endcomment -%}

{{ 'product-promo.css' | asset_url | stylesheet_tag }}
<script defer src="{{ 'product-promo.js' | asset_url }}"></script>

{%- liquid
  assign enabled = section.settings.enable | default: false

  assign cookie_hours = section.settings.cookie_hours | default: 24
  assign layout_mode = section.settings.layout | default: 'text'
  assign pos = section.settings.position | default: 'bottom_right'
  assign show_shadow = section.settings.show_shadow | default: true

  assign bg = section.settings.color_background | default: ''
  assign fg = section.settings.color_text | default: ''
  assign close_color = section.settings.color_close | default: ''
  assign btn_bg = section.settings.color_btn_bg | default: ''
  assign btn_fg = section.settings.color_btn_label | default: ''
  assign btn_bd = section.settings.color_btn_border | default: ''
  assign btn_bg_hover = section.settings.color_btn_bg_hover | default: ''
  assign btn_fg_hover = section.settings.color_btn_label_hover | default: ''
  assign btn_bd_hover = section.settings.color_btn_border_hover | default: ''

  assign style_vars = ''
  if bg != ''
    assign style_vars = style_vars | append: '--ppp-bg:' | append: bg | append: ';'
  endif
  if fg != ''
    assign style_vars = style_vars | append: '--ppp-fg:' | append: fg | append: ';'
  endif
  if close_color != ''
    assign style_vars = style_vars | append: '--ppp-close:' | append: close_color | append: ';'
  endif
  if btn_bg != ''
    assign style_vars = style_vars | append: '--ppp-btn-bg:' | append: btn_bg | append: ';'
  endif
  if btn_fg != ''
    assign style_vars = style_vars | append: '--ppp-btn-fg:' | append: btn_fg | append: ';'
  endif
  if btn_bd != ''
    assign style_vars = style_vars | append: '--ppp-btn-bd:' | append: btn_bd | append: ';'
  endif
  if btn_bg_hover != ''
    assign style_vars = style_vars | append: '--ppp-btn-bg-hover:' | append: btn_bg_hover | append: ';'
  endif
  if btn_fg_hover != ''
    assign style_vars = style_vars | append: '--ppp-btn-fg-hover:' | append: btn_fg_hover | append: ';'
  endif
  if btn_bd_hover != ''
    assign style_vars = style_vars | append: '--ppp-btn-bd-hover:' | append: btn_bd_hover | append: ';'
  endif

  assign has_icon = false
  if section.settings.icon != blank
    assign has_icon = true
  endif
  assign has_heading = false
  if section.settings.heading != blank
    assign has_heading = true
  endif
  assign has_text = false
  if section.settings.text != blank
    assign has_text = true
  endif
  assign btn_label = section.settings.button_label | default: 'OK'
  assign product_obj = section.settings.product
  assign product_desc = section.settings.product_additional_description
  assign cookie_name = section.settings.cookie_name | default: 'productPromoClosed'
-%}

{% if enabled %}
<product-promo
  class="ppp"
  data-cookie-name="{{ cookie_name }}"
  data-cookie-hours="{{ cookie_hours }}"
  data-layout="{{ layout_mode }}"
  {% if show_shadow %}data-shadow="true"{% endif %}
  {% if layout_mode == 'product' and product_obj %}
    data-product-url="{{ product_obj.url }}"
    data-product-id="{{ product_obj.selected_or_first_available_variant.id }}"
  {% endif %}
>
  <div class="ppp__dialog ppp__dialog--pos-{{ pos }}{% if show_shadow %} ppp__dialog--shadow{% endif %}"
       role="dialog" aria-modal="true"
       style="{{ style_vars }}">
    <button class="ppp__close" type="button" data-close aria-label="{{ 'sections.product_promo.button_aria' | t | default: 'Close product promo' }}">×</button>

    {%- if layout_mode == 'text' -%}
      <div class="ppp__content ppp__content--text">
        {%- if has_icon -%}
          <div class="ppp__icon">
            {%- assign img = section.settings.icon -%}
            {{ img | image_url: width: 96 | image_tag: loading: 'lazy', decoding: 'async', widths: '48, 64, 96', alt: img.alt | default: '' }}
          </div>
        {%- endif -%}
        {%- if has_heading -%}
          <h2 class="ppp__heading">{{ section.settings.heading }}</h2>
        {%- endif -%}
        {%- if has_text -%}
          <div class="ppp__text rte">{{ section.settings.text }}</div>
        {%- endif -%}
        <div class="ppp__actions">
          <button class="ppp__btn" data-close aria-label="{{ 'sections.product_promo.button_aria' | t | default: 'Close product promo' }}">{{ btn_label }}</button>
        </div>
      </div>
    {%- else -%}
      <div class="ppp__content ppp__content--product">
        {%- if product_obj -%}
          <div class="ppp__product">
            <div class="ppp__product-media">
              {%- assign pmedia = product_obj.featured_media -%}
              {{ pmedia | image_url: width: 320 | image_tag: loading: 'lazy', decoding: 'async', class: 'ppp__product-img', alt: product_obj.title }}
            </div>
            <div class="ppp__product-info">
              <h3 class="ppp__product-title">{{ product_obj.title }}</h3>
              {%- if product_desc != blank -%}
                <div class="ppp__product-desc rte">{{ product_desc }}</div>
              {%- endif -%}
              <div class="ppp__actions">
                <button class="ppp__btn"
                        data-quick-add
                        data-product-url="{{ product_obj.url }}"
                        data-product-id="{{ product_obj.selected_or_first_available_variant.id }}"
                        aria-label="{{ 'sections.product_promo.button_aria' | t: product_title: product_obj.title | default: 'Open quick view' }}">
                  {{ section.settings.button_label | default: 'View product' }}
                </button>
              </div>
            </div>
          </div>
        {%- else -%}
          {%- if request.design_mode -%}
            <p style="opacity:.7">Pick a product for the promo in section settings.</p>
          {%- endif -%}
        {%- endif -%}
      </div>
    {%- endif -%}
  </div>
</product-promo>
{% endif %}

{% schema %}
{
  "name": "Product promo",
  "enabled_on": { "groups": ["custom.popups"] },
  "class": "section",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "checkbox", "id": "enable", "label": "Enable", "default": false },
    { "type": "select", "id": "layout", "label": "Layout", "default": "text", "options": [
      { "value": "text", "label": "Text" },
      { "value": "product", "label": "Product" }
    ]},
    { "type": "select", "id": "position", "label": "Position", "default": "bottom_right", "options": [
      { "value": "bottom_right", "label": "Bottom right" },
      { "value": "bottom_left", "label": "Bottom left" },
      { "value": "top_right", "label": "Top right" },
      { "value": "top_left", "label": "Top left" }
    ]},
    { "type": "range", "id": "cookie_hours", "label": "Cookie time (hours)", "min": 0, "max": 168, "step": 2, "unit": "h", "default": 24 },
    { "type": "checkbox", "id": "show_shadow", "label": "Show shadow", "default": true },

    { "type": "header", "content": "Text" },
    { "type": "text", "id": "heading", "label": "Heading" },
    { "type": "image_picker", "id": "icon", "label": "Icon" },
    { "type": "richtext", "id": "text", "label": "Text" },
    { "type": "text", "id": "button_label", "label": "Button label", "default": "Learn more" },

    { "type": "header", "content": "Product" },
    { "type": "product", "id": "product", "label": "Product" },
    { "type": "richtext", "id": "product_additional_description", "label": "Product additional description" },

    { "type": "header", "content": "Colors" },
    { "type": "color", "id": "color_background", "label": "Background" },
    { "type": "color", "id": "color_text", "label": "Text" },
    { "type": "color", "id": "color_close", "label": "Button close" },
    { "type": "color", "id": "color_btn_bg", "label": "Button background" },
    { "type": "color", "id": "color_btn_label", "label": "Button label" },
    { "type": "color", "id": "color_btn_border", "label": "Button border" },
    { "type": "color", "id": "color_btn_bg_hover", "label": "Button background (hover)" },
    { "type": "color", "id": "color_btn_label_hover", "label": "Button label (hover)" },
    { "type": "color", "id": "color_btn_border_hover", "label": "Button border (hover)" }
  ],
  "presets": [
    { "name": "Product promo" }
  ]
}
{% endschema %}

{% stylesheet %}
/* Minimal guard if CSS asset fails to load */
.ppp { display: contents; }
{% endstylesheet %}
