# Theme Launch Checklist

Use this checklist to prepare the theme for launch. Each item is actionable and references where to configure it in the Shopify admin or this codebase.

## Required Pages & Templates

- [ ] Create pages and assign templates (Online Store → Pages):
  - [ ] FAQ → assign template: `page.faq`
  - [ ] Contact Us → assign template: `page.contact`
  - [ ] Shipping & Returns → assign template: `page.shipping-returns`
- [ ] Customer accounts (Online Store → Preferences → Customer accounts):
  - Enable accounts as needed. Templates are ready at:
    - Login: `/account/login` (`templates/customers/login.liquid`)
    - Register: `/account/register` (`templates/customers/register.liquid`)
    - Account dashboard: `/account` (`templates/customers/account.liquid`)
    - Addresses: `/account/addresses` (`templates/customers/addresses.liquid`)
    - Order details: from account orders list (`templates/customers/order.liquid`)
    - Activate account: via email (`templates/customers/activate_account.liquid`)
    - Reset password: via email (`templates/customers/reset_password.liquid`)
- [ ] System pages
  - 404 page (exists): `templates/404.json` + `sections/404.liquid` — customize copy/CTA.
  - Password page (exists): `templates/password.json` + `layout/password.liquid` + `sections/password.liquid` — customize copy/logo.
  - Gift card (exists): `templates/gift_card.liquid` — verify branding image and copy.

## Footer Links (Theme editor → Footer section)

- [ ] FAQ link: enable “Show FAQ link” and select the FAQ page; optional label.
- [ ] Contact link: enable “Show Contact link” and select the Contact page; optional label.
- [ ] Shipping & Returns link: enable and select the Shipping & Returns page; optional label.
- [ ] Account link: enable. If logged out it points to Login; if logged in it goes to Account.
- [ ] Footer menu (link list): add any additional links as desired.

## FAQ Page

- [ ] Review sample content in `templates/page.faq.json` and adjust blocks:
  - Category blocks group Q&A and power the sidebar/top bar navigation.
  - Q&A blocks render accordions with optional icons.
- [ ] Validate UX:
  - Deep links: copy link per-question, URL opens and expands the question.
  - Active category highlighting on scroll (desktop sidebar and mobile bar).

## Contact Page

- [ ] Map settings (Section → Contact):
  - [ ] Google Maps API key
  - [ ] Map query (place mode)
  - [ ] Map URL (opens in a new tab)
  - [ ] Map images (desktop/mobile fallbacks)
- [ ] Contact form: submit a test message and verify store notification emails (Settings → Notifications).

## Search Page

- [ ] Section settings (Search):
  - [ ] Filters type: Sidebar or Drawer
  - [ ] Show grid button: 3/4 column toggle
  - [ ] Paginate by: results per page
  - [ ] Pagination type: Pagination, Load more, or Infinite scrolling
  - [ ] Show color swatch in filters (requires Shopify Search & Discovery filters)
- [ ] Validate behavior on `/search`: typing, filters, grid toggle, pagination.
  - Color swatch mapping styles live in `snippets/color-swatch-mapping.liquid` — extend if needed.

## Navigation & Menus

- [ ] Header/primary navigation: add menu items for new pages (FAQ, Contact, Shipping & Returns) as desired.
- [ ] Footer: ensure the link list and quick links reflect your intended structure.

## Translations

- [ ] Add or review translations in `locales/en.default.json`:
  - `faq.categories`, `faq.link_label`, `faq.copy_link`, `faq.copied`
  - Any custom copy added to pages/sections
- [ ] Add additional locales if launching in multiple languages (duplicate and translate `locales/*.json`).

## Theme Settings & Branding

- [ ] Brand basics: logo(s), favicon, social sharing image.
- [ ] Colors and typography (Theme settings → Colors, Typography).
- [ ] Layout width and spacing (Theme settings → Layout).
- [ ] Check product grid/card options and collection templates as needed.

## QA & Accessibility

- [ ] Run linting: `shopify theme check`
- [ ] Test across devices (mobile/tablet/desktop) and major browsers.
- [ ] Keyboard navigation and focus states for menus, accordions, modals.
- [ ] Verify 404, password, gift card, cart, and account flows.
- [ ] Confirm image alt text and headings make sense for SEO/a11y.

## Performance

- [ ] Inspect page weight on key pages (Home, Collection, Product, Search, FAQ, Contact).
- [ ] Lazy loading and responsive images are in place; spot-check above-the-fold images.

## Launch & Deploy

- [ ] Pull latest changes before final edits: `shopify theme pull`
- [ ] Push to a development theme: `shopify theme push`
- [ ] Preview and share the dev theme link for stakeholder QA.
- [ ] When ready, publish the theme and set live. Avoid `--allow-live` pushes unless necessary.

## Quick URLs (after setup)

- FAQ: `/pages/faq`
- Contact: `/pages/contact`
- Shipping & Returns: `/pages/shipping-returns`
- Search: `/search`
- Login: `/account/login`
- Register: `/account/register`
- Account: `/account`
- Addresses: `/account/addresses`
- Password (when storefront locked): `/password`
- 404: any non-existent URL

