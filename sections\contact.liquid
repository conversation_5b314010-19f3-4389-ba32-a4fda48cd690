{% comment %}
  Contact Section (static on Contact page template)

  - Optional Google Maps embed (API key + query)
  - Fallback map images for desktop/mobile with optional external link
  - Customizable contact form labels/placeholders and heading

  Notes:
  - Map renders only if Map URL or Map image is provided (and/or API + query)
  - Map link opens in a new window
{% endcomment %}

{%- liquid
  assign heading = section.settings.heading | default: page.title
  assign map_api_key = section.settings.map_api_key
  assign map_query = section.settings.map_query
  assign map_url = section.settings.map_url
  assign map_image_desktop = section.settings.map_image_desktop
  assign map_image_mobile = section.settings.map_image_mobile

  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0

  assign has_embed = false
  if map_api_key != blank and map_query != blank
    assign has_embed = true
  endif

  assign has_map_visual = false
  if has_embed
    assign has_map_visual = true
  elsif map_image_desktop != blank or map_image_mobile != blank
    assign has_map_visual = true
  endif
  assign show_map = false
  if has_map_visual or map_url != blank
    assign show_map = true
  endif
-%}

<section class="contact" data-section="{{ section.id }}">
  <div class="contact__inner page-width">
    {%- if heading != blank -%}
      <h1 class="contact__title h2">{{ heading }}</h1>
    {%- endif -%}

    {%- if show_map -%}
      <div class="contact__map">
        {%- if map_url != blank -%}<a class="contact__map-link" href="{{ map_url }}" target="_blank" rel="noopener">{%- endif -%}
        <div class="contact__map-media">
          {%- if has_embed -%}
            <iframe
              class="contact__map-iframe"
              loading="lazy"
              allowfullscreen
              referrerpolicy="no-referrer-when-downgrade"
              src="https://www.google.com/maps/embed/v1/place?key={{ map_api_key | escape }}&q={{ map_query | url_encode }}">
            </iframe>
          {%- else -%}
            <picture>
              {%- if map_image_mobile != blank -%}
                <source media="(max-width: 749px)" srcset="{{ map_image_mobile | image_url: width: 1200 }} 1200w" sizes="100vw">
              {%- endif -%}
              {%- if map_image_desktop != blank -%}
                {{ map_image_desktop | image_url: width: 2000 | image_tag: widths: '1000, 1400, 1600, 2000', sizes: '100vw', class: 'contact__map-img', alt: 'Map location' }}
              {%- else -%}
                {{ map_image_mobile | image_url: width: 1600 | image_tag: widths: '800, 1000, 1200, 1600', sizes: '100vw', class: 'contact__map-img', alt: 'Map location' }}
              {%- endif -%}
            </picture>
          {%- endif -%}
        </div>
        {%- if map_url != blank -%}</a>{%- endif -%}
      </div>
    {%- endif -%}

    <div class="contact__body">
      {% form 'contact' %}
        {%- if form.posted_successfully? -%}
          <p class="contact__success" role="status">Thanks for contacting us. We will get back to you soon.</p>
        {%- endif -%}
        {%- if form.errors -%}
          <div class="contact__errors" role="alert">
            <p>Please correct the errors below:</p>
            <ul>
              {%- for field in form.errors -%}
                <li>{{ form.errors.translated_fields[field] | default: field | capitalize }} — {{ form.errors.messages[field] }}</li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}

        <div class="contact__grid">
          <div class="field">
            <label for="ContactFormName">{{ section.settings.name_label | default: 'Name' }}</label>
            <input
              type="text"
              id="ContactFormName"
              name="contact[name]"
              autocomplete="name"
              placeholder="{{ section.settings.name_placeholder | default: 'John Doe' }}"
              value="{{ form.name | default: customer.name }}"
              {% if form.errors contains 'name' %}aria-invalid="true" aria-describedby="ContactFormName-error"{% endif %}
            >
            {% if form.errors contains 'name' %}
              <small id="ContactFormName-error" class="field__error">{{ form.errors.messages.name }}</small>
            {% endif %}
          </div>

          <div class="field">
            <label for="ContactFormEmail">{{ section.settings.email_label | default: 'Email' }}</label>
            <input
              type="email"
              id="ContactFormEmail"
              name="contact[email]"
              autocomplete="email"
              placeholder="{{ section.settings.email_placeholder | default: '<EMAIL>' }}"
              value="{{ form.email | default: customer.email }}"
              {% if form.errors contains 'email' %}aria-invalid="true" aria-describedby="ContactFormEmail-error"{% endif %}
              required
            >
            {% if form.errors contains 'email' %}
              <small id="ContactFormEmail-error" class="field__error">{{ form.errors.messages.email }}</small>
            {% endif %}
          </div>

          <div class="field">
            <label for="ContactFormPhone">{{ section.settings.phone_label | default: 'Phone' }}</label>
            <input
              type="tel"
              id="ContactFormPhone"
              name="contact[phone]"
              autocomplete="tel"
              placeholder="{{ section.settings.phone_placeholder | default: '+****************' }}"
              value="{{ form.phone }}"
            >
          </div>

          <div class="field field--full">
            <label for="ContactFormMessage">{{ section.settings.message_label | default: 'Message' }}</label>
            <textarea
              id="ContactFormMessage"
              name="contact[body]"
              rows="6"
              placeholder="{{ section.settings.message_placeholder | default: 'How can we help?' }}"
            >{{ form.body }}</textarea>
          </div>
        </div>

        <button type="submit" class="button contact__submit">{{ section.settings.button_label | default: 'Send' }}</button>
      {% endform %}
    </div>
  </div>
</section>

{% stylesheet %}
  #shopify-section-{{ section.id }} {
    margin-top: {{ mobile_offset_top | default: 0 }}px;
    margin-bottom: {{ mobile_offset_bottom | default: 0 }}px;
  }
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} {
      margin-top: {{ desktop_offset_top | default: 0 }}px;
      margin-bottom: {{ desktop_offset_bottom | default: 0 }}px;
    }
  }

  .contact__inner {
    max-width: var(--page-width);
    margin: 0 auto;
    padding-left: var(--page-margin, 1rem);
    padding-right: var(--page-margin, 1rem);
  }

  .contact__title { margin-bottom: var(--heading-gap); }

  .contact__map { margin-bottom: 1.5rem; }
  .contact__map-media { position: relative; width: 100%; aspect-ratio: 16 / 9; background: #f3f4f6; overflow: hidden; border-radius: 8px; }
  .contact__map-iframe, .contact__map-img { position: absolute; inset: 0; width: 100%; height: 100%; border: 0; object-fit: cover; }
  .contact__map-link { display: block; text-decoration: none; color: inherit; }

  .contact__grid { display: grid; gap: 1rem; grid-template-columns: 1fr; }
  .field { display: grid; gap: .375rem; }
  .field input, .field textarea { padding: .75rem; border: 1px solid rgba(var(--color-foreground), .2); border-radius: 6px; background: var(--color-background); color: currentColor; }
  .field__error { color: #b91c1c; }
  .field--full { grid-column: 1 / -1; }

  @media (min-width: 750px) {
    .contact__grid { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  }

  .contact__success { background: rgba(16, 185, 129, .12); padding: .75rem 1rem; border-radius: 6px; margin-bottom: 1rem; }
  .contact__errors { background: rgba(239, 68, 68, .08); padding: .75rem 1rem; border-radius: 6px; margin-bottom: 1rem; }
  .contact__submit { margin-top: .5rem; }
{% endstylesheet %}

{% schema %}
{
  "name": "Contact",
  "settings": [
    { "type": "header", "content": "Map" },
    { "type": "text", "id": "map_api_key", "label": "Google maps API key" },
    { "type": "text", "id": "map_query", "label": "Map query", "info": "Place mode only (e.g., Company Name, City)" },
    { "type": "url", "id": "map_url", "label": "Map url" },
    { "type": "image_picker", "id": "map_image_desktop", "label": "Map image desktop" },
    { "type": "image_picker", "id": "map_image_mobile", "label": "Map image mobile" },

    { "type": "header", "content": "Contact form" },
    { "type": "text", "id": "heading", "label": "Heading" },
    { "type": "text", "id": "name_label", "label": "Name label", "default": "Name" },
    { "type": "text", "id": "name_placeholder", "label": "Name placeholder", "default": "John Doe" },
    { "type": "text", "id": "email_label", "label": "Email label", "default": "Email" },
    { "type": "text", "id": "email_placeholder", "label": "Email placeholder", "default": "<EMAIL>" },
    { "type": "text", "id": "phone_label", "label": "Phone label", "default": "Phone" },
    { "type": "text", "id": "phone_placeholder", "label": "Phone placeholder", "default": "+****************" },
    { "type": "text", "id": "message_label", "label": "Message label", "default": "Message" },
    { "type": "text", "id": "message_placeholder", "label": "Message placeholder", "default": "How can we help?" },
    { "type": "text", "id": "button_label", "label": "Button label", "default": "Send" },

    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ]
}
{% endschema %}
