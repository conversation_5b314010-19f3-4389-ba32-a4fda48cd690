/* Image Reveal on Scroll — clip-path-based reveal */
/* Direct target (e.g., apply class/attr directly on <img>) */
.reveal-on-scroll,
[data-reveal-on-scroll].reveal-on-scroll {
  will-change: clip-path;
  -webkit-clip-path: inset(100% 0 0 0);
  clip-path: inset(100% 0 0 0);
  --reveal-duration: 1000ms;
  --reveal-delay: 0ms;
  --reveal-ease: cubic-bezier(0.22, 1, 0.36, 1);
  transition: clip-path var(--reveal-duration) var(--reveal-ease) var(--reveal-delay);
}

.is-revealed.reveal-on-scroll,
[data-reveal-on-scroll].reveal-on-scroll.is-revealed {
  -webkit-clip-path: inset(0 0 0 0);
  clip-path: inset(0 0 0 0);
}

/* Wrapper target: if the data-attribute is on a media wrapper, animate its media children only */
[data-reveal-on-scroll] > img,
[data-reveal-on-scroll] > picture,
[data-reveal-on-scroll] img,
[data-reveal-on-scroll] picture,
[data-reveal-on-scroll] video {
  will-change: clip-path;
  -webkit-clip-path: inset(100% 0 0 0);
  clip-path: inset(100% 0 0 0);
  transition: clip-path var(--reveal-duration, 1000ms) var(--reveal-ease, cubic-bezier(0.22, 1, 0.36, 1)) var(--reveal-delay, 0ms);
}

[data-reveal-on-scroll].is-revealed > img,
[data-reveal-on-scroll].is-revealed > picture,
[data-reveal-on-scroll].is-revealed img,
[data-reveal-on-scroll].is-revealed picture,
[data-reveal-on-scroll].is-revealed video {
  -webkit-clip-path: inset(0 0 0 0);
  clip-path: inset(0 0 0 0);
}

/* Overlay elements: clip overlays themselves when they declare reveal */
[data-reveal-on-scroll].callout-banner__overlay,
[data-reveal-on-scroll].hero__overlay {
  will-change: clip-path;
  -webkit-clip-path: inset(100% 0 0 0);
  clip-path: inset(100% 0 0 0);
  transition: clip-path var(--reveal-duration, 1000ms) var(--reveal-ease, cubic-bezier(0.22, 1, 0.36, 1)) var(--reveal-delay, 0ms);
}
[data-reveal-on-scroll].callout-banner__overlay.is-revealed,
[data-reveal-on-scroll].hero__overlay.is-revealed {
  -webkit-clip-path: inset(0 0 0 0);
  clip-path: inset(0 0 0 0);
}

@media (prefers-reduced-motion: reduce) {
  .reveal-on-scroll,
  [data-reveal-on-scroll] {
    transition: none;
    -webkit-clip-path: none;
    clip-path: none;
  }
}
