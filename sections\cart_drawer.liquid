<section class="cart-drawer" id="cart-drawer" data-cart-drawer data-section-id="{{ section.id }}" aria-hidden="true" role="dialog" aria-modal="true">
  <div class="cart-drawer__overlay" data-cart-drawer-close></div>
  <div class="cart-drawer__content" role="document">
    <header class="cart-drawer__header">
      <h2 id="cart-drawer-heading">Shopping Cart ({{ cart.item_count }})</h2>
      <button type="button" class="cart-drawer__close" data-cart-drawer-close aria-label="Close cart">
        <span class="visually-hidden">Close cart</span>
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
      </button>
    </header>
    <div class="cart-drawer__live" aria-live="polite" aria-atomic="true"></div>
    {% liquid
      assign raw_threshold = section.settings.free_shipping_threshold | default: 0
      assign threshold = raw_threshold | at_least: 0 | floor | times: 100
    %}
    {% if threshold > 0 %}
      <div class="cart-progress" aria-live="polite">
        {% assign pct = 0 %}
        {% if threshold > 0 %}{% assign pct = cart.total_price | divided_by: threshold | times: 100 | at_most: 100 %}{% endif %}
        <div class="cart-progress__bar"><span style="width:{{ pct }}%"></span></div>
        <div class="cart-progress__msg">
          {% if cart.total_price < threshold and threshold > 0 %}
            You're <strong>{{ threshold | minus: cart.total_price | money }}</strong> away from free shipping!
          {% elsif threshold > 0 %}
            🎉 You've unlocked free shipping!
          {% endif %}
        </div>
      </div>
    {% endif %}
    <div class="cart-drawer__items">
      {% if cart.item_count == 0 %}
        <div class="cart-drawer__empty">
          <p>Your cart is empty</p>
          <button type="button" class="btn btn--secondary" data-cart-drawer-close>Continue Shopping</button>
        </div>
      {% else %}
        {% for item in cart.items %}
          <div data-line="{{ forloop.index }}">{% render 'cart-drawer-item', item: item %}</div>
        {% endfor %}
      {% endif %}
    </div>
    {% if cart.item_count > 0 %}
      <footer class="cart-drawer__footer" aria-label="Cart summary and checkout actions">
        <div class="cart-drawer__total" aria-live="polite">
          <span>{{ 'cart.general.total' | t | default: 'Total' }}</span>
          <strong data-cart-total>{{ cart.total_price | money }}</strong>
        </div>
        <div class="cart-drawer__actions" data-cart-actions>
          <a href="/cart" class="btn btn--secondary">{{ 'cart.general.view_cart' | t | default: 'View Cart' }}</a>
          <form action="/cart" method="post" class="checkout-form">
            <button type="submit" name="checkout" class="btn btn--primary">{{ 'cart.general.checkout' | t | default: 'Checkout' }}</button>
          </form>
        </div>
      </footer>
    {% endif %}
  </div>
</section>

{% schema %}
{
  "name": "Cart drawer",
  "settings": [
    { "type": "header", "content": "Free shipping" },
    { "type": "number", "id": "free_shipping_threshold", "label": "Threshold (shop currency, e.g. 120)", "default": 120 },
    { "type": "header", "content": "UI" },
    { "type": "checkbox", "id": "show_payment_icons", "label": "Show payment icons", "default": true }
  ]
}
{% endschema %}

{% comment %} Styles extracted to assets/cart-drawer.css {% endcomment %}
{% comment %}
  Cart drawer functionality is now handled by assets/cart-drawer-runtime.js
  This eliminates duplicate/competing JavaScript implementations
{% endcomment %}

 
