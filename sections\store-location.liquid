{% comment %}
  Store Location

  - Media (Google Map via Embed API if configured, otherwise image)
  - Contact/subscribe inline form (customer newsletter)
  - Info rows and Socials row via blocks
  - Reversible layout on desktop
{% endcomment %}

{%- liquid
  assign heading = section.settings.heading
  assign subheading = section.settings.subheading
  assign placeholder = section.settings.input_placeholder | default: 'Email address'
  assign btn_label = section.settings.button_label | default: 'Subscribe'
  assign btn_style = section.settings.button_style | default: 'primary'
  assign image = section.settings.image
  assign reverse = section.settings.reverse | default: false
  assign gmap_key = section.settings.google_maps_api_key
  assign gmap_query = section.settings.map_query

  assign container_width = section.settings.container_width | default: 'inherit'
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
  assign show_directions = section.settings.show_directions | default: true
  assign directions_label = section.settings.directions_label | default: 'Get directions'
  assign directions_url = section.settings.directions_url
  assign directions_new_tab = section.settings.directions_new_tab | default: true
  assign directions_style = section.settings.directions_style | default: 'secondary'
  assign show_copy = section.settings.show_copy | default: false
  assign address_text = section.settings.address_text | default: gmap_query
  assign copy_label = section.settings.copy_label | default: 'Copy address'
  assign copy_success = section.settings.copy_success | default: 'Copied!'
  assign show_phone = section.settings.show_phone | default: false
  assign phone_label = section.settings.phone_label | default: 'Call us'
  assign phone_number = section.settings.phone_number
  assign phone_style = section.settings.phone_style | default: 'link'
  assign show_email = section.settings.show_email | default: false
  assign email_label = section.settings.email_label | default: 'Email us'
  assign email_address = section.settings.email_address
  assign email_style = section.settings.email_style | default: 'link'
-%}

<section class="store-location{% if reverse %} store-location--reverse{% endif %}" data-section="{{ section.id }}">
  <div class="store-location__inner{% if container_width != 'inherit' %} store-location__inner--custom{% endif %} page-width"
       {% if container_width == 'narrow' %}style="--container-max: 90rem;"{% endif %}
       {% if container_width == 'wide' %}style="--container-max: 110rem;"{% endif %}
  >
    <div class="store-location__grid">
      <div class="store-location__media">
        {% if gmap_key and gmap_query %}
          <div class="store-location__map ratio ratio--16x9">
            <iframe
              loading="lazy"
              referrerpolicy="no-referrer-when-downgrade"
              src="https://www.google.com/maps/embed/v1/place?key={{ gmap_key | url_encode }}&q={{ gmap_query | url_encode }}"
              allowfullscreen
              title="Map: {{ gmap_query }}"
            ></iframe>
          </div>
        {% elsif image %}
          <figure class="store-location__image ratio ratio--16x9">
            {{ image | image_url: width: 2000 | image_tag: widths: '1000, 1400, 1600, 2000', sizes: '100vw', loading: 'lazy', decoding: 'async', alt: '' }}
          </figure>
        {% else %}
          <div class="store-location__placeholder ratio ratio--16x9">
            {{ 'image' | placeholder_svg_tag: 'placeholder' }}
          </div>
        {% endif %}
      </div>

      <div class="store-location__content">
        {% if heading %}<h2 class="store-location__heading h2">{{ heading }}</h2>{% endif %}
        {% if subheading %}<div class="store-location__subheading rte">{{ subheading }}</div>{% endif %}

        <div class="store-location__form">
          {% liquid
            assign btn_class = 'button'
            case btn_style
              when 'primary'
                assign btn_class = 'button button--primary'
              when 'secondary'
                assign btn_class = 'button button--secondary'
            endcase
          %}
          {% form 'customer', class: 'slo-form' %}
            {% if form.posted_successfully? %}
              <p class="slo-form__status" role="status">Thanks for subscribing!</p>
            {% endif %}
            {% if form.errors %}
              <div class="slo-form__status" role="alert">{{ form.errors | default_errors }}</div>
            {% endif %}
            <input type="hidden" name="contact[tags]" value="newsletter,store-location">
            <div class="slo-form__row">
              <input class="slo-form__input" type="email" name="contact[email]" placeholder="{{ placeholder }}" autocomplete="email" required>
              <button class="slo-form__button {{ btn_class }}" type="submit">{{ btn_label }}</button>
            </div>
          {% endform %}
        </div>

        {% if show_directions or show_copy or show_phone or show_email %}
          {% liquid
            assign dir_href = directions_url
            if dir_href == blank and gmap_query != blank
              assign dir_href = 'https://www.google.com/maps/dir/?api=1&destination=' | append: gmap_query | url_encode
            endif
            assign dir_class = 'button'
            case directions_style
              when 'primary'
                assign dir_class = 'button button--primary'
              when 'secondary'
                assign dir_class = 'button button--secondary'
              when 'link'
                assign dir_class = 'link'
            endcase
            assign phone_class = 'button'
            case phone_style
              when 'primary'
                assign phone_class = 'button button--primary'
              when 'secondary'
                assign phone_class = 'button button--secondary'
              when 'link'
                assign phone_class = 'link'
            endcase
            assign email_class = 'button'
            case email_style
              when 'primary'
                assign email_class = 'button button--primary'
              when 'secondary'
                assign email_class = 'button button--secondary'
              when 'link'
                assign email_class = 'link'
            endcase
          %}
          <div class="slo-actions">
            {% if show_directions and dir_href %}
              <a class="slo-actions__directions {{ dir_class }}" href="{{ dir_href }}" aria-label="Get directions to {{ gmap_query }}"{% if directions_new_tab %} target="_blank" rel="noopener"{% endif %}>{{ directions_label }}</a>
            {% endif %}
            {% if show_phone and phone_number %}
              <a class="slo-actions__phone {{ phone_class }}" href="tel:{{ phone_number | replace: ' ', '' }}" aria-label="Call {{ phone_number | escape }}">{{ phone_label }}</a>
            {% endif %}
            {% if show_email and email_address %}
              <a class="slo-actions__email {{ email_class }}" href="mailto:{{ email_address }}" aria-label="Email {{ email_address | escape }}">{{ email_label }}</a>
            {% endif %}
            {% if show_copy and address_text %}
              <button type="button" class="slo-actions__copy button button--secondary" data-copy-text="{{ address_text | escape }}" data-copy-success="{{ copy_success | escape }}">{{ copy_label }}</button>
              <span class="slo-actions__status" aria-live="polite"></span>
            {% endif %}
          </div>
        {% endif %}

        <ul class="store-location__list" role="list">
          {% for block in section.blocks %}
            {% case block.type %}
              {% when 'info' %}
                {% if block.settings.heading or block.settings.text %}
                  <li class="slo-item" {{ block.shopify_attributes }}>
                    {% if block.settings.icon %}
                      <span class="slo-item__icon">
                        {{ block.settings.icon | image_url: width: 48 | image_tag: widths: '24, 36, 48', sizes: '24px', alt: '' }}
                      </span>
                    {% endif %}
                    <div class="slo-item__body">
                      {% if block.settings.heading %}<div class="slo-item__heading">{{ block.settings.heading }}</div>{% endif %}
                      {% if block.settings.text %}<div class="slo-item__text rte">{{ block.settings.text }}</div>{% endif %}
                    </div>
                  </li>
                {% endif %}
              {% when 'socials' %}
                <li class="slo-item" {{ block.shopify_attributes }}>
                  {% if block.settings.icon %}
                    <span class="slo-item__icon">
                      {{ block.settings.icon | image_url: width: 48 | image_tag: widths: '24, 36, 48', sizes: '24px', alt: '' }}
                    </span>
                  {% endif %}
                  <div class="slo-item__body">
                    {% if block.settings.heading %}<div class="slo-item__heading">{{ block.settings.heading }}</div>{% endif %}
                    <div class="slo-item__socials">
                      {% assign socials = '' %}
                      {% if settings.social_facebook != blank %}<a href="{{ settings.social_facebook }}" target="_blank" rel="noopener">Facebook</a>{% endif %}
                      {% if settings.social_instagram != blank %}<a href="{{ settings.social_instagram }}" target="_blank" rel="noopener">Instagram</a>{% endif %}
                      {% if settings.social_tiktok != blank %}<a href="{{ settings.social_tiktok }}" target="_blank" rel="noopener">TikTok</a>{% endif %}
                      {% if settings.social_pinterest != blank %}<a href="{{ settings.social_pinterest }}" target="_blank" rel="noopener">Pinterest</a>{% endif %}
                      {% if settings.social_youtube != blank %}<a href="{{ settings.social_youtube }}" target="_blank" rel="noopener">YouTube</a>{% endif %}
                      {% if settings.social_twitter != blank %}<a href="{{ settings.social_twitter }}" target="_blank" rel="noopener">Twitter/X</a>{% endif %}
                      {% if settings.social_linkedin != blank %}<a href="{{ settings.social_linkedin }}" target="_blank" rel="noopener">LinkedIn</a>{% endif %}
                    </div>
                  </div>
                </li>
            {% endcase %}
          {% endfor %}
        </ul>
      </div>
    </div>
  </div>
</section>

{% style %}
  /* Offsets */
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; } }

  .store-location__inner.page-width { max-width: var(--container-max, var(--page-width)); margin: 0 auto; padding-left: var(--page-margin, 1rem); padding-right: var(--page-margin, 1rem); }
  .store-location__grid { display: grid; gap: 1.25rem; }
  @media (min-width: 990px) { .store-location__grid { grid-template-columns: 1fr 1fr; align-items: start; } }
  .store-location--reverse .store-location__grid { direction: rtl; }
  .store-location--reverse .store-location__media, .store-location--reverse .store-location__content { direction: ltr; }

  .ratio { position: relative; width: 100%; }
  .ratio--16x9 { padding-top: 56.25%; }
  .ratio > * { position: absolute; inset: 0; width: 100%; height: 100%; border: 0; }

  .store-location__heading { margin: 0 0 var(--heading-gap); }
  .store-location__subheading { color: rgba(var(--color-foreground), .8); margin-bottom: var(--text-gap); }

  .slo-form__row { display: grid; grid-template-columns: 1fr auto; gap: .5rem; }
  @media (max-width: 599px) { .slo-form__row { grid-template-columns: 1fr; } }
  .slo-form__input { padding: .75rem .9rem; border: 1px solid rgba(0,0,0,.15); border-radius: 6px; min-width: 0; }
  .slo-form__button { padding: .75rem 1rem; }
  .slo-form__status { margin-top: .5rem; }

  .store-location__list { display: grid; gap: .75rem; margin-top: 1.25rem; }
  .slo-item { display: grid; grid-template-columns: auto 1fr; gap: .75rem; align-items: start; }
  .slo-item__icon img { width: 24px; height: auto; }
  .slo-item__heading { font-weight: 600; margin-bottom: .125rem; }
  .slo-item__socials { display: inline-flex; flex-wrap: wrap; gap: .5rem .75rem; }
  .slo-actions { display: inline-flex; gap: .5rem; align-items: center; margin-top: .75rem; flex-wrap: wrap; }
  .slo-actions__status { margin-left: .25rem; font-size: .9rem; opacity: .8; }
{% endstyle %}

<script>
  (() => {
    const root = document.getElementById('shopify-section-{{ section.id }}');
    if (!root) return;
    const btn = root.querySelector('.slo-actions__copy');
    if (!btn) return;
    const status = root.querySelector('.slo-actions__status');
    btn.addEventListener('click', async () => {
      const text = btn.getAttribute('data-copy-text') || '';
      const success = btn.getAttribute('data-copy-success') || 'Copied!';
      try {
        await navigator.clipboard.writeText(text);
        if (status) { status.textContent = success; setTimeout(() => { status.textContent=''; }, 1500); }
      } catch(_) {
        const ta = document.createElement('textarea');
        ta.value = text; ta.style.position='fixed'; ta.style.opacity='0';
        document.body.appendChild(ta); ta.select();
        try { document.execCommand('copy'); if (status) { status.textContent = success; setTimeout(()=>{ status.textContent=''; }, 1500);} } catch(__) {}
        document.body.removeChild(ta);
      }
    });
  })();
</script>

{% schema %}
{
  "name": "Store location",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "text", "id": "heading", "label": "Heading", "default": "Visit our store" },
    { "type": "richtext", "id": "subheading", "label": "Subheading" },
    { "type": "text", "id": "input_placeholder", "label": "Input placeholder", "default": "Email address" },
    { "type": "text", "id": "button_label", "label": "Form button label", "default": "Subscribe" },
    { "type": "select", "id": "button_style", "label": "Form button style", "default": "primary", "options": [
      { "value": "primary", "label": "Primary" },
      { "value": "secondary", "label": "Secondary" }
    ] },
    { "type": "image_picker", "id": "image", "label": "Image" },
    { "type": "checkbox", "id": "reverse", "label": "Reverse content (desktop)", "default": false },
    { "type": "text", "id": "google_maps_api_key", "label": "Google maps API key" },
    { "type": "text", "id": "map_query", "label": "Map query", "info": "Place mode query, e.g., '1600 Amphitheatre Parkway, Mountain View, CA'" },

    { "type": "header", "content": "Actions" },
    { "type": "checkbox", "id": "show_directions", "label": "Show Get directions button", "default": true },
    { "type": "text", "id": "directions_label", "label": "Get directions label", "default": "Get directions" },
    { "type": "url", "id": "directions_url", "label": "Directions link override" },
    { "type": "checkbox", "id": "directions_new_tab", "label": "Open directions in a new tab", "default": true },
    { "type": "select", "id": "directions_style", "label": "Directions button style", "default": "secondary", "options": [
      { "value": "primary", "label": "Primary" },
      { "value": "secondary", "label": "Secondary" },
      { "value": "link", "label": "Link" }
    ] },
    { "type": "checkbox", "id": "show_copy", "label": "Show Copy address action", "default": false },
    { "type": "text", "id": "address_text", "label": "Address text (defaults to Map query)" },
    { "type": "text", "id": "copy_label", "label": "Copy address label", "default": "Copy address" },
    { "type": "text", "id": "copy_success", "label": "Copy success text", "default": "Copied!" },
    { "type": "checkbox", "id": "show_phone", "label": "Show phone link", "default": false },
    { "type": "text", "id": "phone_label", "label": "Phone label", "default": "Call us" },
    { "type": "text", "id": "phone_number", "label": "Phone number" },
    { "type": "select", "id": "phone_style", "label": "Phone button style", "default": "link", "options": [
      { "value": "primary", "label": "Primary" },
      { "value": "secondary", "label": "Secondary" },
      { "value": "link", "label": "Link" }
    ] },
    { "type": "checkbox", "id": "show_email", "label": "Show email link", "default": false },
    { "type": "text", "id": "email_label", "label": "Email label", "default": "Email us" },
    { "type": "text", "id": "email_address", "label": "Email address" },
    { "type": "select", "id": "email_style", "label": "Email button style", "default": "link", "options": [
      { "value": "primary", "label": "Primary" },
      { "value": "secondary", "label": "Secondary" },
      { "value": "link", "label": "Link" }
    ] },

    { "type": "header", "content": "Layout" },
    { "type": "select", "id": "container_width", "label": "Container width", "default": "inherit", "options": [
      { "value": "inherit", "label": "Inherit" },
      { "value": "narrow", "label": "Narrow" },
      { "value": "wide", "label": "Wide" }
    ] },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "blocks": [
    {
      "type": "info",
      "name": "Store information",
      "settings": [
        { "type": "image_picker", "id": "icon", "label": "Icon" },
        { "type": "text", "id": "heading", "label": "Heading" },
        { "type": "richtext", "id": "text", "label": "Text" }
      ]
    },
    {
      "type": "socials",
      "name": "Store socials",
      "settings": [
        { "type": "image_picker", "id": "icon", "label": "Icon" },
        { "type": "text", "id": "heading", "label": "Heading" }
      ]
    }
  ],
  "presets": [
    { "name": "Store location", "blocks": [ { "type": "info" }, { "type": "socials" } ] }
  ]
}
{% endschema %}
