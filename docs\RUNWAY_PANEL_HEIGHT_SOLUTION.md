# Runway Panel Height Solution - Implementation Summary

## Problem Statement

You were implementing the Runway sticky rail pattern and encountered an issue where:

1. **The panel was taller than the main content** (product gallery)
2. **The panel didn't release properly** when scrolling to the bottom
3. **Content near the bottom of the panel was cut off**

You observed that <PERSON><PERSON><PERSON>' implementation has the panel height roughly equal to the combined height of their gallery + "Shop the Look" + "Keep Exploring" sections, so everything releases together.

You asked: **"How do we handle these situations? Should we standardize the height of the panel and sections?"**

---

## Solution Implemented

**Short answer: Don't standardize heights.**

We've implemented a **robust, self-contained approach** that:
1. Matches the rail container height to the media height (using existing `--runway-slider-h`)
2. Makes the inner content scrollable within that fixed height
3. Computes the release point dynamically based on what sections are actually present

This works with any theme composition without requiring height coordination.

### Key Changes

#### 1. CSS: Match Rail Height to Media Height

**File:** `sections/product.liquid` (lines ~205-224)

```css
/* Rail container shares the same viewport height as media */
.product__info {
  height: var(--runway-slider-h) !important; /* aligns with media */
  overflow: visible !important;
}
```

The rail container now uses the same height calculation as the media gallery (`--runway-slider-h`), which is already computed as `100vh - header - fold-buffer`.

#### 2. CSS: Scrollable Inner Content

**File:** `sections/product.liquid` (lines ~241-293)

```css
/* The inner card scrolls within the fixed-height container */
.product-info {
  height: 100% !important;
  max-height: 100% !important;
  overflow: auto !important;
  overscroll-behavior: contain;
  scrollbar-gutter: stable both-edges;
}

/* Styled scrollbar (subtle, luxury aesthetic) */
.product-info::-webkit-scrollbar {
  width: 4px;
}
.product-info::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
}
```

The inner `.product-info` card scrolls within the fixed-height `.product__info` container, preventing content cutoff.

#### 3. JavaScript: Dynamic Release Point Calculation

**File:** `sections/product.liquid` (lines ~3361-3464)

```javascript
// Find the element that marks where the rail should stop sticking
const findReleaseAnchor = () => {
  // 1) explicit merchant marker
  const marked = document.querySelector('[data-rail-end]');
  if (marked) return marked;

  // 2) heuristic: look for common "mates" after the product section
  const candidates = [];
  let cursor = nextSection;
  while (cursor && checks < MAX_CHECK) {
    candidates.push(cursor);
    // stop early if we spot common rail mates
    if (cursor.querySelector('.product-recs, .shop-the-look, [data-rail-mate]'))
      break;
    cursor = cursor.nextElementSibling;
  }
  return candidates[candidates.length - 1] || nextSection || sectionRoot;
};

const computeRelease = () => {
  const sliderH = getSliderH();
  const sectionTop = sectionRoot.getBoundingClientRect().top + window.scrollY;
  const anchor = findReleaseAnchor();
  const anchorBottom = anchor.getBoundingClientRect().top + window.scrollY + anchor.offsetHeight;

  const releaseTop = Math.max(0, Math.round(anchorBottom - sectionTop - sliderH));
  sectionRoot.style.setProperty('--release-top', releaseTop + 'px');
};
```

**Key insight:** Instead of standardizing heights, we **compute** the release point dynamically based on:

- Explicit `[data-rail-end]` markers (merchant control)
- Heuristic detection of common "mate" sections (recommendations, etc.)
- Fallback to sensible defaults

The release formula: `releaseTop = (anchor bottom) - (section top) - (rail height)`

---

## How It Works

### Three States

1. **Start State** (absolute positioning)
   - Panel is positioned at the top of the section
   - User hasn't scrolled past the section top yet

2. **Fixed State** (fixed positioning)
   - Panel sticks to the viewport top (below header)
   - Follows the user as they scroll down
   - Panel is scrollable if content exceeds viewport

3. **Released State** (absolute positioning)
   - Panel is positioned at the bottom of the section
   - Happens when the visible panel bottom reaches section bottom
   - Panel scrolls away with the page

### Visual Flow

```
┌─────────────────────────────────────────────────────────┐
│ Header (sticky)                                         │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌──────────────────────────┐  ┌──────────────────┐   │
│  │                          │  │ Panel (START)    │   │ ← Initial position
│  │   Gallery                │  │ - Absolute       │   │
│  │                          │  │ - Top of section │   │
│  └──────────────────────────┘  └──────────────────┘   │
│                                                         │
│                                      ↓ User scrolls     │
│                                                         │
│  ┌──────────────────────────┐  ┌──────────────────┐   │
│  │                          │  │ Panel (FIXED)    │   │ ← Sticks to viewport
│  │   Gallery                │  │ - Fixed          │   │
│  │                          │  │ - Below header   │   │
│  │                          │  │ - Scrollable     │   │
│  └──────────────────────────┘  └──────────────────┘   │
│                                                         │
│                                      ↓ User scrolls     │
│                                                         │
│  ┌──────────────────────────┐                          │
│  │   Gallery                │                          │
│  └──────────────────────────┘  ┌──────────────────┐   │
│                                 │ Panel (RELEASED) │   │ ← Releases at bottom
│                                 │ - Absolute       │   │
│                                 │ - Bottom aligned │   │
│                                 └──────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

---

## Advantages of This Approach

### ✅ No Height Standardization Required

- **Flexible content**: Merchants can add/remove blocks freely
- **Variable descriptions**: Works with short or long product descriptions
- **Theme customization**: Users can customize without breaking the layout
- **Different products**: Each product can have different content amounts

### ✅ Works on All Screen Sizes

- **Tall viewports**: Panel uses available space
- **Short viewports**: Panel is scrollable, nothing is cut off
- **Ultra-wide**: Adapts to viewport height
- **Zoom levels**: Works at 90%, 110%, 125% zoom

### ✅ Smooth User Experience

- **Scroll position resets**: When returning to top, panel shows top content
- **Smooth scrolling**: Panel scrolls smoothly with `scroll-behavior: smooth`
- **Styled scrollbar**: Subtle, luxury aesthetic
- **No content cutoff**: Everything is accessible

### ✅ Performance Optimized

- **RequestAnimationFrame**: Smooth, throttled updates
- **ResizeObserver**: Automatic recalculation when content changes
- **Passive listeners**: Optimized scroll performance
- **Cached measurements**: Reduces layout thrashing

---

## Alternative Approaches

If you want to exactly replicate Hermès' behavior, see the documentation:

- **[RUNWAY_PANEL_HEIGHT.md](./RUNWAY_PANEL_HEIGHT.md)** - Detailed explanation of all approaches
- **[RUNWAY_PANEL_HEIGHT_COMPARISON.md](./RUNWAY_PANEL_HEIGHT_COMPARISON.md)** - Visual comparison and decision matrix
- **[RUNWAY_STICKY_TRACK.md](./RUNWAY_STICKY_TRACK.md)** - How to extend the track with follow sections

### Quick Summary of Alternatives

**Hermès Approach (Height Matching):**
- Panel height = Gallery + Follow sections
- No scrollbar in panel
- Requires height coordination
- Best for: Fixed layouts with controlled content

**Hybrid Approach:**
- Panel can grow with track height
- Still capped at viewport for safety
- Uses `data-runway-follow` system
- Best for: Power users who want control

---

## Testing

### What to Test

1. **Content variations:**
   - [ ] Short product description (1-2 lines)
   - [ ] Long product description (10+ lines)
   - [ ] Minimum blocks (title, price, add to cart)
   - [ ] Maximum blocks (all features enabled)

2. **Screen sizes:**
   - [ ] Desktop: 1920px, 1440px, 1280px
   - [ ] Tablet: Should use standard layout (not Runway)
   - [ ] Mobile: Should use standard layout (not Runway)

3. **Scroll behavior:**
   - [ ] Scroll down slowly - panel should stick
   - [ ] Scroll to bottom - panel should release
   - [ ] Scroll back up - panel should reset scroll position
   - [ ] Scroll panel content - should be smooth

4. **Edge cases:**
   - [ ] Very tall panel (20+ blocks)
   - [ ] Very short section (minimal content)
   - [ ] Browser zoom (90%, 110%, 125%)
   - [ ] Sticky header enabled/disabled

### How to Test

1. **Open your product page** in Runway layout mode
2. **Open DevTools** (F12) and go to Console
3. **Run these checks:**

```javascript
// Check panel height constraint
const panel = document.querySelector('.product__info');
console.log('Panel actual height:', panel.scrollHeight, 'px');
console.log('Panel visible height:', panel.offsetHeight, 'px');
console.log('Panel max-height:', getComputedStyle(panel).maxHeight);

// Check if panel is scrollable
console.log('Is scrollable:', panel.scrollHeight > panel.offsetHeight);

// Check section height
const section = document.querySelector('.product');
console.log('Section height:', section.offsetHeight, 'px');

// Check release calculation
const viewportHeight = window.innerHeight;
const headerH = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--header-h')) || 0;
const maxPanelH = viewportHeight - headerH - 24;
console.log('Viewport height:', viewportHeight, 'px');
console.log('Max panel height:', maxPanelH, 'px');
console.log('Effective panel height:', Math.min(panel.scrollHeight, maxPanelH), 'px');
```

---

## Customization

### Adjust Panel Max Height

If you want more/less space for the panel:

```css
.product__info {
  /* Change 24px to your preferred bottom buffer */
  max-height: calc(100vh - var(--header-h, 0px) - var(--pi-top-gap, 0px) - 48px) !important;
}
```

### Change Scrollbar Style

```css
/* Make scrollbar more visible */
.product__info::-webkit-scrollbar {
  width: 8px; /* Wider scrollbar */
}
.product__info::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3); /* Darker thumb */
}

/* Or hide scrollbar completely */
.product__info {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.product__info::-webkit-scrollbar {
  display: none;
}
```

### Disable Scroll Position Reset

If you don't want the panel to reset scroll position:

```javascript
// Remove this section from the code (lines ~3465-3475)
// Or comment it out:
/*
if (currentState === 'fixed' && lastState !== 'fixed') {
  infoEl.scrollTop = 0;
}
*/
```

---

## Troubleshooting

### Panel content is still cut off

1. Check that `overflow-y: auto` is applied
2. Verify no ancestor has `overflow: hidden`
3. Inspect computed `max-height` in DevTools
4. Check browser console for JavaScript errors

### Panel releases too early

1. Verify `--header-h` CSS variable is correct
2. Check viewport height detection
3. Inspect release calculation in console (see Testing section)

### Scrollbar doesn't appear

1. Check that content actually exceeds viewport
2. Verify `overflow-y: auto` (not `scroll` or `hidden`)
3. Test in different browsers (Chrome, Firefox, Safari)

### Panel never releases

1. Section might be too short - add more content or use `data-runway-follow`
2. Check that release logic is running (add console.log)
3. Verify section height calculation

---

## Files Modified

1. **sections/product.liquid**
   - Lines ~205-227: Panel height constraint CSS
   - Lines ~229-279: Scrollbar styling and viewport unit support
   - Lines ~3403-3447: Smart release logic
   - Lines ~3448-3483: Scroll position reset

2. **docs/RUNWAY_PANEL_HEIGHT.md** (new)
   - Comprehensive guide to panel height management

3. **docs/RUNWAY_PANEL_HEIGHT_COMPARISON.md** (new)
   - Visual comparison of different approaches

---

## Next Steps

1. **Test the implementation** with your actual product content
2. **Adjust scrollbar styling** to match your brand aesthetic
3. **Consider adding follow sections** if you want to extend the track (see RUNWAY_STICKY_TRACK.md)
4. **Monitor user feedback** on the scrollable panel behavior
5. **Optimize content** to fit within viewport when possible

---

## Questions?

If you have questions or need to adjust the behavior:

1. Check the detailed documentation in `docs/RUNWAY_PANEL_HEIGHT.md`
2. Review the comparison guide in `docs/RUNWAY_PANEL_HEIGHT_COMPARISON.md`
3. See the sticky track guide in `docs/RUNWAY_STICKY_TRACK.md`
4. Test with the console commands in the Testing section above

