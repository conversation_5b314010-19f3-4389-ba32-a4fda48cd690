{% comment %}
  404 Section (static on 404 template)

  Settings:
  - Label, Heading, Subheading, Text
  - Button label/link/style
  - Social heading, Show socials (reads URLs from Theme Settings → Social)
  - Images: first/second (desktop/mobile variants)
  - Layout: desktop/mobile offset top/bottom
{% endcomment %}

{%- liquid
  assign label = section.settings.label
  assign heading = section.settings.heading | default: '404.title' | t
  assign subheading = section.settings.subheading
  assign text = section.settings.text
  assign btn_label = section.settings.button_label | default: '404.back_to_shopping' | t
  assign btn_link = section.settings.button_link | default: routes.all_products_collection_url
  assign btn_style = section.settings.button_style | default: 'primary'

  assign show_socials = section.settings.show_socials | default: false
  assign social_heading = section.settings.social_heading

  assign img1_d = section.settings.image_first_desktop
  assign img1_m = section.settings.image_first_mobile
  assign img2_d = section.settings.image_second_desktop
  assign img2_m = section.settings.image_second_mobile

  assign mot = section.settings.mobile_offset_top | default: 0
  assign mob = section.settings.mobile_offset_bottom | default: 0
  assign dot = section.settings.desktop_offset_top | default: 0
  assign dob = section.settings.desktop_offset_bottom | default: 0
-%}

<section class="not-found" data-section="{{ section.id }}">
  <div class="not-found__inner page-width">
    <div class="not-found__images">
      {% if img1_d or img1_m %}
        <figure class="not-found__image">
          <picture>
            {% if img1_m %}<source media="(max-width: 749px)" srcset="{{ img1_m | image_url: width: 1200 }} 1200w" sizes="100vw">{% endif %}
            {% if img1_d %}
              {{ img1_d | image_url: width: 2000 | image_tag: widths: '1000, 1400, 1600, 2000', sizes: '50vw', loading: 'lazy', decoding: 'async', alt: '' }}
            {% endif %}
          </picture>
        </figure>
      {% endif %}

      {% if img2_d or img2_m %}
        <figure class="not-found__image">
          <picture>
            {% if img2_m %}<source media="(max-width: 749px)" srcset="{{ img2_m | image_url: width: 1200 }} 1200w" sizes="100vw">{% endif %}
            {% if img2_d %}
              {{ img2_d | image_url: width: 2000 | image_tag: widths: '1000, 1400, 1600, 2000', sizes: '50vw', loading: 'lazy', decoding: 'async', alt: '' }}
            {% endif %}
          </picture>
        </figure>
      {% endif %}
    </div>

    <div class="not-found__content">
      {% if label != blank %}
        <div class="not-found__eyebrow">{{ label }}</div>
      {% endif %}
      {% if heading != blank %}
        <h1 class="not-found__title h2">{{ heading }}</h1>
      {% endif %}
      {% if subheading != blank %}
        <p class="not-found__subtitle">{{ subheading }}</p>
      {% endif %}
      {% if text != blank %}
        <div class="not-found__text rte">{{ text }}</div>
      {% endif %}

      {% if btn_label != blank and btn_link != blank %}
        {% assign btn_class = 'button' %}
        {% case btn_style %}
          {% when 'primary' %}
            {% assign btn_class = 'button button--primary' %}
          {% when 'secondary' %}
            {% assign btn_class = 'button button--secondary' %}
          {% when 'primary_outline' %}
            {% assign btn_class = 'button button--primary button--outline' %}
          {% when 'secondary_outline' %}
            {% assign btn_class = 'button button--secondary button--outline' %}
        {% endcase %}
        <a class="{{ btn_class }}" href="{{ btn_link }}">{{ btn_label }}</a>
      {% endif %}

      {% if show_socials %}
        {% assign has_social = false %}
        {% if settings.social_facebook or settings.social_instagram or settings.social_tiktok or settings.social_pinterest or settings.social_youtube or settings.social_twitter or settings.social_linkedin %}
          {% assign has_social = true %}
        {% endif %}
        {% if has_social %}
          {% if social_heading != blank %}
            <h3 class="not-found__social-heading">{{ social_heading }}</h3>
          {% endif %}
          <div class="not-found__socials">
            {% if settings.social_facebook %}<a href="{{ settings.social_facebook }}" target="_blank" rel="noopener">Facebook</a>{% endif %}
            {% if settings.social_instagram %}<a href="{{ settings.social_instagram }}" target="_blank" rel="noopener">Instagram</a>{% endif %}
            {% if settings.social_tiktok %}<a href="{{ settings.social_tiktok }}" target="_blank" rel="noopener">TikTok</a>{% endif %}
            {% if settings.social_pinterest %}<a href="{{ settings.social_pinterest }}" target="_blank" rel="noopener">Pinterest</a>{% endif %}
            {% if settings.social_youtube %}<a href="{{ settings.social_youtube }}" target="_blank" rel="noopener">YouTube</a>{% endif %}
            {% if settings.social_twitter %}<a href="{{ settings.social_twitter }}" target="_blank" rel="noopener">Twitter</a>{% endif %}
            {% if settings.social_linkedin %}<a href="{{ settings.social_linkedin }}" target="_blank" rel="noopener">LinkedIn</a>{% endif %}
          </div>
        {% endif %}
      {% endif %}
    </div>
  </div>
</section>

{% stylesheet %}
  #shopify-section-{{ section.id }} { margin-top: {{ mot }}px; margin-bottom: {{ mob }}px; }
  @media (min-width: 990px){ #shopify-section-{{ section.id }} { margin-top: {{ dot }}px; margin-bottom: {{ dob }}px; } }

  .not-found { padding: 2rem 0; }
  .not-found__inner { max-width: var(--page-width); margin: 0 auto; }

  .not-found__images { display: grid; grid-template-columns: 1fr; gap: 1rem; margin-bottom: 1.5rem; }
  .not-found__image { margin: 0; border-radius: 8px; overflow: hidden; }
  @media (min-width: 990px){ .not-found__images { grid-template-columns: 1fr 1fr; } }

  .not-found__content { text-align: center; max-width: 720px; margin: 0 auto; }
  .not-found__eyebrow { text-transform: uppercase; letter-spacing: .08em; font-size: .85rem; opacity: .7; margin-bottom: .25rem; }
  .not-found__title { margin: 0 0 .5rem; }
  .not-found__subtitle { opacity: .8; margin: 0 0 .75rem; }
  .not-found__text { margin-bottom: 1rem; }
  .not-found__social-heading { margin: 1rem 0 .5rem; }
  .not-found__socials { display: flex; gap: .5rem; justify-content: center; flex-wrap: wrap; }
  .not-found__socials a { display:inline-flex; align-items:center; justify-content:center; min-width: 36px; height: 36px; padding: 0 .6rem; border: 1px solid rgba(0,0,0,.2); border-radius: 999px; text-decoration: none; color: inherit; }

  .button { display: inline-flex; align-items: center; justify-content: center; padding: .75rem 1.1rem; border-radius: 4px; text-decoration: none; border: 1px solid transparent; }
  .button--primary { background: var(--color-foreground); color: var(--color-background); border-color: var(--color-foreground); }
  .button--secondary { background: transparent; color: var(--color-foreground); border-color: var(--color-foreground); }
  .button--outline { background: transparent; }
{% endstylesheet %}

{% schema %}
{
  "name": "404",
  "settings": [
    { "type": "text", "id": "label", "label": "Label" },
    { "type": "text", "id": "heading", "label": "Heading" },
    { "type": "text", "id": "subheading", "label": "Subheading" },
    { "type": "richtext", "id": "text", "label": "Text" },
    { "type": "text", "id": "button_label", "label": "Button label" },
    { "type": "url", "id": "button_link", "label": "Button link" },
    { "type": "select", "id": "button_style", "label": "Button style", "default": "primary", "options": [
      { "value": "primary", "label": "Primary" },
      { "value": "secondary", "label": "Secondary" },
      { "value": "primary_outline", "label": "Primary with border" },
      { "value": "secondary_outline", "label": "Secondary with border" }
    ]},

    { "type": "text", "id": "social_heading", "label": "Social heading" },
    { "type": "checkbox", "id": "show_socials", "label": "Show socials", "default": true },

    { "type": "image_picker", "id": "image_first_desktop", "label": "Image first desktop" },
    { "type": "image_picker", "id": "image_first_mobile", "label": "Image first mobile" },
    { "type": "image_picker", "id": "image_second_desktop", "label": "Image second desktop" },
    { "type": "image_picker", "id": "image_second_mobile", "label": "Image second mobile" },

    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ]
}
{% endschema %}
