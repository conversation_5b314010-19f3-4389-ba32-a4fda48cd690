/* Product Promo Popup Web Component */
(function(){
  'use strict';

  class ProductPromo extends HTMLElement {
    connectedCallback() {
      this.cookieName = this.dataset.cookieName || 'productPromoClosed';
      this.cookieHours = parseInt(this.dataset.cookieHours || '24', 10);
      this.dialog = this.querySelector('.ppp__dialog');
      this.backdrop = this.querySelector('[data-backdrop]');
      this.closeBtn = this.querySelector('[data-close]');

      if (!this.dialog) return;

      this.close = this.close.bind(this);
      this.handleBackdropClick = this.handleBackdropClick.bind(this);

      if (this.closeBtn) this.closeBtn.addEventListener('click', this.close);
      if (this.backdrop) this.backdrop.addEventListener('click', this.handleBackdropClick);

      // Auto-open on first paint if cookie not set
      if (!this._hasCookie()) {
        requestAnimationFrame(() => this.open());
      }
    }

    disconnectedCallback() {
      if (this.closeBtn) this.closeBtn.removeEventListener('click', this.close);
      if (this.backdrop) this.backdrop.removeEventListener('click', this.handleBackdropClick);
    }

    open() {
      this.setAttribute('open', '');
    }

    close() {
      this.removeAttribute('open');
      this._setCookie();
    }

    handleBackdropClick(ev) {
      if (ev.target === this.backdrop) this.close();
    }

    _setCookie() {
      if (this.cookieHours <= 0) return;
      const d = new Date();
      d.setTime(d.getTime() + (this.cookieHours * 60 * 60 * 1000));
      document.cookie = `${this.cookieName}=1; expires=${d.toUTCString()}; path=/`;
    }

    _hasCookie() {
      return document.cookie.split('; ').some(c => c.startsWith(`${this.cookieName}=`));
    }
  }

  if (!customElements.get('product-promo')) {
    customElements.define('product-promo', ProductPromo);
  }
})();

