/* Global button variants */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: .75rem 1.1rem;
  border-radius: 4px;
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
}

.button--primary {
  background: var(--color-foreground);
  color: var(--color-background);
  border-color: var(--color-foreground);
}
.button--secondary {
  background: transparent;
  color: var(--color-foreground);
  border-color: var(--color-foreground);
}
.button--outline { background: transparent; }

.button--full-width { width: 100%; }

.button:disabled,
.button[disabled] {
  opacity: .6;
  cursor: not-allowed;
}

/* Hover and focus states */
.button--primary:hover { opacity: .92; }
.button--secondary:hover,
.button--outline:hover { background: var(--button-hover-overlay-color, color-mix(in oklab, var(--color-foreground) var(--button-hover-mix), transparent)); }

.button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px var(--button-focus-ring-color, color-mix(in oklab, var(--color-foreground) var(--button-focus-ring-mix), transparent));
}
