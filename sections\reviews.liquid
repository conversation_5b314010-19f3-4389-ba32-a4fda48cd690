{% comment %}
  Reviews section: rotating testimonials with four layouts
  Layouts: Base, Small product, Big product, Big image
{% endcomment %}

{%- liquid
  assign layout = section.settings.layout | default: 'base'
  assign heading = section.settings.heading
  assign autoplay = section.settings.autoplay | default: false
  assign autoplay_speed = section.settings.autoplay_speed | default: 5000

  assign container_width = section.settings.container_width | default: 'inherit'
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="reviews reviews--{{ layout }} color-{{ section.settings.color_scheme | default: 'background-1' }}" data-section="{{ section.id }}"
  data-autoplay="{{ autoplay }}" data-autoplay-speed="{{ autoplay_speed }}">
  <div class="reviews__inner{% if container_width != 'inherit' %} reviews__inner--custom{% endif %} page-width">
    {% if heading %}
      <h2 class="reviews__heading">{{ heading }}</h2>
    {% endif %}

    <div class="reviews__viewport" data-viewport>
      <div class="reviews__track" data-track>
        {%- for block in section.blocks -%}
          {%- liquid
            assign author_img = block.settings.author_image
            assign author_name = block.settings.author_name
            assign author_role = block.settings.author_role
            assign text = block.settings.text
            assign p = block.settings.product
            assign add_img = block.settings.additional_image

            assign media_obj = nil
            if layout == 'big_image'
              if add_img
                assign media_obj = add_img
              elsif p and p.featured_media
                assign media_obj = p.featured_media
              endif
            elsif layout contains 'product'
              if add_img
                assign media_obj = add_img
              elsif p and p.featured_media
                assign media_obj = p.featured_media
              endif
            endif
          -%}
          <article class="review" role="group" aria-roledescription="slide" aria-label="{{ forloop.index }} / {{ section.blocks.size }}" {{ block.shopify_attributes }}>
            <div class="review__body">
              {% if media_obj and layout != 'base' %}
                <div class="review__media">
                  {{ media_obj | image_url: width: 1200 | image_tag: widths: '600, 800, 1000, 1200', sizes: '(min-width: 990px) 40vw, 100vw', loading: 'lazy', decoding: 'async', alt: '' }}
                  {% if p and layout contains 'product' %}
                    <div class="review__product-title">
                      <a href="{{ p.url }}">{{ p.title }}</a>
                    </div>
                  {% endif %}
                </div>
              {% endif %}

              <div class="review__content">
                {% if text %}
                  <div class="review__text rte">{{ text }}</div>
                {% endif %}
                <div class="review__author">
                  {% if author_img %}
                    <span class="review__author-image">
                      {{ author_img | image_url: width: 96 | image_tag: widths: '48, 64, 96', sizes: '48px', loading: 'lazy', decoding: 'async', alt: author_name | default: '' }}
                    </span>
                  {% endif %}
                  <span class="review__author-meta">
                    {% if author_name %}<span class="review__author-name">{{ author_name }}</span>{% endif %}
                    {% if author_role %}<span class="review__author-role">{{ author_role }}</span>{% endif %}
                  </span>
                </div>
              </div>
            </div>
          </article>
        {%- endfor -%}
      </div>

      <div class="reviews__dots" data-dots aria-hidden="true"></div>
    </div>
  </div>
</section>

<script>
(() => {
  const root = document.getElementById('shopify-section-{{ section.id }}');
  if (!root) return;
  const track = root.querySelector('[data-track]');
  const slides = Array.from(track?.children || []);
  const dotsWrap = root.querySelector('[data-dots]');
  if (!track || !slides.length) return;

  const autoplay = root.querySelector('.reviews')?.getAttribute('data-autoplay') === 'true';
  const autoplaySpeed = parseInt(root.querySelector('.reviews')?.getAttribute('data-autoplay-speed') || '5000', 10);
  let index = 0; let timer = null;

  // Initialize
  slides.forEach((s, i) => s.classList.toggle('is-active', i === 0));

  // Dots
  if (dotsWrap) {
    dotsWrap.innerHTML = slides.map((_, i) => `<button class="reviews__dot${i===0?' is-active':''}" data-index="${i}" aria-label="Slide ${i+1}"></button>`).join('');
    dotsWrap.addEventListener('click', (e) => {
      const btn = e.target.closest('button[data-index]'); if (!btn) return;
      goTo(parseInt(btn.getAttribute('data-index')));
    });
  }

  function updateDots(){
    const dots = dotsWrap?.querySelectorAll('.reviews__dot') || [];
    dots.forEach((d,i)=>d.classList.toggle('is-active', i===index));
  }
  function goTo(i){
    if (i === index) return;
    if (i < 0) i = slides.length - 1; else if (i >= slides.length) i = 0;
    slides[index]?.classList.remove('is-active');
    slides[i]?.classList.add('is-active');
    index = i; updateDots(); resetAutoplay();
  }
  function next(){ goTo(index + 1); }

  function resetAutoplay(){
    if (!autoplay) return;
    if (timer) clearTimeout(timer);
    timer = setTimeout(next, autoplaySpeed);
  }
  if (autoplay) resetAutoplay();
})();
</script>

{% style %}
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; } }

  #shopify-section-{{ section.id }} .reviews__inner.page-width { max-width: var(--container-max, var(--page-width)); margin: 0 auto; padding: 0 1rem; }
  {% if container_width == 'narrow' %}
    #shopify-section-{{ section.id }} .reviews__inner { --container-max: 90rem; }
  {% elsif container_width == 'wide' %}
    #shopify-section-{{ section.id }} .reviews__inner { --container-max: 110rem; }
  {% endif %}

  #shopify-section-{{ section.id }} .reviews__heading { margin: 0 0 1rem; font-size: clamp(1.25rem, 3vw, 2rem); }
  #shopify-section-{{ section.id }} .reviews__viewport { position: relative; }
  #shopify-section-{{ section.id }} .reviews__track { width: 100%; }
  #shopify-section-{{ section.id }} .review { display: none; }
  #shopify-section-{{ section.id }} .review.is-active { display: block; }

  /* Base layout: text-centric */
  #shopify-section-{{ section.id }} .review__body { display: grid; gap: 1rem; align-items: center; }
  #shopify-section-{{ section.id }} .review__content { display: grid; gap: 0.5rem; }
  #shopify-section-{{ section.id }} .review__text { font-size: 1rem; }
  #shopify-section-{{ section.id }} .review__author { display: inline-flex; align-items: center; gap: .5rem; margin-top: .5rem; }
  #shopify-section-{{ section.id }} .review__author-image img { border-radius: 999px; width: 48px; height: 48px; object-fit: cover; }
  #shopify-section-{{ section.id }} .review__author-name { font-weight: 600; }
  #shopify-section-{{ section.id }} .review__author-role { opacity: .8; margin-left: .25rem; }

  /* Small product: compact media aside */
  #shopify-section-{{ section.id }} .reviews--small_product .review__body { grid-template-columns: 160px 1fr; }
  #shopify-section-{{ section.id }} .reviews--small_product .review__media img { width: 100%; height: auto; border-radius: 10px; display: block; }

  /* Big product: larger media */
  #shopify-section-{{ section.id }} .reviews--big_product .review__body { grid-template-columns: minmax(240px, 40%) 1fr; }
  #shopify-section-{{ section.id }} .reviews--big_product .review__media img { width: 100%; height: auto; border-radius: 12px; display: block; }

  /* Big image: full-width image stacked */
  #shopify-section-{{ section.id }} .reviews--big_image .review__body { grid-template-columns: 1fr; }
  #shopify-section-{{ section.id }} .reviews--big_image .review__media { order: -1; }
  #shopify-section-{{ section.id }} .reviews--big_image .review__media img { width: 100%; height: auto; border-radius: 12px; display: block; margin-bottom: 0.75rem; }

  /* Dots */
  #shopify-section-{{ section.id }} .reviews__dots { display: inline-flex; gap: .5rem; margin-top: .75rem; }
  #shopify-section-{{ section.id }} .reviews__dot { width: 9px; height: 9px; border-radius: 999px; background: currentColor; opacity: .4; border: 0; }
  #shopify-section-{{ section.id }} .reviews__dot.is-active { opacity: 1; }

  @media (max-width: 749px){
    #shopify-section-{{ section.id }} .reviews--small_product .review__body,
    #shopify-section-{{ section.id }} .reviews--big_product .review__body { grid-template-columns: 1fr; }
  }
{% endstyle %}

{% schema %}
{
  "name": "Reviews",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "select", "id": "layout", "label": "Layout", "default": "base", "options": [
      { "value": "base", "label": "Base" },
      { "value": "small_product", "label": "Small product" },
      { "value": "big_product", "label": "Big product" },
      { "value": "big_image", "label": "Big image" }
    ] },
    { "type": "text", "id": "heading", "label": "Heading" },
    { "type": "checkbox", "id": "autoplay", "label": "Autoplay", "default": false },
    { "type": "range", "id": "autoplay_speed", "label": "Autoplay speed (ms)", "min": 1000, "max": 9000, "step": 500, "default": 5000 },

    { "type": "header", "content": "Colors" },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "background-1" },

    { "type": "header", "content": "Layout" },
    { "type": "select", "id": "container_width", "label": "Container width", "default": "inherit", "options": [
      { "value": "inherit", "label": "Inherit" },
      { "value": "narrow", "label": "Narrow" },
      { "value": "wide", "label": "Wide" }
    ] },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "blocks": [
    {
      "type": "review",
      "name": "Review",
      "settings": [
        { "type": "image_picker", "id": "author_image", "label": "Author image" },
        { "type": "text", "id": "author_name", "label": "Author name" },
        { "type": "text", "id": "author_role", "label": "Author role" },
        { "type": "richtext", "id": "text", "label": "Text" },
        { "type": "product", "id": "product", "label": "Product" },
        { "type": "image_picker", "id": "additional_image", "label": "Additional image" }
      ]
    }
  ],
  "presets": [
    {
      "name": "Reviews",
      "blocks": [
        { "type": "review", "settings": { "author_name": "Alex C.", "author_role": "Verified Buyer", "text": "<p>Loved the quality and quick delivery!</p>" } },
        { "type": "review", "settings": { "author_name": "Priya N.", "author_role": "Customer", "text": "<p>Great value. The product exceeded expectations.</p>" } },
        { "type": "review", "settings": { "author_name": "Marco R.", "author_role": "Designer", "text": "<p>Beautiful design and easy to use.</p>" } }
      ]
    }
  ]
}
{% endschema %}
