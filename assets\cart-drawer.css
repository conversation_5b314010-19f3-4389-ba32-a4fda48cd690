/* Base */
.visually-hidden{position:absolute!important;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0 0 0 0);white-space:nowrap;border:0;}
.cart-drawer{position:fixed;top:0;right:0;width:100%;height:100%;z-index:9999;visibility:hidden;opacity:0;transition:visibility 0s 0.3s,opacity 0.3s ease;}
.cart-drawer.is-open{visibility:visible;opacity:1;transition:visibility 0s,opacity 0.3s ease;}
.cart-drawer__overlay{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.5);cursor:pointer;}
.cart-drawer__content{position:absolute;top:0;right:0;width:100%;max-width:500px;height:100%;background:#fff;display:flex;flex-direction:column;transform:translateX(100%);transition:transform .3s ease;}
.cart-drawer.is-open .cart-drawer__content{transform:translateX(0);}
.cart-drawer__header{display:flex;align-items:center;justify-content:space-between;padding:20px 2rem;border-bottom:1px solid #e5e5e5;}
.cart-drawer__header h2{margin:0;font-size:1.25rem;font-weight:600;}
.cart-drawer__close{background:none;border:none;cursor:pointer;padding:4px;border-radius:4px;transition:background-color .2s;}
.cart-drawer__close:hover{background:#f5f5f5;}
.cart-drawer__items{flex:1;overflow:auto;padding:0 2rem;}
.cart-drawer__footer{padding:20px 20px 24px;border-top:1px solid #e5e5e5;display:flex;flex-direction:column;gap:16px;background:linear-gradient(#fff,#fff) padding-box,linear-gradient(to bottom,rgba(0,0,0,0.05),transparent) border-box;}
.cart-drawer__total{display:flex;justify-content:space-between;align-items:flex-start;gap:12px;font-size:.95rem;font-weight:500;}
.cart-drawer__total span{color:#444;}
.cart-drawer__total strong{font-size:1rem;}
.cart-drawer__actions{display:grid;grid-template-columns:1fr 1fr;gap:12px;align-items:stretch;}
.cart-drawer__actions .btn{width:100%;}
.cart-drawer__actions form{margin:0;}
.cart-drawer__actions .btn--primary{font-size:.95rem;letter-spacing:.25px;}
@media (max-width:480px){
  .cart-drawer__actions{grid-template-columns:1fr;}
  .cart-drawer__actions a{order:2;}
  .cart-drawer__actions form{order:1;}
}
.cart-progress{padding:12px 20px 0;}
.cart-progress__bar{background:#eee;height:6px;border-radius:3px;margin:6px 0 4px;overflow:hidden;}
.cart-progress__bar span{display:block;height:100%;background:#000;width:0;transition:width .3s ease;}
.btn{padding:12px 16px;border-radius:6px;font-weight:500;text-align:center;text-decoration:none;border:none;cursor:pointer;transition:background-color .2s,box-shadow .2s,color .2s;}
.btn--primary{background:#000;color:#fff;box-shadow:0 0 0 1px #000 inset,0 2px 4px rgba(0,0,0,.1);}
.btn--primary:hover{background:#222;}
.btn--primary:active{background:#111;}
.btn--secondary{background:#f5f5f5;color:#000;border:1px solid #e5e5e5;}
.btn--secondary:hover{background:#e5e5e5;}
/* Busy state - visual feedback */
[data-cart-drawer][data-busy] .cart-drawer__items{opacity:.55;}
[data-cart-drawer][data-busy] .cart-drawer__footer{opacity:.55;}
/* Disable action buttons while busy to prevent double submits */
[data-cart-drawer][data-busy] [data-qty-minus],
[data-cart-drawer][data-busy] [data-qty-plus],
[data-cart-drawer][data-busy] [data-remove],
[data-cart-drawer][data-busy] .cart-drawer__footer button,
[data-cart-drawer][data-busy] .cart-drawer__footer a {
  pointer-events: none;
  cursor: progress;
  opacity: 0.6;
}
/* Keep quantity inputs fully interactive and focusable */
[data-cart-drawer] [data-line-qty] {
  pointer-events: auto !important;
  cursor: text !important;
  opacity: 1 !important;
}
/* Smooth transitions for cart items */
[data-cart-drawer] [data-line] {
  transition: opacity 0.2s ease;
}
@media (max-width:768px){.cart-drawer__content{max-width:100%;}.cart-drawer__actions{grid-template-columns:1fr;}}
@media (prefers-reduced-motion:reduce){.cart-drawer,.cart-drawer__content,.cart-drawer.is-open,.cart-drawer.is-open .cart-drawer__content,.cart-progress__bar span{transition:none!important;}}

