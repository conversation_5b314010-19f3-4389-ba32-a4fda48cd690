<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Image Zoom Test</title>
  <style>
    body {
      margin: 0;
      font-family: system-ui, sans-serif;
    }
    
    .section {
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    }
    
    .section img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .content {
      position: absolute;
      color: white;
      text-align: center;
      z-index: 1;
      padding: 2rem;
      background: rgba(0,0,0,0.5);
      border-radius: 10px;
    }
    
    .spacer {
      height: 50vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f5f5;
    }
  </style>
  <link rel="stylesheet" href="assets/image-zoom.css">
</head>
<body>
  <!-- Above the fold section - should zoom on load -->
  <div class="section">
    <div class="zoom-on-scroll" data-zoom-on-scroll data-zoom-to="1.2">
      <img src="https://picsum.photos/seed/above-fold/1920/1080.jpg" alt="Above fold image">
    </div>
    <div class="content">
      <h1>Above the Fold</h1>
      <p>This image SHOULD zoom on page load</p>
    </div>
  </div>
  
  <div class="spacer">
    <p>Scroll down to see the zoom effect</p>
  </div>
  
  <!-- Below the fold section - should zoom when scrolled into view -->
  <div class="section">
    <div class="zoom-on-scroll" data-zoom-on-scroll data-zoom-to="1.2">
      <img src="https://picsum.photos/seed/below-fold/1920/1080.jpg" alt="Below fold image">
    </div>
    <div class="content">
      <h1>Below the Fold</h1>
      <p>This image SHOULD zoom when scrolled into view</p>
    </div>
  </div>
  
  <div class="spacer">
    <p>Another section</p>
  </div>
  
  <!-- Another below the fold section -->
  <div class="section">
    <div class="zoom-on-scroll" data-zoom-on-scroll data-zoom-to="1.15">
      <img src="https://picsum.photos/seed/third-section/1920/1080.jpg" alt="Third section image">
    </div>
    <div class="content">
      <h1>Third Section</h1>
      <p>This should also zoom when scrolled into view</p>
    </div>
  </div>
  
  <script src="assets/image-zoom.js"></script>
</body>
</html>
