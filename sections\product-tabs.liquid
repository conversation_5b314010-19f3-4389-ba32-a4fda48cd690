{% comment %}
  Product Tabs Section

  - Displays multiple tabs, each with up to 4 selected products
  - General header with optional label, heading, and text
  - Configurable container width and top/bottom offsets
  - Accessible tablist/tabpanel semantics
{% endcomment %}

{%- liquid
  assign label = section.settings.label
  assign heading = section.settings.heading
  assign text = section.settings.text

  assign container_width = section.settings.container_width | default: 'inherit'
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0

  assign button_aria = section.settings.button_aria
-%}

<section class="product-tabs" data-section="{{ section.id }}">
  <div class="product-tabs__inner{% if container_width != 'inherit' %} product-tabs__inner--custom{% endif %} page-width"
       {% if container_width == 'narrow' %}style="--container-max: 90rem;"{% endif %}
       {% if container_width == 'wide' %}style="--container-max: 110rem;"{% endif %}
  >
    {% if label or heading or text %}
      <header class="product-tabs__header">
        {% if label %}<div class="product-tabs__eyebrow">{{ label }}</div>{% endif %}
        {% if heading %}<h2 class="product-tabs__title h2">{{ heading }}</h2>{% endif %}
        {% if text %}<div class="product-tabs__text rte">{{ text }}</div>{% endif %}
      </header>
    {% endif %}

    <div class="product-tabs__tabs" role="tablist" aria-label="{{ heading | default: 'products.product.tabs' | t }}">
      {% for block in section.blocks %}
        {% if block.type == 'tab' %}
          {% assign i = forloop.index0 %}
          <button
            id="Tab-{{ section.id }}-{{ i }}"
            class="product-tabs__tab{% if i == 0 %} is-active{% endif %}"
            role="tab"
            type="button"
            aria-selected="{% if i == 0 %}true{% else %}false{% endif %}"
            tabindex="{% if i == 0 %}0{% else %}-1{% endif %}"
            aria-controls="Panel-{{ section.id }}-{{ i }}"
            {% if button_aria != blank %}aria-label="{{ button_aria }}: {{ block.settings.heading | default: 'Tab' }}"{% endif %}
            {{ block.shopify_attributes }}
          >
            {{ block.settings.heading | default: 'Tab' }}
          </button>
        {% endif %}
      {% endfor %}
    </div>

    <div class="product-tabs__panels">
      {% for block in section.blocks %}
        {% if block.type == 'tab' %}
          {% assign i = forloop.index0 %}
          {% assign source = block.settings.content_source | default: 'manual' %}
          {% assign limit_count = block.settings.product_count | default: 4 %}
          <div
            id="Panel-{{ section.id }}-{{ i }}"
            class="product-tabs__panel"
            role="tabpanel"
            aria-labelledby="Tab-{{ section.id }}-{{ i }}"
            {% unless i == 0 %}hidden{% endunless %}
          >
            <ul class="product-tabs__grid" role="list">
              {% if source == 'collection' and block.settings.collection %}
                {% for product in block.settings.collection.products limit: limit_count %}
                  <li class="product-tabs__item">{%- render 'card-product', card_product: product, columns: 4, show_swatches: false -%}</li>
                {% endfor %}
              {% else %}
                {% assign rendered = 0 %}
                {% if block.settings.product_1 and rendered < limit_count %}
                  <li class="product-tabs__item">{%- render 'card-product', card_product: block.settings.product_1, columns: 4, show_swatches: false -%}</li>
                  {% assign rendered = rendered | plus: 1 %}
                {% endif %}
                {% if block.settings.product_2 and rendered < limit_count %}
                  <li class="product-tabs__item">{%- render 'card-product', card_product: block.settings.product_2, columns: 4, show_swatches: false -%}</li>
                  {% assign rendered = rendered | plus: 1 %}
                {% endif %}
                {% if block.settings.product_3 and rendered < limit_count %}
                  <li class="product-tabs__item">{%- render 'card-product', card_product: block.settings.product_3, columns: 4, show_swatches: false -%}</li>
                  {% assign rendered = rendered | plus: 1 %}
                {% endif %}
                {% if block.settings.product_4 and rendered < limit_count %}
                  <li class="product-tabs__item">{%- render 'card-product', card_product: block.settings.product_4, columns: 4, show_swatches: false -%}</li>
                  {% assign rendered = rendered | plus: 1 %}
                {% endif %}
              {% endif %}
            </ul>

            {% assign cta_url = blank %}
            {% if block.settings.show_view_all %}
              {% if source == 'collection' and block.settings.collection %}
                {% assign cta_url = block.settings.collection.url %}
              {% elsif block.settings.view_all_url != blank %}
                {% assign cta_url = block.settings.view_all_url %}
              {% endif %}
            {% endif %}
            {% if cta_url %}
              {% assign cta_label = block.settings.view_all_label %}
              {% if cta_label == blank %}
                {% assign cta_label = 'collections.general.view_all' | t %}
                {% if cta_label contains 'translation missing' %}
                  {% assign cta_label = 'View all' %}
                {% endif %}
              {% endif %}
              {% assign cta_aria = block.settings.view_all_aria | default: cta_label %}
              {% assign cta_style = block.settings.view_all_style | default: 'primary' %}
              {% assign cta_class = 'button' %}
              {% case cta_style %}
                {% when 'primary' %}
                  {% assign cta_class = 'button button--primary' %}
                {% when 'secondary' %}
                  {% assign cta_class = 'button button--secondary' %}
                {% when 'link' %}
                  {% assign cta_class = 'link' %}
              {% endcase %}
              <div class="product-tabs__cta-wrap">
                <a class="product-tabs__cta {{ cta_class }}" href="{{ cta_url }}" aria-label="{{ cta_aria | escape }}">{{ cta_label }}</a>
              </div>
            {% endif %}
          </div>
        {% endif %}
      {% endfor %}
    </div>
  </div>
</section>

<script>
  (() => {
    const root = document.getElementById('shopify-section-{{ section.id }}');
    if (!root) return;
    const tabs = root.querySelectorAll('.product-tabs__tab');
    const panels = root.querySelectorAll('.product-tabs__panel');
    if (!tabs.length || !panels.length) return;

    function activate(index) {
      tabs.forEach((t, i) => {
        const active = i === index;
        t.classList.toggle('is-active', active);
        t.setAttribute('aria-selected', active ? 'true' : 'false');
      });
      panels.forEach((p, i) => {
        if (i === index) {
          p.removeAttribute('hidden');
        } else {
          p.setAttribute('hidden', '');
        }
      });
    }

    tabs.forEach((btn, i) => btn.addEventListener('click', () => activate(i)));
    // Keyboard nav: ArrowLeft/Right, Home/End
    const onKey = (ev) => {
      const keys = ['ArrowLeft','ArrowRight','Home','End'];
      if (!keys.includes(ev.key)) return;
      ev.preventDefault();
      const current = Array.from(tabs).findIndex(t => t.getAttribute('aria-selected') === 'true');
      let next = current;
      if (ev.key === 'ArrowRight') next = (current + 1) % tabs.length;
      else if (ev.key === 'ArrowLeft') next = (current - 1 + tabs.length) % tabs.length;
      else if (ev.key === 'Home') next = 0;
      else if (ev.key === 'End') next = tabs.length - 1;
      activate(next);
      tabs[next]?.focus();
    };
    root.querySelector('.product-tabs__tabs')?.addEventListener('keydown', onKey);
  })();
</script>

{% style %}
  /* Offsets */
  #shopify-section-{{ section.id }} {
    margin-top: {{ mobile_offset_top }}px;
    margin-bottom: {{ mobile_offset_bottom }}px;
  }
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} {
      margin-top: {{ desktop_offset_top }}px;
      margin-bottom: {{ desktop_offset_bottom }}px;
    }
  }

  .product-tabs__inner.page-width {
    max-width: var(--container-max, var(--page-width));
    margin: 0 auto;
    padding-left: var(--page-margin, 1rem);
    padding-right: var(--page-margin, 1rem);
  }

  .product-tabs__header { text-align: center; margin-bottom: 1.5rem; }
  .product-tabs__eyebrow { font-size: .9rem; letter-spacing: .06em; text-transform: uppercase; opacity: .8; margin-bottom: .25rem; }
  .product-tabs__title { margin: 0 0 .5rem; }
  .product-tabs__text { color: rgba(var(--color-foreground), .8); max-width: 52rem; margin: 0 auto; }

  .product-tabs__tabs { display: flex; gap: .75rem; justify-content: center; margin: 1.5rem 0 1rem; flex-wrap: wrap; }
  .product-tabs__tab { appearance: none; background: transparent; border: 0; border-bottom: 2px solid transparent; padding: .5rem .75rem; cursor: pointer; font-weight: 600; }
  .product-tabs__tab.is-active { border-color: currentColor; }

  .product-tabs__panels { margin-top: 1rem; }
  .product-tabs__grid { display: grid; gap: 1rem; grid-template-columns: repeat(2, minmax(0, 1fr)); }
  @media (min-width: 990px) { .product-tabs__grid { grid-template-columns: repeat(4, minmax(0, 1fr)); } }
  .product-tabs__cta-wrap { text-align: center; margin-top: 1rem; }
{% endstyle %}

{% schema %}
{
  "name": "Product tabs",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "text", "id": "heading", "label": "Heading", "default": "Product tabs" },
    { "type": "text", "id": "label", "label": "Label" },
    { "type": "richtext", "id": "text", "label": "Text" },

    { "type": "header", "content": "Layout" },
    {
      "type": "select",
      "id": "container_width",
      "label": "Container width",
      "default": "inherit",
      "options": [
        { "value": "inherit", "label": "Inherit" },
        { "value": "narrow", "label": "Narrow" },
        { "value": "wide", "label": "Wide" }
      ]
    },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },

    { "type": "header", "content": "Additional information" },
    { "type": "text", "id": "button_aria", "label": "Button aria", "info": "Message for tab button aria-label." }
  ],
  "blocks": [
    {
      "type": "tab",
      "name": "Tab",
      "settings": [
        { "type": "text", "id": "heading", "label": "Heading", "default": "Featured" },
        { "type": "select", "id": "content_source", "label": "Content source", "default": "manual", "options": [
          { "value": "manual", "label": "Manual products" },
          { "value": "collection", "label": "Collection" }
        ] },
        { "type": "collection", "id": "collection", "label": "Collection" },
        { "type": "range", "id": "product_count", "label": "Products to show", "min": 1, "max": 4, "step": 1, "default": 4 },
        { "type": "product", "id": "product_1", "label": "Product 1" },
        { "type": "product", "id": "product_2", "label": "Product 2" },
        { "type": "product", "id": "product_3", "label": "Product 3" },
        { "type": "product", "id": "product_4", "label": "Product 4" },
        { "type": "checkbox", "id": "show_view_all", "label": "Show View all CTA", "default": true },
        { "type": "text", "id": "view_all_label", "label": "View all label" },
        { "type": "text", "id": "view_all_aria", "label": "View all aria-label" },
        { "type": "url", "id": "view_all_url", "label": "View all link (manual source)" },
        { "type": "select", "id": "view_all_style", "label": "View all style", "default": "primary", "options": [
          { "value": "primary", "label": "Primary" },
          { "value": "secondary", "label": "Secondary" },
          { "value": "link", "label": "Link" }
        ] }
      ],
      "limit": 8
    }
  ],
  "presets": [
    {
      "name": "Product tabs",
      "blocks": [
        { "type": "tab" },
        { "type": "tab" }
      ]
    }
  ]
}
{% endschema %}
