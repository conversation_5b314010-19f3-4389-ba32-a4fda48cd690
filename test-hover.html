<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Card Hover</title>
    <style>
        .card-wrapper {
            border: 1px solid #ccc;
            margin: 20px;
            padding: 20px;
            width: 300px;
        }
        
        .card__media .media {
            position: relative;
            aspect-ratio: 3/4;
            overflow: hidden;
            background: #f0f0f0;
        }
        
        .card__media .media .card-media__primary,
        .card__media .media .card-media__preview {
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            display: block;
            object-fit: cover;
            object-position: center top;
        }
        
        .card-media__preview {
            opacity: 0;
            pointer-events: none;
            transition: opacity 160ms ease;
        }
        
        .card__swatches {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .swatch {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid transparent;
            cursor: pointer;
        }
        
        .swatch--red { background-color: #dc2626; }
        .swatch--blue { background-color: #2563eb; }
        .swatch--green { background-color: #16a34a; }
    </style>
</head>
<body>
    <div class="card-wrapper" data-enable-hover-preview="true">
        <div class="card__media">
            <div class="media">
                <img class="card-media__primary" 
                     src="https://picsum.photos/400/533?random=1" 
                     alt="Product Image">
                <img class="card-media__preview" 
                     alt="" 
                     aria-hidden="true" 
                     hidden 
                     width="1" 
                     height="1">
            </div>
        </div>
        
        <div class="card__swatches">
            <button class="swatch swatch--red" 
                    data-variant-id="123"
                    data-media-id="456"
                    data-src="https://picsum.photos/400/533?random=2"
                    data-srcset="https://picsum.photos/400/533?random=2 400w"
                    data-sizes="400px">
            </button>
            <button class="swatch swatch--blue" 
                    data-variant-id="124"
                    data-media-id="457"
                    data-src="https://picsum.photos/400/533?random=3"
                    data-srcset="https://picsum.photos/400/533?random=3 400w"
                    data-sizes="400px">
            </button>
            <button class="swatch swatch--green" 
                    data-variant-id="125"
                    data-media-id="458"
                    data-src="https://picsum.photos/400/533?random=4"
                    data-srcset="https://picsum.photos/400/533?random=4 400w"
                    data-sizes="400px">
            </button>
        </div>
    </div>

    <script src="./assets/variant-hover.js"></script>
    
    <div id="debug-info" style="margin: 20px; padding: 10px; background: #f0f0f0; border: 1px solid #ccc;">
        <h3>Debug Info:</h3>
        <div id="debug-output"></div>
    </div>
    
    <script>
        // Override console.log to show in the page
        const debugOutput = document.getElementById('debug-output');
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const div = document.createElement('div');
            div.textContent = args.join(' ');
            debugOutput.appendChild(div);
        };
        
        const originalWarn = console.warn;
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            const div = document.createElement('div');
            div.style.color = 'red';
            div.textContent = 'WARN: ' + args.join(' ');
            debugOutput.appendChild(div);
        };
        
        // Test hover manually
        setTimeout(() => {
            const firstSwatch = document.querySelector('.swatch');
            if (firstSwatch) {
                console.log('Testing manual hover on first swatch');
                const event = new PointerEvent('pointerenter', { bubbles: true });
                firstSwatch.dispatchEvent(event);
            }
        }, 2000);
    </script>
</body>
</html>
