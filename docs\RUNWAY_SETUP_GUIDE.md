# Runway Sticky Track Setup Guide

## Quick Setup (3 Steps)

This guide shows you exactly how to enable the Hermès-style sticky panel that stays visible through multiple sections.

---

## Step 1: Add Attributes to Your Product Template

The sticky track system uses `data-runway-follow` and `data-runway-stop` attributes to know which sections to include. You need to add these attributes to the section wrappers in your theme's layout file.

### Option A: Edit via Theme Customizer (Recommended)

Unfortunately, Shopify's theme customizer doesn't expose section wrapper attributes directly. You'll need to use **Option B** below.

### Option B: Edit `layout/theme.liquid` Directly

Open `layout/theme.liquid` and find where sections are rendered. Look for this pattern:

```liquid
{%- for section in template.sections -%}
  <div id="shopify-section-{{ section.id }}" class="shopify-section">
    {% render section %}
  </div>
{%- endfor -%}
```

**Replace it with this enhanced version:**

```liquid
{%- for section in template.sections -%}
  {%- liquid
    # Determine if this section should be part of the Runway sticky track
    assign runway_follow = false
    assign runway_stop = false
    
    # Add sections that should extend the sticky track
    if section.type == 'product-recommendations'
      assign runway_follow = true
    endif
    
    # Add sections where the panel should release (stop tracking)
    if section.type == 'footer' or section.type == 'newsletter'
      assign runway_stop = true
    endif
  -%}
  
  <div id="shopify-section-{{ section.id }}" 
       class="shopify-section"
       {% if runway_follow %}data-runway-follow{% endif %}
       {% if runway_stop %}data-runway-stop{% endif %}>
    {% render section %}
  </div>
{%- endfor -%}
```

### Option C: Manual Template Editing (Most Control)

If you want precise control per template, edit the specific template JSON files:

#### For `templates/product.json`:

Since JSON doesn't support attributes on section wrappers, you'll need to use **Option B** above (theme.liquid) or add a small JavaScript snippet to your theme that adds the attributes dynamically.

Add this to `layout/theme.liquid` before the closing `</body>` tag:

```liquid
{% if template.name == 'product' %}
<script>
(function(){
  // Add data-runway-follow to recommendation sections
  const recSection = document.querySelector('#shopify-section-template--{{ template.suffix }}__recommendations');
  if (recSection) recSection.setAttribute('data-runway-follow', '');
  
  // Add data-runway-stop to footer
  const footerSection = document.querySelector('.shopify-section:has(.footer)');
  if (footerSection) footerSection.setAttribute('data-runway-stop', '');
})();
</script>
{% endif %}
```

---

## Step 2: Verify Auto-Detection is Working

The system has smart defaults. Even without explicit attributes, it will automatically include sections that contain:

- `.product-recs` (product recommendations)
- `.recently-viewed` (recently viewed products)
- `[data-recent-container]` (recent items)
- `[data-keep-exploring]` (keep exploring sections)

**To test:**

1. Open a product page with the Runway layout (stack layout)
2. Open browser DevTools (F12)
3. In the Console, type:
   ```javascript
   getComputedStyle(document.querySelector('.product')).getPropertyValue('--runway-track-h')
   ```
4. You should see a value larger than just the gallery height (e.g., `"1200px"` instead of `"800px"`)

---

## Step 3: Customize Which Sections to Include

### Add More Sections to the Track

Edit the section type checks in the code from Step 1, Option B:

```liquid
# Add sections that should extend the sticky track
if section.type == 'product-recommendations'
  assign runway_follow = true
endif

# Add your custom sections here:
if section.type == 'complementary-products'
  assign runway_follow = true
endif

if section.type == 'recently-viewed'
  assign runway_follow = true
endif

if section.type == 'shop-the-look'
  assign runway_follow = true
endif
```

### Set a Stop Point

```liquid
# Add sections where the panel should release (stop tracking)
if section.type == 'footer'
  assign runway_stop = true
endif

if section.type == 'newsletter'
  assign runway_stop = true
endif

if section.type == 'promo-banner'
  assign runway_stop = true
endif
```

---

## Advanced: JavaScript-Based Attribute Injection

If you prefer a JavaScript-only approach (no Liquid editing), add this to `layout/theme.liquid`:

```liquid
{% if template.name == 'product' %}
<script>
(function(){
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
  
  function init() {
    // Get the product section
    const productSection = document.querySelector('.shopify-section:has(.product)');
    if (!productSection) return;
    
    // Find all sections after the product section
    let currentSection = productSection.nextElementSibling;
    
    while (currentSection && currentSection.classList.contains('shopify-section')) {
      // Check section content to determine if it should follow
      const hasRecs = currentSection.querySelector('.product-recs');
      const hasRecent = currentSection.querySelector('[data-recent-container]');
      const hasKeepExploring = currentSection.querySelector('[data-keep-exploring]');
      const hasFooter = currentSection.querySelector('.footer');
      const hasNewsletter = currentSection.querySelector('.newsletter');
      
      // Add data-runway-follow to recommendation-type sections
      if (hasRecs || hasRecent || hasKeepExploring) {
        currentSection.setAttribute('data-runway-follow', '');
      }
      
      // Add data-runway-stop to footer/newsletter sections
      if (hasFooter || hasNewsletter) {
        currentSection.setAttribute('data-runway-stop', '');
        break; // Stop processing after the stop point
      }
      
      currentSection = currentSection.nextElementSibling;
    }
  }
})();
</script>
{% endif %}
```

---

## Verification Checklist

After setup, verify everything is working:

- [ ] **Panel stays sticky** through the product gallery
- [ ] **Panel stays sticky** through recommendation sections
- [ ] **Panel releases** before the footer/newsletter
- [ ] **Footer clears** the panel (no overlap)
- [ ] **Panel content** is fully visible (no cut-off)
- [ ] **Gallery scrolls** horizontally without issues
- [ ] **Resize window** - panel adapts correctly
- [ ] **Mobile view** - standard layout (no sticky panel)

---

## Troubleshooting

### Panel releases too early

**Cause:** Sections don't have `data-runway-follow` attribute.

**Fix:** Add the attribute using one of the methods above, or check that auto-detection selectors match your section structure.

**Debug:**
```javascript
// Check which sections have the attribute
document.querySelectorAll('[data-runway-follow]').forEach(el => console.log(el.id));
```

### Panel never releases

**Cause:** No `data-runway-stop` attribute, or too many sections included.

**Fix:** Add `data-runway-stop` to the section before the footer.

**Debug:**
```javascript
// Check the computed track height
console.log(getComputedStyle(document.querySelector('.product')).getPropertyValue('--runway-track-h'));
```

### Panel content gets cut off

**Cause:** The `--runway-extra-space` variable isn't being calculated correctly.

**Fix:** This should be automatic. Check browser console for JavaScript errors.

**Debug:**
```javascript
// Check the extra space value
console.log(getComputedStyle(document.querySelector('.product')).getPropertyValue('--runway-extra-space'));
```

### Footer overlaps panel

**Cause:** The product section's `::after` pseudo-element isn't extending the page.

**Fix:** Verify the CSS rule exists (it should be in `sections/product.liquid` around line 1503).

**Debug:**
```javascript
// Check if the ::after element has height
const product = document.querySelector('.product');
const afterHeight = getComputedStyle(product, '::after').height;
console.log('After height:', afterHeight);
```

---

## Best Practices

1. **Limit follow sections to 2-4** - Too many can feel disorienting
2. **Always set a stop point** - Use `data-runway-stop` before major transitions
3. **Test on real content** - Empty sections won't contribute height
4. **Consider mobile** - This only affects desktop (≥990px)
5. **Use semantic section types** - Makes auto-detection more reliable

---

## Related Documentation

- [Runway Sticky Track Technical Details](./RUNWAY_STICKY_TRACK.md)
- [Product Section Configuration](./PRODUCT_SECTION.md)
- [Theme Customization Guide](../USER_GUIDE.md)

