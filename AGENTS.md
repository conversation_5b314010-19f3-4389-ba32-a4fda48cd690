# Repository Guidelines

## Project Structure & Module Organization
- `layout/`: Theme layouts (e.g., `theme.liquid`).
- `templates/`: JSON templates per resource (e.g., `product.json`).
- `sections/`: Reusable page sections with `schema`.
- `snippets/`: Small Liquid partials used by sections/templates.
- `assets/`: CSS/JS, images, and static assets.
- `config/`: Theme settings (`settings_schema.json`, etc.).
- `locales/`: Translations (`*.json`).
- `blocks/`: Custom block definitions used by sections.
- `docs/`, `USER_GUIDE.md`, troubleshooting files: Contributor references.

## Build, Test, and Development Commands
- `shopify theme dev --store=<store>`: Run local dev server with live reload.
- `shopify theme push`: Upload local changes to the remote theme (add `--allow-live` only if required).
- `shopify theme pull`: Sync down remote changes before larger refactors.
- `shopify theme check` (or `theme-check`): Lint Liquid/JSON per `.theme-check.yml`.
- Optional: Use `swatch-demo.html` and `debug-swatch-info.html` for local feature checks.

## Coding Style & Naming Conventions
- Indentation: 2 spaces; wrap lines at ~100 chars; no trailing whitespace.
- Liquid: Prefer filters over complex logic; keep assignments near usage; comment with concise intent.
- Files: lowercase, dash-separated (e.g., `product-card.liquid`, `component-swatch.liquid`).
- Sections/Snippets: Prefix by role (`section-`, `component-`, `util-`) when helpful.
- Assets: Co-locate CSS/JS by feature (e.g., `assets/swatch.css`, `assets/swatch.js`).
- Linting: Fix all `theme-check` warnings unless explicitly silenced in config.

## Testing Guidelines
- Lint: `shopify theme check` must pass before PR review.
- Manual: Verify templates and sections across a dev store preview; attach screenshots for UI changes.
- Conventions: Test files or HTML demos use clear names (e.g., `test-swatch-card.html`).

## Commit & Pull Request Guidelines
- Commits: Imperative mood, scoped path and intent (e.g., `sections: add color-swatch block`).
- Group related changes; avoid unrelated churn (format separately).
- PRs: Include summary, screenshots/GIFs for UI, steps to validate, and linked issues.
- Reference docs updated (e.g., `README.md`, `USER_GUIDE.md`, `docs/`).

## Security & Configuration Tips
- Do not commit secrets or private store data; respect `.shopifyignore`.
- Be careful pushing to live themes (`--allow-live`); prefer a development theme.
- Keep translations in `locales/` complete and consistent.

