{%- comment -%}
  Product Grid Section

  Purpose: Display products in a grid with multiple configuration options
  - Multiple display modes: specific collection, new products, popular products, manual selection
  - Responsive grid with customizable columns
  - Promo blocks can be inserted after specified product positions
  - Performance optimized with lazy loading and responsive images
  - Accessible with proper headings and ARIA labels
{%- endcomment -%}

{% liquid
  assign heading = section.settings.heading
  assign description = section.settings.description
  assign display_mode = section.settings.display_mode
  assign collection = section.settings.collection
  assign products_count = section.settings.products_count | default: 8
  assign image_ratio = section.settings.image_ratio | default: 'adapt'
  assign show_vendor = section.settings.show_vendor | default: false
  assign color_scheme = section.settings.color_scheme | default: 'scheme-1'
  
  comment
    Calculate responsive grid columns
  endcomment
  assign cols_desktop = section.settings.columns_desktop | default: 4
  assign cols_tablet = section.settings.columns_tablet | default: 2
  assign cols_mobile = section.settings.columns_mobile | default: 1
  
  comment
    Check if we're on a collection page for pagination
  endcomment
  assign on_collection_page = false
  assign current_collection = collection
  if request.page_type == 'collection' or template.name == 'collection'
    assign on_collection_page = true
    assign current_collection = collection
  endif
  if on_collection_page == false and section.settings.collection != blank
    assign current_collection = section.settings.collection
  endif
  
  comment
    Set products source based on display mode
  endcomment
  assign products_found = 0
  
  case display_mode
    when 'collection'
      if current_collection != blank
        assign products_found = current_collection.products_count
      endif
    when 'new'
      assign products = collections['all'].products
      assign products_found = products.size
    when 'popular'
      assign products = collections['all'].products
      assign products_found = products.size
    when 'manual'
      comment
        Manual mode products are handled directly in the loop
      endcomment
      assign products_found = section.blocks.size
  endcase
%}

<div class="product-grid color-{{ color_scheme }} section-{{ section.id }}-padding">
 <div class="product-grid__inner page-width">
    
    {% comment %} Section Header {% endcomment %}
    {%- if heading != blank or description != blank -%}
      <div class="product-grid__header">
        {%- if heading != blank -%}
          <h2 class="product-grid__title h2">{{ heading }}</h2>
        {%- endif -%}
        {%- if description != blank -%}
          <div class="product-grid__description rte">
            {{ description }}
          </div>
        {%- endif -%}
      </div>
    {%- endif -%}

    {% comment %} Product Grid {% endcomment %}
    {% if products_found > 0 %}
      <div
        class="product-grid__grid"
        style="
          --cols-desktop: {{ cols_desktop }};
          --cols-tablet: {{ cols_tablet }};
          --cols-mobile: {{ cols_mobile }};
        "
      >
        {%- assign product_index = 0 -%}

        {%- if display_mode == 'manual' -%}
          {%- for b in section.blocks -%}
            {%- if b.type == 'product' and b.settings.product != blank -%}
              {%- assign product_index = product_index | plus: 1 -%}

              {%- for block in section.blocks -%}
                {%- if block.type == 'promo' and block.settings.insert_after == product_index -%}
                  {%- assign has_promo = false -%}
                  {%- if block.settings.promo_type == 'image' and block.settings.image != blank -%}
                    {%- assign has_promo = true -%}
                  {%- elsif block.settings.promo_type == 'richtext' and block.settings.html != blank -%}
                    {%- assign has_promo = true -%}
                  {%- endif -%}

                  {%- if has_promo -%}
                    <div class="product-grid__promo-block" {{ block.shopify_attributes }}>
                      {%- if block.settings.promo_type == 'image' -%}
                        <div class="product-grid__promo-image{% if section.settings.enable_image_zoom %} zoom-on-scroll{% endif %}" {% if section.settings.enable_image_zoom %}data-zoom-on-scroll{% endif %}{% if section.settings.enable_image_reveal %} data-reveal-on-scroll{% endif %}>
                          {{ block.settings.image | image_url: width: 600 | image_tag: loading: 'lazy', class: 'product-grid__promo-img', widths: '300, 400, 500, 600', alt: block.settings.image.alt }}
                        </div>
                      {%- else -%}
                        <div class="product-grid__promo-content rte">
                          {{ block.settings.html }}
                        </div>
                      {%- endif -%}
                    </div>
                  {%- endif -%}
                {%- endif -%}
              {%- endfor -%}

              {%- render 'card-product', card_product: b.settings.product, image_ratio: image_ratio, show_vendor: show_vendor, show_secondary_image: section.settings.show_secondary_image, show_quick_add: section.settings.show_quick_add, enable_favorites: section.settings.enable_favorites, show_new_badges: section.settings.show_new_badges, show_swatches: section.settings.show_swatches_on_cards, enable_hover_preview: section.settings.enable_card_hover_preview, max_swatches: section.settings.max_swatches_per_product, section_id: section.id, enable_zoom_on_cards: section.settings.enable_image_zoom, enable_reveal_on_cards: section.settings.enable_image_reveal -%}

              {%- if product_index >= products_count -%}{% break %}{% endif -%}
            {%- endif -%}
          {%- endfor -%}

        {%- else -%}
          {%- comment -%} Handle collection products {%- endcomment -%}
          {%- if display_mode == 'collection' and current_collection != blank -%}
              {%- for product in current_collection.products limit: products_count -%}
                {%- assign product_index = product_index | plus: 1 -%}

                {%- comment -%} Check for promo blocks to insert {%- endcomment -%}
                {%- for block in section.blocks -%}
                  {%- if block.type == 'promo' and block.settings.insert_after == product_index -%}
                    {%- assign has_promo = false -%}
                    {%- if block.settings.promo_type == 'image' and block.settings.image != blank -%}
                      {%- assign has_promo = true -%}
                    {%- elsif block.settings.promo_type == 'richtext' and block.settings.html != blank -%}
                      {%- assign has_promo = true -%}
                    {%- endif -%}

                    {%- if has_promo -%}
                      <div class="product-grid__promo-block" {{ block.shopify_attributes }}>
                        {%- if block.settings.promo_type == 'image' -%}
                          <div class="product-grid__promo-image{% if section.settings.enable_image_zoom %} zoom-on-scroll{% endif %}" {% if section.settings.enable_image_zoom %}data-zoom-on-scroll{% endif %}{% if section.settings.enable_image_reveal %} data-reveal-on-scroll{% endif %}>
                            {{ block.settings.image | image_url: width: 600 | image_tag: loading: 'lazy', class: 'product-grid__promo-img', widths: '300, 400, 500, 600', alt: block.settings.image.alt }}
                          </div>
                        {%- else -%}
                          <div class="product-grid__promo-content rte">
                            {{ block.settings.html }}
                          </div>
                        {%- endif -%}
                      </div>
                    {%- endif -%}
                  {%- endif -%}
                {%- endfor -%}

                {%- render 'card-product', card_product: product, image_ratio: image_ratio, show_vendor: show_vendor, show_secondary_image: section.settings.show_secondary_image, show_quick_add: section.settings.show_quick_add, enable_favorites: section.settings.enable_favorites, show_new_badges: section.settings.show_new_badges, show_swatches: section.settings.show_swatches_on_cards, enable_hover_preview: section.settings.enable_card_hover_preview, max_swatches: section.settings.max_swatches_per_product, section_id: section.id, enable_zoom_on_cards: section.settings.enable_image_zoom, enable_reveal_on_cards: section.settings.enable_image_reveal -%}
              {%- endfor -%}
          {%- else -%}
            {%- assign threshold = 'now' | date: '%s' | minus: 2592000 -%}
            {%- for product in products -%}
              {%- assign qualifies = true -%}
              {%- if display_mode == 'new' -%}
                {%- assign created = product.created_at | date: '%s' -%}
                {%- if created < threshold -%}{% assign qualifies = false %}{%- endif -%}
              {%- endif -%}

              {%- if qualifies -%}
                {%- assign product_index = product_index | plus: 1 -%}

                {%- for block in section.blocks -%}
                  {%- if block.type == 'promo' and block.settings.insert_after == product_index -%}
                    {%- assign has_promo = false -%}
                    {%- if block.settings.promo_type == 'image' and block.settings.image != blank -%}
                      {%- assign has_promo = true -%}
                    {%- elsif block.settings.promo_type == 'richtext' and block.settings.html != blank -%}
                      {%- assign has_promo = true -%}
                    {%- endif -%}

                    {%- if has_promo -%}
                      <div class="product-grid__promo-block" {{ block.shopify_attributes }}>
                        {%- if block.settings.promo_type == 'image' -%}
                          <div class="product-grid__promo-image{% if section.settings.enable_image_zoom %} zoom-on-scroll{% endif %}" {% if section.settings.enable_image_zoom %}data-zoom-on-scroll{% endif %}{% if section.settings.enable_image_reveal %} data-reveal-on-scroll{% endif %}>
                            {{ block.settings.image | image_url: width: 600 | image_tag: loading: 'lazy', class: 'product-grid__promo-img', widths: '300, 400, 500, 600', alt: block.settings.image.alt }}
                          </div>
                        {%- else -%}
                          <div class="product-grid__promo-content rte">
                            {{ block.settings.html }}
                          </div>
                        {%- endif -%}
                      </div>
                    {%- endif -%}
                  {%- endif -%}
                {%- endfor -%}

                {%- render 'card-product', card_product: product, image_ratio: image_ratio, show_vendor: show_vendor, show_secondary_image: section.settings.show_secondary_image, show_quick_add: section.settings.show_quick_add, enable_favorites: section.settings.enable_favorites, show_new_badges: section.settings.show_new_badges, show_swatches: section.settings.show_swatches_on_cards, enable_hover_preview: section.settings.enable_card_hover_preview, max_swatches: section.settings.max_swatches_per_product, section_id: section.id, enable_zoom_on_cards: section.settings.enable_image_zoom, enable_reveal_on_cards: section.settings.enable_image_reveal -%}

                {%- if product_index >= products_count -%}{% break %}{% endif -%}
              {%- endif -%}
            {%- endfor -%}
          {%- endif -%}
        {%- endif -%}
      </div>
    {% else %}
      <p class="color-foreground" style="opacity:.7">No products found. Check section settings.</p>
    {% endif %}
  </div>
</div>

{% style %}
  .product-grid {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .product-grid__inner {
    max-width: var(--page-width);
    margin: 0 auto;
    padding: 0 1.5rem;
  }

  .product-grid__header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .product-grid__title {
    font-size: clamp(2.4rem, 4vw, 4.8rem);
    margin-bottom: var(--heading-gap);
  }

  .product-grid__description {
    max-width: 60rem;
    margin: 0 auto;
    font-size: 1.6rem;
    line-height: 1.6;
    color: rgba(var(--color-foreground), 0.8);
  }

  .product-grid__grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(var(--cols-mobile), 1fr);
  }

  @media screen and (min-width: 750px) {
    .product-grid__grid {
      grid-template-columns: repeat(var(--cols-tablet), 1fr);
    }
  }

  @media screen and (min-width: 990px) {
    .product-grid__grid {
      grid-template-columns: repeat(var(--cols-desktop), 1fr);
      gap: 2rem;
    }
  }

  .product-grid__promo-block {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 2rem;
    background: rgba(var(--color-foreground), 0.05);
    border-radius: 0.4rem;
  }

  .product-grid__promo-image {
    margin-bottom: 1.5rem;
  }

  .product-grid__promo-img {
    max-width: 100%;
    height: auto;
  }

  .product-grid__promo-content {
    font-size: 1.6rem;
    line-height: 1.5;
  }
  
  .product-grid__promo-block:empty {
    display: none;
  }
  
  #shopify-section-{{ section.id }} .product-grid__inner.page-width {
    max-width: none;
    padding-left: max(12px, 2vw);
    padding-right: max(12px, 2vw);
  }
  
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} .product-grid__grid {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
{% endstyle %}

{% schema %}
{
  "name": "Product Grid",
  "settings": [
    { "type": "checkbox", "id": "enable_image_zoom", "label": "Enable image zoom-on-scroll", "default": true },
    { "type": "checkbox", "id": "enable_image_reveal", "label": "Enable image reveal-on-scroll", "default": true },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "select",
      "id": "display_mode",
      "label": "Display mode",
      "options": [
        { "value": "collection", "label": "Specific collection" },
        { "value": "new", "label": "New products" },
        { "value": "popular", "label": "Popular products" },
        { "value": "manual", "label": "Manual selection" }
      ],
      "default": "collection"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "range",
      "id": "products_count",
      "label": "Number of products to show",
      "min": 2,
      "max": 24,
      "step": 1,
      "default": 8
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "label": "Columns on desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "columns_tablet",
      "label": "Columns on tablet",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 2
    },
    {
      "type": "range",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 1
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "Image ratio",
      "options": [
        { "value": "adapt", "label": "Adapt to image" },
        { "value": "portrait", "label": "Portrait" },
        { "value": "square", "label": "Square" },
        { "value": "landscape", "label": "Landscape" }
      ],
      "default": "adapt"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "Color scheme",
      "options": [
        { "value": "scheme-1", "label": "Scheme 1" },
        { "value": "scheme-2", "label": "Scheme 2" }
      ],
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Product card settings"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "label": "Show secondary image on hover",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_quick_add",
      "label": "Show quick add button",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_favorites",
      "label": "Enable favorites",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_new_badges",
      "label": "Show NEW badges",
      "default": true
    },
    {
      "type": "header",
      "content": "Color swatches on cards"
    },
    {
      "type": "checkbox",
      "id": "show_swatches_on_cards",
      "label": "Show color swatches on product cards",
      "default": true,
      "info": "Display color options as swatches on collection cards"
    },
    {
      "type": "checkbox",
      "id": "enable_card_hover_preview",
      "label": "Enable card hover preview",
      "default": true,
      "info": "Hover over color swatches to preview variant images"
    },
    {
      "type": "range",
      "id": "max_swatches_per_product",
      "min": 3,
      "max": 8,
      "step": 1,
      "default": 5,
      "label": "Maximum swatches per product",
      "info": "Limit number of color swatches shown on each card"
    }
  ],
  "blocks": [
    {
      "type": "promo",
      "name": "Promo block",
      "settings": [
        {
          "type": "range",
          "id": "insert_after",
          "label": "Insert after product",
          "min": 1,
          "max": 24,
          "step": 1,
          "default": 4
        },
        {
          "type": "select",
          "id": "promo_type",
          "label": "Type",
          "options": [
            { "value": "image", "label": "Image" },
            { "value": "richtext", "label": "Rich text" }
          ],
          "default": "image"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "richtext",
          "id": "html",
          "label": "Content"
        }
      ]
    },
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Product Grid",
      "settings": {
        "display_mode": "collection"
      },
      "blocks": [
        { "type": "promo" }
      ]
    }
  ]
}
{% endschema %}


