-Blueprint — Production-ready Shopify theme
=========================================

Purpose
-------

A compact, actionable blueprint that maps your feature list to pragmatic implementation choices, phases, and deliverables for a Theme-Store-ready Shopify theme (Liquid/OS2.0) with optional companion app components.

Quick decisions
---------------

- Distribution: Theme Store requires a Liquid theme (Online Store 2.0). Headless frontends must be distributed separately.
- Strategy: Theme-first (Liquid + JSON templates + sections), optional companion app for server-side logic (Functions, Checkout UI Extensions), and a small API to power recommendations or heavy merch rules.

Architecture
------------

- Theme: Liquid templates, JSON templates, `sections/`, `snippets/`, `locales/`, assets pipeline, client-side TypeScript for UX.
- Optional companion app: Shopify Functions, GraphQL Admin/Storefront APIs, Checkout UI Extensions where needed.
- Data: Use `Metaobjects` and `Metafields` for product attributes (ingredients, nutritional info, tabs). Keep all merchant-facing content translatable via `locales/*.json` and schema keys.

Dev setup & tooling
-------------------

- Core tools: Shopify CLI, Node (TypeScript), ESLint, Prettier, `Stylelint`, Theme Check.
- Tests and checks: Theme Check, Lighthouse, pa11y/axe, Playwright/Cypress for smoke tests.
- Performance guardrails: lazy-loading images, responsive `srcset`, CSS containment, minimal main-thread JS, defer non-critical scripts.

Phase plan (prioritized)
------------------------

Phase 0 — Repo & infra (1–2 days)

- Scaffold file map, `package.json`, `tsconfig.json`.
- CI: GitHub Actions running Theme Check, Lighthouse CI, pa11y smoke.
- Local verification: `shopify theme dev`, `theme-check` run.

Phase 1 — Core theme MVP (1–2 weeks)

- Layout, `header` (mega menu support), `footer`, tokens & utilities.
- Product page: gallery, variant picker, swatches, stock counter, quick-buy snippet.
- Cart: slide-out cart, `/cart/add.js` + `/cart/change.js`, cart notes, shipping meter.
- Collection & index pages: basic filters (tag-based), pagination / Load more.
- Locales: `locales/en.default.json` populated.

Phase 2 — UX & conversion (2–3 weeks)

- Quick view modal, countdown timer, promo banners, age gate, FAQ, before/after slider.
- Predictive search (instant results + keyboard nav).
- Product tabs driven by `Metaobjects`.
- Progressive enhancements: infinite scroll, lazy-loaded widgets.

Phase 3 — Merch & personalization (2–4 weeks)

- Companion app: algorithmic recommendations, bundles, discount Functions, Checkout UI Extensions (if required).
- Theme fallback: related-products algorithm using tags/collections.

Phase 4 — Polish & Marketplace prep (1 week)

- WCAG AA accessibility, multi-language (EN/FR/IT/DE/ES), JSON-LD SEO, demo store, screenshots, docs, support plan.
- Submit to Theme Store when Theme Check and reviewer checklist are green.

Feature map & implementation notes
----------------------------------

(Each item lists where it should live: Theme / App / Hybrid)

Cart & checkout

- Slide-out cart: Theme (Liquid + TS) calling Cart AJAX API; emit analytics events.
- Sticky cart / Quick buy: Theme snippets targeting slide-out cart.
- Cart notes: Theme form bound to `/cart/update.js`.
- In-store pickup: Theme UI surface; rely on Shopify pickup APIs where available.
- Checkout customizations: Theme handles branding; Checkout UI Extensions (app) for in-checkout widgets.
- Stock counter: Theme uses `inventory_quantity` and API guards.

Marketing & conversion

- Age verifier: Theme section with cookie/localStorage persistence.
- Blogs: Theme templates with related articles and reading time.
- Countdown timer: Theme section (deadline + `time zone` input).
- Cross-sell / recommendations: Hybrid — theme fallback (tags), app for advanced algorithms.
- Contact form: Theme section + honeypot; app if you need server-side delivery.
- Language switcher: Theme + `locales/*.json`.
- Promo banners/popups: Theme sections with scheduling; app for advanced targeting.
- Quick view: Theme modal fetching product JSON.

Merchandising

- Animation: CSS-first, `prefers-reduced-motion` aware.
- Before/after slider: Accessible, no-dependencies JS in theme.
- Color swatches: Theme snippet mapping option values to colors/images; URL-sync for variant.
- Galleries & zoom: Theme with native `<dialog>` lightbox and lazy loading.
- Tabs & `metaobject` content: Theme renders `Metaobjects`; content authored via `Metaobjects`.
- Product videos: Native `<video>` or embed lightbox.

Product discovery & nav

- Sticky header / back-to-top: Theme, IntersectionObserver.
- Breadcrumbs & JSON-LD: Theme renders structured data.
- Filters & Search: Integrate Search & Discovery app blocks for filtering; theme provides sort and tag chips.
- Predictive search: Theme TS talking to the predictive endpoint; keyboard controls.
- Infinite scroll: Progressive enhancement; provide Load more by default.
- Mega menu: Header section reading navigation menus + promo blocks.

Internationalization

- Provide `locales/en.default.json`, `fr.json`, `it.json`, `de.json`, `es.json`.
- Translate schema labels and use the `t` filter for UI strings.
- Prices via `money` filters; prepare for RTL if needed.

Accessibility

- WCAG 2.2 AA: keyboard focus, ARIA for accordions/menus, escape-to-close for modals, color contrast tokens.

Performance & SEO

- Aim Sub-2s LCP: lightweight hero, critical CSS `inlined`, responsive images, defer JS for non-essential features.
- JSON-LD product/article/breadcrumbs.
- Cache / debounce cart requests; guard double-add clicks.

Settings & extensibility

- Rich `settings_schema.json` toggles and `presets`.
- Portable, nestable sections; app blocks for third-party integrations.
- Event bus in TS for analytics events: `view_item`, `add_to_cart`, `begin_checkout`.

Minimal file map (starter)

```text
 /sections
  header-mega-menu.liquid
  promo-banner.liquid
  age-gate.liquid
  faq.liquid
  slideshow.liquid
  product-gallery.liquid
  before-after-slider.liquid
  recommended-products.liquid
/snippets
  quick-buy.liquid
  product-swatch.liquid
  price-badge.liquid
  stock-counter.liquid
  breadcrumbs.liquid
/layout
  theme.liquid
/templates
  product.json
  collection.json
  blog.json
  article.json
  page.faq.json
/assets
  theme.ts    // cart, quick view, predictive search
  styles.css  // tokens, utilities
/locales
  en.default.json fr.json it.json de.json es.json
/config
  settings_schema.json
```

Theme vs App — short mapping
---------------------------

- Theme responsibilities: all UI rendering, schema-driven customization, `Metaobject` rendering, client-side UX, and graceful fallbacks.
- App responsibilities: heavy server-side logic, secure API keys, Functions for discounts, Checkout UI Extensions, recommendations algorithms, post-purchase flows.
- Hybrid pattern: app computes data and stores results in `metafields`/`metaobjects` for the theme to render; theme falls back to simple related-products logic.

Contracts (3 examples)
----------------------

1. Cart slide-out (theme)

- Inputs: add event with `variantId` & `qty`; `/cart.json` to sync.
- Outputs: item list, subtotal, shipping meter; analytics events.
- Error modes: network failure, out-of-stock -> friendly UI.
- Success: UI reflects server cart within ~1s.

1. Product gallery (theme)

- Inputs: product JSON (images/media/variants); settings toggles.
- Outputs: accessible gallery, keyboard, lightbox, add-to-cart hook.
- Error modes: missing media -> placeholder.
- Success: first image LCP-friendly; lightbox lazy-loads.

1. Recommendations (hybrid)

- Inputs: product/collection context; optional `/recommendations?pid=` endpoint.
- Outputs: product preview list (id, handle, image, price).
- Fallback: theme-run related-products via tags.
- Error modes: app timeout -> fallback.

QA checklist (ship with confidence)
----------------------------------

- Variant switch preserves URL and history.
- Add-to-cart works across PDP, collections, quick view, slide-out.
- Filters paginate and are linkable; SSR fallback for SEO.
- Translations render accurately; metaobjects respected.
- Age gate respects keyboard/ARIA and remembers preference.
- Accessibility: pa11y/axe high-severity issues fixed.

Edge cases & defensive rules
---------------------------

- Always provide a visual fallback for missing content.
- Fail open on recommendations (use fallback) but fail safe for checkout.
- Limit client-side rendering for very large collections; use pagination and server-side search.
- Avoid coupling theme to private keys or external-only data.

Dev tasks I can implement for you now (pick one)
-----------------------------------------------

1. Create starter repo stubs: sections/snippets/layout/assets + minimal TS cart module.
2. Add a GitHub Actions workflow that runs Theme Check + Lighthouse + pa11y on pushes to `main`.
3. Generate `config/settings_schema.json` skeleton and a populated `locales/en.default.json` with common keys.

Next step
---------

Tell me which of the three dev tasks above you want me to implement first and I will add the files and run the quick checks.
