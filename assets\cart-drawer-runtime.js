/* Cart Drawer Runtime Bootstrap
 * Progressive enhancement, resilient to late section load
 */
(function(){
  if (window.__CART_DRAWER_RUNTIME__) return; // idempotent
  window.__CART_DRAWER_RUNTIME__ = true;

  const LOG_PREFIX = '[cart-drawer]';
  const STORAGE_KEY = 'cartDrawer:isOpen'; // persistence key (currently write-only; restore disabled pending bug fix)
  let lastActiveTrigger = null;
  let focusTrapActive = false;

  const getDrawer = () => document.querySelector('[data-cart-drawer]');
  // Limit inert application to typical structural containers to avoid edge cases with injected modals / portals
  const nonDrawerRoots = () => {
    const selectors = ['header', 'main', 'footer', '#shopify-section-header'];
    const els = new Set();
    selectors.forEach(sel => document.querySelectorAll(sel).forEach(el => {
      if (!el.closest('[data-cart-drawer]')) els.add(el);
    }));
    return Array.from(els);
  };
  const getFocusable = (root) => root ? Array.from(root.querySelectorAll('a[href],button:not([disabled]),textarea,input:not([disabled]),select:not([disabled]),[tabindex]:not([tabindex="-1"])')).filter(el => !el.hasAttribute('inert') && !el.closest('[aria-hidden="true"]')) : [];

  function setInert(state) {
    try {
      nonDrawerRoots().forEach(el => {
        if (state) el.setAttribute('inert', ''); else el.removeAttribute('inert');
      });
    } catch(err) {
      console.warn(LOG_PREFIX, 'inert application failed', err);
    }
  }

  // Live region setup (polite)
  let liveRegion = document.getElementById('cart-drawer-runtime-live');
  if (!liveRegion) {
    liveRegion = document.createElement('div');
    liveRegion.id = 'cart-drawer-runtime-live';
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.style.cssText = 'position:absolute;left:-9999px;top:auto;width:1px;height:1px;overflow:hidden;';
    document.body.appendChild(liveRegion);
  }
  function announce(msg) {
    if (!msg) return;
    liveRegion.textContent = '';
    // force reflow for screen readers to pick change
    void liveRegion.offsetHeight;
    liveRegion.textContent = msg;
  }
  window.cartDrawerAnnounce = announce;

  function syncAria(isOpen) {
    document.querySelectorAll('[data-open-cart]').forEach(btn => {
      btn.setAttribute('aria-expanded', isOpen ? 'true' : 'false');
      if (isOpen) btn.setAttribute('data-cart-trigger-active', 'true'); else btn.removeAttribute('data-cart-trigger-active');
    });
    const drawer = getDrawer();
    if (drawer) {
      drawer.setAttribute('aria-hidden', isOpen ? 'false' : 'true');
    }
  }

  function activateFocusTrap(drawer) {
    if (!drawer || focusTrapActive) return;
    focusTrapActive = true;
    const focusables = getFocusable(drawer);
    if (focusables.length) {
      // focus first meaningful focusable; prefer close button
      const closeBtn = drawer.querySelector('[data-cart-drawer-close]');
      (closeBtn || focusables[0]).focus({ preventScroll: true });
    } else {
      drawer.setAttribute('tabindex', '-1');
      drawer.focus({ preventScroll: true });
    }
  }

  function deactivateFocusTrap() {
    focusTrapActive = false;
  }

  function onKeydown(e) {
    if (!focusTrapActive) return;
    if (e.key === 'Tab') {
      const drawer = getDrawer();
      if (!drawer) return;
      const focusables = getFocusable(drawer);
      if (!focusables.length) { e.preventDefault(); return; }
      const first = focusables[0];
      const last = focusables[focusables.length - 1];
      if (e.shiftKey && document.activeElement === first) { e.preventDefault(); last.focus(); }
      else if (!e.shiftKey && document.activeElement === last) { e.preventDefault(); first.focus(); }
    }
    if (e.key === 'Escape') closeCartDrawer();
  }

  function openCartDrawer(source = 'manual') {
    const drawer = getDrawer();
    if (!drawer) { console.warn(LOG_PREFIX, 'openCartDrawer called but drawer not found'); return; }
    if (drawer.classList.contains('is-open')) { return; }
    if (!lastActiveTrigger) lastActiveTrigger = document.activeElement instanceof HTMLElement ? document.activeElement : null;
    drawer.classList.add('is-open');
    drawer.classList.toggle('reduce-motion', window.matchMedia('(prefers-reduced-motion: reduce)').matches);
    document.body.style.overflow = 'hidden';
    syncAria(true);
    sessionStorage.setItem(STORAGE_KEY, '1');
    setInert(true);
    activateFocusTrap(drawer);
    document.addEventListener('keydown', onKeydown);
    console.info(LOG_PREFIX, 'opened (source=' + source + ')');
  }
  window.openCartDrawer = openCartDrawer;

  function closeCartDrawer(source = 'manual') {
    const drawer = getDrawer();
    if (!drawer || !drawer.classList.contains('is-open')) return;
    drawer.classList.remove('is-open');
    document.body.style.overflow = '';
    syncAria(false);
    sessionStorage.removeItem(STORAGE_KEY);
    setInert(false);
    deactivateFocusTrap();
    document.removeEventListener('keydown', onKeydown);
    const trigger = lastActiveTrigger || document.querySelector('[data-open-cart]');
    trigger && trigger.focus({ preventScroll: true });
    console.info(LOG_PREFIX, 'closed (source=' + source + ')');
  }
  window.closeCartDrawer = closeCartDrawer;

  // Delegated triggers
  document.addEventListener('click', (e) => {
    const openBtn = e.target.closest('[data-open-cart]');
    if (openBtn) { e.preventDefault(); openCartDrawer(); return; }
    if (e.target.closest('[data-cart-drawer-close]')) { e.preventDefault(); closeCartDrawer(); return; }
    if (e.target.matches && e.target.matches('.cart-drawer__overlay')) { closeCartDrawer(); }
  });

  // Observe for late mounted drawer; disconnect once bound
  const observer = new MutationObserver(() => {
    const drawer = getDrawer();
    if (drawer && !drawer.dataset.runtimeBound) {
      drawer.dataset.runtimeBound = '1';
      // Enhance ARIA
      drawer.setAttribute('role', 'dialog');
      drawer.setAttribute('aria-modal', 'true');
      drawer.setAttribute('aria-hidden', 'true');
      const header = drawer.querySelector('.cart-drawer__header h2');
      if (header && !header.id) header.id = 'cart-drawer-title';
      if (header) drawer.setAttribute('aria-labelledby', header.id);
      console.info(LOG_PREFIX, 'drawer detected & runtime bound');
      // (Temporarily disabled) Automatic restore removed to prevent lock issues.
      if (sessionStorage.getItem(STORAGE_KEY) === '1') {
        console.info(LOG_PREFIX, 'previous open state detected (not auto-opening)');
        sessionStorage.removeItem(STORAGE_KEY); // clear stale state so it does not persist forever
      }
      observer.disconnect();
    }
  });
  observer.observe(document.documentElement, { childList: true, subtree: true });

  // Auto-open on custom events from add-to-cart flows
  let cartUpdateOpenTimer = null;
  // Helper to refresh the drawer section HTML
  async function refreshCartDrawerSection() {
    try {
      const drawer = getDrawer();
      if (!drawer) return;
      const json = await fetch('/?sections=cart_drawer', { headers: { 'Accept': 'application/json' } }).then(r => r.json());
      const html = json && (json['cart_drawer'] || json['cart-drawer']);
      if (!html) return;
      const tmp = document.createElement('div');
      tmp.innerHTML = html;
      const next = tmp.querySelector('[data-cart-drawer]');
      if (!next) return;
      drawer.innerHTML = next.innerHTML; // keep outer node & listeners
      // Rebind cart operations on the updated DOM
      setupCartOperations();
    } catch (err) {
      console.error(LOG_PREFIX, 'refresh (event) failed', err);
    }
  }

  document.addEventListener('cart:updated', () => {
    // Debounce rapid consecutive updates (e.g., multiple adds)
    if (cartUpdateOpenTimer) clearTimeout(cartUpdateOpenTimer);
    cartUpdateOpenTimer = setTimeout(async () => {
      await refreshCartDrawerSection();
      const drawer = getDrawer();
      if (drawer && !drawer.classList.contains('is-open')) openCartDrawer('cart:updated');
    }, 50);
  });

  // Emergency: Shift+Escape forces close & clears persistence
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && e.shiftKey) {
      sessionStorage.removeItem(STORAGE_KEY);
      closeCartDrawer('shift+escape');
      console.info(LOG_PREFIX, 'emergency close (Shift+Escape)');
    }
  });

  // Expose manual reset for debugging
  window.resetCartDrawerState = function() {
    sessionStorage.removeItem(STORAGE_KEY);
    setInert(false);
    closeCartDrawer('reset');
    console.info(LOG_PREFIX, 'state reset invoked');
  };

  // Respect user motion preferences dynamically
  window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
    const drawer = getDrawer();
    if (!drawer) return;
    drawer.classList.toggle('reduce-motion', e.matches);
  });

  // Cart operations functionality
  function setupCartOperations() {
    const drawer = getDrawer();
    if (!drawer || drawer.dataset.cartBound === '1') return;
    drawer.dataset.cartBound = '1';
    drawer.removeAttribute('data-busy');

    // Helpers
    const setBusy = (on) => drawer.toggleAttribute('data-busy', !!on);
    const getItemKey = (node) => {
      const el = node.closest('[data-line]');
      return el ? el.dataset.lineKey : null;
    };

    // Format price in cents to currency string
    const formatPrice = (cents) => {
      try {
        // Try to get currency from existing price elements
        const existingPrice = drawer.querySelector('[data-line-subtotal]');
        if (existingPrice) {
          const text = existingPrice.textContent;
          const currencySymbol = text.replace(/[\d.,\s]/g, '');
          const amount = (cents / 100).toFixed(2);
          return currencySymbol + amount;
        }
        // Fallback to basic formatting
        return '$' + (cents / 100).toFixed(2);
      } catch (err) {
        return '$' + (cents / 100).toFixed(2);
      }
    };

    // Update cart total by summing all line subtotals
    const updateCartTotal = () => {
      const lineEls = drawer.querySelectorAll('[data-line]:not([style*="opacity: 0.5"])');
      let total = 0;

      lineEls.forEach(lineEl => {
        const unitPrice = parseInt(lineEl.dataset.unitPrice || '0', 10);
        const qtyInput = lineEl.querySelector('[data-line-qty]');
        const quantity = parseInt(qtyInput?.value || '0', 10);
        total += unitPrice * quantity;
      });

      const totalEl = drawer.querySelector('[data-cart-total]');
      if (totalEl) {
        totalEl.textContent = formatPrice(total);
      }
    };

    async function refreshDrawer() { await refreshCartDrawerSection(); }

    async function changeItem(key, quantity) {
      setBusy(true);

      // Update UI immediately for better UX
      const lineEl = drawer.querySelector(`[data-line-key="${key}"]`);
      if (lineEl) {
        const qtyInput = lineEl.querySelector('[data-line-qty]');
        const subtotalEl = lineEl.querySelector('[data-line-subtotal]');
        const unitPrice = parseInt(lineEl.dataset.unitPrice || '0', 10);

        if (qtyInput) {
          qtyInput.value = quantity;
        }

        // Update line subtotal immediately
        if (subtotalEl && unitPrice) {
          const newSubtotal = unitPrice * quantity;
          subtotalEl.textContent = formatPrice(newSubtotal);
        }

        // If quantity is 0, fade out the item
        if (quantity === 0) {
          lineEl.style.opacity = '0.5';
        }
      }

      // Update cart total immediately
      updateCartTotal();

      try {
        const res = await fetch('/cart/change.js', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({ id: key, quantity })
        });
        if (!res.ok) {
          console.warn(LOG_PREFIX, 'change.js status', res.status);
          // Revert UI changes on error
          if (lineEl) {
            lineEl.style.opacity = '';
            const qtyInput = lineEl.querySelector('[data-line-qty]');
            if (qtyInput) {
              // Revert to previous value - we'll let the refresh handle this
            }
          }
          return;
        }

        const json = await res.json().catch(() => null);
        if (json && typeof json.item_count !== 'undefined') {
          const badge = document.querySelector('[data-cart-count],[data-cart-badge]');
          if (badge) {
            if (json.item_count > 0) {
              badge.textContent = json.item_count;
              badge.style.display = 'flex';
            } else {
              badge.textContent = '';
              badge.style.display = 'none';
            }
          }
        }
      } catch (err) {
        console.error(LOG_PREFIX, 'network error', err);
        // Revert UI changes on error
        if (lineEl) {
          lineEl.style.opacity = '';
        }
      } finally {
        // Small delay to show immediate UI feedback before refresh
        setTimeout(async () => {
          await refreshDrawer();
          setBusy(false);
          drawer.removeAttribute('data-busy');
        }, 100);
      }
    }

    // Delegated events for cart operations
    drawer.addEventListener('click', (e) => {
      const minus = e.target.closest('[data-qty-minus]');
      const plus = e.target.closest('[data-qty-plus]');
      const remove = e.target.closest('[data-remove]');
      if (!minus && !plus && !remove) return;
      e.preventDefault();
      const lineEl = e.target.closest('[data-line]');
      if (!lineEl) return;
      const key = getItemKey(lineEl);
      if (!key) return;
      const qtyInput = lineEl.querySelector('[data-line-qty]');
      const current = parseInt(qtyInput?.value || '0', 10);
      if (minus) return changeItem(key, Math.max(0, current - 1));
      if (plus) return changeItem(key, current + 1);
      if (remove) return changeItem(key, 0);
    });

    drawer.addEventListener('change', (e) => {
      const input = e.target.closest('[data-line-qty]');
      if (!input) return;
      const key = getItemKey(input);
      if (!key) return;
      const next = Math.max(0, parseInt(input.value || '0', 10));
      changeItem(key, next);
    });

    drawer.addEventListener('keydown', (e) => {
      if (e.key !== 'Enter') return;
      const input = e.target.closest('[data-line-qty]');
      if (!input) return;
      e.preventDefault();
      input.blur();
    });
  }

  // Setup cart operations when drawer is detected
  const cartObserver = new MutationObserver(() => {
    const drawer = getDrawer();
    if (drawer && !drawer.dataset.cartBound) {
      setupCartOperations();
    }
  });
  cartObserver.observe(document.documentElement, { childList: true, subtree: true });

  // Initial setup if drawer already exists
  if (getDrawer()) {
    setupCartOperations();
  }

  // Initial diagnostics
  if (!getDrawer()) console.info(LOG_PREFIX, 'waiting for drawer element...');
})();
