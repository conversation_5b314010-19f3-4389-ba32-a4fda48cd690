/* Callout Banner Section Styles
   - High-impact banner with configurable layout and styling
   - Responsive background images with overlay support
   - Uses theme color schemes and CSS variables
   - Full-bleed background extends to viewport edges
*/

.callout-banner {
  position: relative;
  overflow: hidden;
  display: grid;
  place-items: center;
  background: var(--color-background);
  color: var(--color-foreground);
  margin: 0;
}

/* White text when there's a background image or overlay for better contrast */
.callout-banner--has-overlay {
  color: #ffffff;
}

/* Height modifiers */
.callout-banner--height-small {
  min-height: 280px;
}

.callout-banner--height-medium {
  min-height: 420px;
}

/* Background image and overlay */
.callout-banner__image,
.callout-banner__overlay {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}

/* Ensure zoom wrapper fills the banner so overflow/transform work */
.callout-banner__media {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}

.callout-banner__image {
  object-fit: cover;
  object-position: center;
}

/* Image position modifiers */
.callout-banner--image-top .callout-banner__image {
  object-position: center top;
}

.callout-banner--image-center .callout-banner__image {
  object-position: center;
}

.callout-banner--image-bottom .callout-banner__image {
  object-position: center bottom;
}

.callout-banner__overlay {
  background: rgb(0 0 0 / var(--overlay-opacity));
  pointer-events: none;
}

/* Content wrapper */
.callout-banner__inner {
  width: 100%;
  padding: 1.5rem;
}

.callout-banner__content {
  position: relative;
  z-index: 1;
  display: grid;
  gap: 1rem;
}

/* Content alignment modifiers */
.callout-banner--align-left .callout-banner__content {
  justify-items: start;
  text-align: left;
}

.callout-banner--align-center .callout-banner__content {
  justify-items: center;
  text-align: center;
}

.callout-banner--align-right .callout-banner__content {
  justify-items: end;
  text-align: right;
}

/* Typography */
.callout-banner__heading {
  margin: 0;
  font-size: 3rem;
  line-height: 1.1;
  font-weight: bold;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
}

.callout-banner__subheading {
  font-size: 1.125rem;
  line-height: 1.4;
  opacity: 0.9;
  word-wrap: break-word;
}

/* White text over images/overlays for readability */
.callout-banner--has-overlay .callout-banner__heading,
.callout-banner--has-overlay .callout-banner__subheading {
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.callout-banner__button {
  margin-top: 0.75rem;
  justify-self: start;
}

/* Base button styles */
.callout-banner .button {
  display: inline-block;
  padding: 1rem 1.5rem;
  background: var(--color-button, #121212);
  color: var(--color-button-label, #ffffff);
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: opacity 0.2s ease;
  min-height: 48px;
  box-sizing: border-box;
}

.callout-banner .button:hover {
  opacity: 0.9;
}

.callout-banner .button:focus {
  outline: 2px solid var(--color-accent-1, #121212);
  outline-offset: 2px;
}

/* Ensure button has good contrast over images */
.callout-banner--has-overlay .callout-banner__button {
  background: #ffffff;
  color: #000000;
  border: none;
}

.callout-banner--has-overlay .callout-banner__button:hover {
  background: rgba(255, 255, 255, 0.9);
  color: #000000;
}

.callout-banner--align-center .callout-banner__button {
  justify-self: center;
}

.callout-banner--align-right .callout-banner__button {
  justify-self: end;
}

/* Responsive adjustments */
@media screen and (max-width: 749px) {
  .callout-banner__inner {
    padding: 1rem;
  }

  .callout-banner__content {
    gap: 0.75rem;
  }

  .callout-banner__heading {
    font-size: 2rem;
  }

  .callout-banner__subheading {
    font-size: 1rem;
  }

  .callout-banner--height-small {
    min-height: 240px;
  }

  .callout-banner--height-medium {
    min-height: 320px;
  }
}

@media screen and (max-width: 479px) {
  .callout-banner__heading {
    font-size: 1.75rem;
  }
}
