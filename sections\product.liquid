{%- comment -%}
  Prada-Style Product Page
  
  Purpose: Clean, sophisticated product page with image slider and dot indicators
  - Image slider with dot navigation on the left
  - Product details and variant selection on the right
  - Minimalist design inspired by luxury fashion sites
  - Color swatch support with hover preview
{%- endcomment -%}

{%- liquid
  assign classes = ''
  case section.settings.layout_style
    when '50-50'
      assign classes = classes | append: ' product--layout-50-50'
    when '60-40'
      assign classes = classes | append: ' product--layout-60-40'
    when 'stack'
      assign classes = classes | append: ' product--layout-runway'
  endcase
  if section.settings.gallery_two_cols and section.settings.layout_style != 'stack'
    assign classes = classes | append: ' product--gallery-2cols'
  endif
-%}

{% style %}
  /* Prevent horizontal scroll on the page */
  body {
    overflow-x: hidden;
  }
  
    .product-page {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 100vw;
    width: 100%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  @media (min-width: 768px) {
    .product-page {
      grid-template-columns: 1fr 1fr;
      gap: 0;
      max-width: 100vw;
      padding: 0;
      /* Do not force full-viewport height so header isn’t covered */
      min-height: auto;
      overflow-x: hidden; /* Prevent horizontal scroll */
    }
  }
  
  /* Adjust layout for wide screens to better accommodate 5:7 images */
  @media (min-width: 1200px) {
    .product-page {
      grid-template-columns: 60% 40%; /* Give more space to images on wide screens */
    }
  }
  
  @media (min-width: 1600px) {
    .product-page {
      grid-template-columns: 65% 35%; /* Even more space for images on ultra-wide screens */
    }
  }
  
  /* Image Slider Section */
  .product-media {
    position: relative;
    display: flex;
    flex-direction: column;
    height: auto;
  }
  
  @media (min-width: 768px) {
    .product-media {
      width: 100%; /* Take full width of its grid column */
      /* Keep media below sticky header and avoid covering it and leave peek for next heading */
      height: calc(100vh - var(--header-h, 0) - var(--fold-buffer, 0));
      position: sticky;
      top: var(--header-h, 0);
      display: block; /* Reset to block for desktop */
    }
  }

  /* Prefer dvh/svh for sticky media height when available */
  @supports (height: 100dvh) {
    @media (min-width: 768px) {
      .product-media { height: calc(100dvh - var(--header-h, 0) - var(--fold-buffer, 0)); }
    }
  }
  @supports (height: 100svh) {
    @media (min-width: 768px) {
      .product-media { height: calc(100svh - var(--header-h, 0) - var(--fold-buffer, 0)); }
    }
  }
  
  /* Ensure media takes proper space on wide screens */
  @media (min-width: 1200px) {
    .product-media {
      width: 100%;
      max-width: none;
    }
  }
  
  .product-slider {
    position: relative;
    overflow: hidden;
    background: #f8f8f8;
    cursor: grab;
    user-select: none;
    -webkit-user-select: none;
    touch-action: pan-y;
    width: 100%;
    /* Keep slider above the fold on mobile by subtracting header and summary heights */
    height: min(
      calc(100vh - var(--header-h, 0px) - var(--summary-height, 0px) - var(--fold-buffer, 0px)),
      100vh
    );
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Prefer modern viewport units when supported (improves mobile browser UI behavior) */
  @supports (height: 100dvh) {
    .product-slider { height: calc(100dvh - var(--header-h, 0px) - var(--summary-height, 0px) - var(--fold-buffer, 0px)); }
  }
  @supports (height: 100svh) {
    .product-slider { height: calc(100svh - var(--header-h, 0px) - var(--summary-height, 0px) - var(--fold-buffer, 0px)); }
  }

  /* Runway desktop layout: horizontal media with floating info (overlay) */
  @media(min-width: 990px){
    /* Set CSS variables for Runway layout */
    .product--layout-runway,
    .product[data-layout="stack"] {
      position: relative;
      --header-h: 0px;
      --runway-info-w: 360px;
      /* Responsive media sizing defaults; can be overridden via inline style */
      --runway-media-max: 855px;
      --runway-media-min: 320px;
      --runway-media-w: 60vw;
      /* Single source of truth for slider height */
      --runway-slider-h: calc(100vh - var(--header-h, 0) - var(--fold-buffer, 0));
    }

    /* Prefer dvh/svh for variable when supported */
    @supports (height: 100dvh) {
      .product--layout-runway,
      .product[data-layout="stack"] {
        --runway-slider-h: calc(100dvh - var(--header-h, 0) - var(--fold-buffer, 0));
      }
    }
    @supports (height: 100svh) {
      .product--layout-runway,
      .product[data-layout="stack"] {
        --runway-slider-h: calc(100svh - var(--header-h, 0) - var(--fold-buffer, 0));
      }
    }
    
    /* Main grid: allows normal page scroll */
    .product--layout-runway .product__grid,
    .product[data-layout="stack"] > .product__grid {
      display: block !important;
      padding: 0 !important;
      position: relative !important;
    }
    
    /* Neutralize the product-page wrapper */
    .product--layout-runway .product-page,
    .product[data-layout="stack"] .product-page {
      display: block !important;
      grid-template-columns: none !important;
      gap: 0 !important;
      min-height: 0 !important;
      padding: 0 !important;
    }
    
    /* Media container wrapper: sticky below header */
    .product--layout-runway .product__media,
    .product[data-layout="stack"] .product__media {
      position: sticky !important;
      top: var(--header-h, 0) !important;
      left: 0 !important;
      width: 100vw !important;
      height: var(--runway-slider-h) !important;
      z-index: 1 !important;
      margin-left: calc(-1 * var(--page-margin, 0px)) !important;
      margin-right: calc(-1 * var(--page-margin, 0px)) !important;
    }
    
    /* Media container inner */
    .product--layout-runway .product-media,
    .product[data-layout="stack"] .product-media {
      height: 100% !important;
      position: relative !important;
      margin: 0 !important;
      padding: 0 !important;
      overflow: hidden !important; /* ensure no internal child adds visual gap */
    }
    
    /* Info panel: default runway rules */
    /* Floating info container (now can be moved under the section root) */
    .product--layout-runway > .product__info,
    .product[data-layout="stack"] > .product__info,
    .product--layout-runway .product__info,
    .product[data-layout="stack"] .product__info {
      position: absolute !important;
      /* Use pixel-measured right offset (set by JS) to eliminate sub-pixel rounding between absolute/fixed */
      right: var(--rail-right, var(--pi-edge-gap, clamp(12px, 2.5vw, 22px))) !important;
      /* Never inherit unexpected left positioning */
      left: auto !important;
      /* Start flush to the section top by default; JS will offset when header is sticky */
      --pi-top-gap: 0px;
      top: var(--pi-start-top, var(--pi-top-gap, 0px)) !important;
      width: var(--runway-info-w, 360px) !important;
      max-width: 90vw !important;
      /* Rail container shares the same viewport height as media */
      height: var(--runway-slider-h) !important;
      overflow: visible !important;
      z-index: 100 !important;
      pointer-events: auto !important;
      /* Guard against sub-pixel paint jitter */
      transform: translateZ(0);
      box-sizing: border-box;
    }

    /* Fixed state: stick under header with no animation */
    .product--layout-runway > .product__info.is-fixed,
    .product[data-layout="stack"] > .product__info.is-fixed {
      position: fixed !important;
      top: var(--header-h, 0) !important;
      /* Use the same pixel-measured right offset for both states */
      right: var(--rail-right, var(--pi-edge-gap, clamp(12px, 2.5vw, 22px))) !important;
    }

    /* Released state at the section bottom */
    .product--layout-runway > .product__info.is-released,
    .product[data-layout="stack"] > .product__info.is-released {
      position: absolute !important;
      top: var(--release-top, auto) !important;
      bottom: auto !important;
      /* Use the same pixel-measured right offset for all three states */
      right: var(--rail-right, var(--pi-edge-gap, clamp(12px, 2.5vw, 22px))) !important;
      /* When released, expand to fit content and avoid inner scroll */
      height: auto !important;
    }

    /* When released, remove the card’s bottom margin so the visual edge touches the anchor */
    .product--layout-runway > .product__info.is-released .product-info,
    .product[data-layout="stack"] > .product__info.is-released .product-info {
      margin-bottom: 0 !important;
    }
    
    /* Info panel styling: card inside the floating container */
    .product--layout-runway .product-info,
    .product[data-layout="stack"] .product-info {
      position: relative !important;
      /* No inner scroll: allow natural content height */
      height: auto !important;
      max-height: none !important;
      overflow: visible !important;
      overscroll-behavior: contain;
      scrollbar-gutter: stable both-edges;
      /* Flat, fully opaque background (no backdrop blur or shadows) */
      background: #ffffff !important;
      /* Clean, consistent padding */
      --pi-pad: 24px;
      --pi-gap: 16px;
      --pi-gap-lg: 0; /* tighter layout per requirements */
      padding: var(--pi-pad) !important;
      /* remove drop shadow */
      box-shadow: none !important;
      /* No internal top margin to avoid visual gap when sticking */
      margin-top: 0 !important;
      margin-bottom: var(--pi-offset-bottom, clamp(8px, 2vh, 24px)) !important;
      /* Rely on container right offset for the outer gap */
      margin-right: 0 !important;
      display: flex !important;
      flex-direction: column !important;
      gap: var(--pi-gap-lg) !important;
    }

    /* Styled scrollbar for inner card (subtle, luxury aesthetic) */
    .product--layout-runway .product-info::-webkit-scrollbar,
    .product[data-layout="stack"] .product-info::-webkit-scrollbar {
      width: 4px;
    }
    .product--layout-runway .product-info::-webkit-scrollbar-track,
    .product[data-layout="stack"] .product-info::-webkit-scrollbar-track {
      background: transparent;
    }
    .product--layout-runway .product-info::-webkit-scrollbar-thumb,
    .product[data-layout="stack"] .product-info::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.15);
      border-radius: 2px;
    }
    .product--layout-runway .product-info::-webkit-scrollbar-thumb:hover,
    .product[data-layout="stack"] .product-info::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.25);
    }
    /* Firefox scrollbar styling */
    .product--layout-runway .product-info,
    .product[data-layout="stack"] .product-info {
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.15) transparent;
    }
    /* Remove pseudo-element overlay since we use flat background */
    .product--layout-runway .product-info::before,
    .product[data-layout="stack"] .product-info::before { display: none !important; }

    /* Glass effect option: enable background blur and translucency when requested */
    #product-{{ section.id }}.pi-glass .product-info {
      background: rgba(255,255,255,0.5) !important;
      backdrop-filter: saturate(1.2) blur(18px) !important;
      -webkit-backdrop-filter: saturate(1.2) blur(18px) !important;
    }
    .product--layout-runway .product-info > *,
    .product[data-layout="stack"] .product-info > * {
      position: relative;
      z-index: 1;
    }
    /* Normalize spacing inside product info */
    .product--layout-runway .product-info > * + *,
    .product[data-layout="stack"] .product-info > * + * {
      margin-top: 0 !important; /* gap handles rhythm */
    }
    
    /* Hide mobile summary */
    .product--layout-runway .product-summary,
    .product[data-layout="stack"] .product-summary {
      display: none !important;
    }

    /* Slider viewport: fill container; keep relative positioning to avoid disappearance */
    .product--layout-runway .product-slider,
    .product[data-layout="stack"] .product-slider {
      height: var(--runway-slider-h) !important;
      min-height: 0 !important; /* override global min-height */
      width: 100% !important;
      position: relative !important;
      top: 0 !important;
      left: 0 !important;
      /* Use auto to avoid reserving scrollbar space on Windows */
      overflow-x: auto !important;
      overflow-y: hidden !important;
      background: transparent !important;
      scroll-snap-type: none !important;
      scroll-behavior: auto !important;
      -webkit-overflow-scrolling: touch !important;
      scrollbar-width: none !important; /* Firefox */
      -ms-overflow-style: none !important; /* IE/Edge Legacy */
      overscroll-behavior-x: contain !important; /* contain horizontal overscroll */
      cursor: grab !important;
      /* Allow both horizontal swipe and vertical page scroll when
         pointer is over the slider (fixes inability to scroll page
         while hovering the media in Runway layout). */
      touch-action: pan-x pan-y !important;
    }
    
    /* Hide scrollbar */
    .product--layout-runway .product-slider::-webkit-scrollbar,
    .product[data-layout="stack"] .product-slider::-webkit-scrollbar {
      width: 0 !important;
      height: 0 !important;
      display: none !important;
      background: transparent !important;
    }
    
    /* Container: full height wrapper */
    .product--layout-runway .slider-container,
    .product[data-layout="stack"] .slider-container {
      height: 100% !important;
      width: fit-content !important;
      min-width: 100% !important;
      position: relative !important;
      /* Ensure the overflowing track isn't clipped by the container. */
      overflow: visible !important;
    }
    
    /* Track: flex container for images */
    .product--layout-runway .slider-track,
    .product[data-layout="stack"] .slider-track {
      height: 100% !important;
      /* Allow the track to grow wider than the viewport so the
         slider actually has horizontal overflow. This overrides the
         generic `.slider-track { width:100%; max-width:100%; }`. */
      width: max-content !important;
      max-width: none !important;
      display: flex !important;
      gap: var(--runway-gap, 5px) !important;
      padding: 0 !important;
      margin: 0 !important;
      user-select: none !important;
    }

    /* End gutter so the last image can clear the fixed info panel */
    .product--layout-runway .slider-track::after,
    .product[data-layout="stack"] .slider-track::after {
      content: "";
      /* Add extra spacer equal to the info width + track gap + right edge gap */
      flex: 0 0 calc(
        var(--runway-info-w, 360px)
        + var(--runway-gap, 5px)
        + var(--pi-edge-gap, clamp(12px, 2.5vw, 22px))
      );
      pointer-events: none;
      background: transparent;
    }
    
    /* Grabbing cursor when dragging */
    .product--layout-runway .slider-track:active,
    .product[data-layout="stack"] .slider-track:active,
    .product--layout-runway .slider-track.dragging,
    .product[data-layout="stack"] .slider-track.dragging {
      cursor: grabbing !important;
    }
    
    /* Hide scrollbar on Webkit browsers */
    .product--layout-runway .slider-track::-webkit-scrollbar,
    .product[data-layout="stack"] .slider-track::-webkit-scrollbar {
      width: 0 !important;
      height: 0 !important;
      display: none !important;
      background: transparent !important;
    }
    
    /* Images: fixed max width on desktop (Runway spec) */
    .product--layout-runway .slider-image,
    .product[data-layout="stack"] .slider-image {
      position: relative !important;
      opacity: 1 !important;
      /* Responsive width with clamp to match Runway style */
      flex: 0 0 clamp(var(--runway-media-min, 320px), var(--runway-media-w, 60vw), var(--runway-media-max, 855px)) !important;
      width: clamp(var(--runway-media-min, 320px), var(--runway-media-w, 60vw), var(--runway-media-max, 855px)) !important;
      max-width: var(--runway-media-max, 855px) !important;
      height: 100% !important;
      max-height: 100% !important;
      object-fit: cover !important;
      object-position: center !important;
      scroll-snap-align: none !important;
      scroll-snap-stop: normal !important;
      display: block !important;
      user-select: none !important;
      pointer-events: none !important;
    }
    
    /* Hide dots on desktop */
    .product--layout-runway .slider-dots,
    .product[data-layout="stack"] .slider-dots {
      display: none !important;
    }
    
    /* Thumbnails positioning - absolute within sticky media container */
    .product--layout-runway .product-thumbs,
    .product[data-layout="stack"] .product-thumbs {
      position: absolute !important;
      z-index: 90 !important;
    }
    
    .product--layout-runway .product-thumbs.product-thumbs--bottom,
    .product[data-layout="stack"] .product-thumbs.product-thumbs--bottom {
      bottom: 2rem !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
    }
    
    
    /* Remove extra bottom spacing to keep media container compact */
    .product--layout-runway,
    .product[data-layout="stack"] {
      padding-bottom: 0 !important;
    }
  
  /* Ensure sections below product are visible with proper background */
  #shopify-section-{{ section.id }}.product--layout-runway ~ .shopify-section,
  #shopify-section-{{ section.id }}[data-layout="stack"] ~ .shopify-section {
    position: relative !important;
    z-index: 1 !important;
    background: rgb(var(--color-background)) !important;
    margin-top: 0 !important;
  }

  {% if section.settings.layout_style == 'stack' %}
  /* Keep default .page-width alignment for following sections; do not force left anchoring */
  /* Specifically override recommendations container which centers itself */
  #shopify-section-{{ section.id }} ~ .shopify-section .product-recs__inner.page-width {
    margin-left: 0 !important;
    margin-right: auto !important;
    padding-left: var(--page-margin, 1rem) !important;
    /* Keep standard page padding on the right; width below handles the buffer */
    padding-right: var(--page-margin, 1rem) !important;
    /* 20px buffer to the panel by default */
    --recs-buffer: 20px;
    /* Fill the viewport to the left of the panel, regardless of container max */
    width: calc(100vw - (var(--runway-info-w, 360px) + var(--pi-edge-gap, 0px) + var(--recs-buffer))) !important;
    max-width: none !important;
    box-sizing: border-box;
  }

  /* Make Recommended and Recently viewed thumbnails consistent */
  #shopify-section-{{ section.id }} ~ .shopify-section .product-recs__inner .recs-item--simple img {
    width: 100%;
    height: auto;
    aspect-ratio: 3 / 4;         /* match card media */
    object-fit: cover;            /* crop to the same frame */
    object-position: center;      /* align like card media */
    border-radius: 4px;           /* match card radius */
    display: block;
  }

  /* Use the same gap between recommendation thumbnails as the slider gap */
  #shopify-section-{{ section.id }} ~ .shopify-section .product-recs__panels {
    --gap: var(--runway-gap, 5px) !important; /* feeds grid-auto-columns calc */
    --visible: 4 !important;                 /* show 4 items on desktop */
  }
  #shopify-section-{{ section.id }} ~ .shopify-section .recs-track {
    gap: var(--runway-gap, 5px) !important;
    /* Prevent tiny cards: keep a reasonable min width and fill when space allows */
    grid-auto-columns: clamp(240px,
      calc((100% - (var(--runway-gap, 5px) * (var(--visible, 4) - 1))) / var(--visible, 4)),
      1fr) !important;
  }
  {% endif %}
  }
  
  /* Enable horizontal swiping for swipe animation on mobile */
  @media (max-width: 989px) {
    .product-slider.swipe-animation {
      touch-action: pan-x pan-y;
    }
  }
  
  @media (min-width: 768px) {
    .product-slider {
      /* Respect sticky header height so we don’t sit under it */
      height: calc(100vh - var(--header-h, 0) - var(--fold-buffer, 0));
      position: relative;
      min-height: 600px; /* Minimum height to prevent too small containers */
      width: 100%; /* Full width of its container */
    }
  }

  /* Prefer dvh/svh for desktop slider height when available */
  @supports (height: 100dvh) {
    @media (min-width: 768px) {
      .product-slider { height: calc(100dvh - var(--header-h, 0) - var(--fold-buffer, 0)); }
    }
  }
  @supports (height: 100svh) {
    @media (min-width: 768px) {
      .product-slider { height: calc(100svh - var(--header-h, 0) - var(--fold-buffer, 0)); }
    }
  }
  
  /* Ensure slider takes exactly 50% of page width on desktop */
  @media (min-width: 768px) {
    .product-page {
      grid-template-columns: 1fr 1fr; /* Two equal columns */
    }
  }
  
  /* Optimize for wide screens and 5:7 image ratio */
  @media (min-width: 1200px) {
    .product-slider {
      height: calc(100vh - var(--header-h, 0) - var(--fold-buffer, 0));
      max-height: none; /* Remove height cap for better image display */
    }
  }
  
  /* For ultra-wide screens, use full height */
  @media (min-width: 1920px) {
    .product-slider {
      height: calc(100vh - var(--header-h, 0) - var(--fold-buffer, 0)); /* Use viewport minus header */
      max-height: none; /* Remove max-height constraint */
    }
  }
  
  .product-slider:active {
    cursor: grabbing;
  }
  
  /* Slider Container for Scroll Animation */
  .slider-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  
  .slider-track {
    width: 100%;
    max-width: 100%;
    height: 100%;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
  }
  
  /* Fade Animation (Default) */
  .slider-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover; /* Use cover for full width display */
    object-position: center;
    opacity: 0;
    transition: opacity 0.4s ease;
    background: #f8f8f8; /* Match container background */
  }
  
  .slider-image.active {
    opacity: 1;
  }
  .slider-image[role="button"]{ cursor: zoom-in; }
  
  /* Media query for better image handling on wider screens */
  @media (min-width: 768px) and (max-aspect-ratio: 4/3) {
    .slider-image {
      object-fit: cover; /* Use cover for full width display */
      object-position: center;
    }
  }
  
  @media (min-width: 768px) and (min-aspect-ratio: 4/3) {
    .slider-image {
      object-fit: cover; /* Use cover for full width display on wide screens */
      object-position: center;
    }
  }
  
  /* Two-column layout for fade animation */
  .product-slider.two-column .slider-track {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .product-slider.two-column .slider-image {
    position: static;
    display: block;
    width: 100%;
    height: auto;
    opacity: 1;
  }
  
  .product-slider.two-column .slider-image.active {
    opacity: 1;
  }
  
  /* Scroll Animation - Vertical Scroll */
  .product-slider.scroll-animation {
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.6) transparent;
  }
  
  /* Custom Scrollbar for Webkit browsers */
  .product-slider.scroll-animation::-webkit-scrollbar {
    width: 4px;
  }
  
  .product-slider.scroll-animation::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }
  
  .product-slider.scroll-animation::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.6);
    border-radius: 2px;
    transition: background 0.2s ease;
  }
  
  .product-slider.scroll-animation::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.8);
  }
  
  .product-slider.scroll-animation .slider-container {
    overflow: visible;
  }
  
  .product-slider.scroll-animation .slider-track {
    display: block;
    height: auto;
    transition: none;
  }
  
  .product-slider.scroll-animation .slider-image {
    position: static;
    display: block;
    width: 100%;
    height: calc(100vh - var(--summary-height, 120px)); /* Match container height for mobile */
    opacity: 1;
    transition: none;
    object-fit: cover; /* Use cover for full width display */
    object-position: center;
    background: #f8f8f8;
    margin-bottom: 2px; /* Add 2px space between images */
  }
  
  /* Two-column layout for scroll animation */
  .product-slider.scroll-animation.two-column .slider-track {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .product-slider.scroll-animation.two-column .slider-image {
    position: static;
    display: block;
    width: 100%;
    height: auto;
    margin-bottom: 0;
  }
  
 @media (min-width: 768px) {
    .product-slider.scroll-animation .slider-image {
      height: auto; /* Full height on desktop */
    }
  }
  
  /* Responsive object-fit for scroll animation */
  @media (min-width: 768px) and (max-aspect-ratio: 4/3) {
    .product-slider.scroll-animation .slider-image {
      object-fit: cover; /* Use cover for full width display */
      object-position: center;
    }
  }
  
  @media (min-width: 768px) and (min-aspect-ratio: 4/3) {
    .product-slider.scroll-animation .slider-image {
      object-fit: cover; /* Use cover for full width display */
      object-position: center;
    }
  }
  
  /* For ultra-wide screens, ensure images extend properly */
  @media (min-width: 1920px) {
    .product-slider.scroll-animation .slider-image {
      height: auto; /* Full viewport height */
      max-height: none; /* Remove height restrictions */
    }
  }
  
  .product-slider.scroll-animation .slider-image.active {
    opacity: 1;
  }
  
  /* Product Summary Below Slider */
  .product-summary {
    padding: 2rem 1.5rem;
    background: white;
    min-height: 120px; /* Minimum height to prevent clipping */
    height: auto; /* Auto height based on content */
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  @media (min-width: 768px) {
    .product-summary {
      display: none; /* Hide on desktop - use right panel instead */
    }
  }
  
  .product-summary .product-title {
    font-size: 1.25rem;
    font-weight: 400;
    margin: 0 0 0.5rem 0;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  .product-summary .product-price {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    color: #666;
    font-weight: 500;
    word-wrap: break-word;
  }
  
  .product-summary .add-to-cart-btn {
    width: 100%;
    max-width: 100%;
    padding: 1rem;
    background: var(--ui-btn-bg, #000);
    color: var(--ui-btn-fg, #fff);
    border: 1px solid var(--ui-btn-bd, transparent);
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s ease;
    box-sizing: border-box;
  }
  
  .product-summary .add-to-cart-btn:hover {
    background: #333;
  }
  /* Thumbnails */
  .product-thumbs{ position:absolute; z-index:6; display:flex; gap:.5rem; }
  .product-thumbs--bottom{ left:50%; transform:translateX(-50%); bottom: .75rem; flex-direction: row; }
  .product-thumbs--left{ left: .75rem; top:50%; transform: translateY(-50%); flex-direction: column; }
  .product-thumb{ padding:0; border:none; background: transparent; opacity:.7; cursor:pointer; border-radius:6px; overflow:hidden; }
  .product-thumb.is-active{ opacity: 1; outline: 2px solid #fff; box-shadow: 0 0 0 2px rgba(0,0,0,.2); }
  .product-thumb img{ display:block; width:60px; height:60px; object-fit: cover; }
  @media(min-width: 990px){ .product-thumb img{ width:72px; height:72px; } }
  /* Left column thumbs layout */
  .product-media--thumbs-left{ display:grid; grid-template-columns: auto 1fr; align-items:start; gap:.75rem; }
  .product-media--thumbs-left .product-thumbs{
    position: static;
    transform: none;
    left:auto; top:auto; bottom:auto;
    flex-direction: column;
    /* Constrain to available sticky height and allow scrolling */
    max-height: 100%;
    overflow: auto;
    overscroll-behavior: contain;
    scrollbar-gutter: stable both-edges;
    padding-right: 4px;
  }
  /* Allow both thumbs and slider to shrink within the grid to avoid overflow */
  .product-media--thumbs-left .product-thumbs,
  .product-media--thumbs-left .product-slider { min-height: 0; }
  /* Container width and offsets */
  #shopify-section-{{ section.id }} .product__grid{ width: 100%; margin-left: 0; margin-right: 0; padding-top: var(--pad-top-mobile); padding-bottom: var(--pad-bottom-mobile); }
  @media(min-width: 990px){
    #shopify-section-{{ section.id }} .product__grid{ padding-top: var(--pad-top-desktop); padding-bottom: var(--pad-bottom-desktop); }
  }
  /* Media height variations */
  .product-slider.media-height--square .slider-image{ aspect-ratio: 1 / 1; height: auto; }
  .product-slider.media-height--fit_screen { height: calc(100vh - var(--header-h, 0) - var(--fold-buffer, 0)); }
  @supports (height: 100dvh) {
    .product-slider.media-height--fit_screen { height: calc(100dvh - var(--header-h, 0) - var(--fold-buffer, 0)); }
  }
  @supports (height: 100svh) {
    .product-slider.media-height--fit_screen { height: calc(100svh - var(--header-h, 0) - var(--fold-buffer, 0)); }
  }
  .product-slider.scroll-smooth{ scroll-behavior: smooth; }
  /* Two-column big-first layout */
  .product-slider.two-column.two-column--big-first .slider-track{ display:grid; grid-template-columns: 1fr 1fr; gap: 1rem; }
  .product-slider.two-column.two-column--big-first .slider-image{ position: static; opacity: 1; width: 100%; height: auto; }
  .product-slider.two-column.two-column--big-first .slider-image:first-child{ grid-column: 1 / -1; }
  /* Mobile arrows */
  .slider-arrow{ display:none; position:absolute; top:50%; transform:translateY(-50%); z-index:5; background:rgba(255,255,255,.8); border:none; width:32px; height:32px; border-radius:16px; }
  .slider-arrow--prev{ left:.5rem; }
  .slider-arrow--next{ right:.5rem; }
  @media(max-width: 749px){ .slider-arrow{ display:block; } }
  /* Size guide button */
  .size-guide__btn{ display:inline-flex; align-items:center; gap:.5rem; padding:.5rem .75rem; border:1px solid var(--ui-btn-bd, #ddd); border-radius:999px; background: var(--ui-btn-bg, transparent); color: var(--ui-btn-fg, currentColor); cursor:pointer; }
  .notify-btn, .ask-btn, .sticky-atc__btn, .copy-code{ padding: .6rem 1rem; border-radius: 999px; background: var(--ui-btn-bg, #000); color: var(--ui-btn-fg, #fff); border: 1px solid var(--ui-btn-bd, transparent); cursor:pointer; }
  /* Pickup */
  .pdp-pickup{ margin: 1rem 0; }
  .pickup__header{ font-weight: 600; margin-bottom: .25rem; }
  .pickup__list{ list-style:none; padding:0; margin:0; display:flex; flex-direction:column; gap:.25rem; }
  .pickup__item{ display:flex; gap:.5rem; align-items: baseline; }
  .pickup__loc{ font-weight:500; }
  .pickup__eta{ opacity:.75; font-size:.9rem; }
  .pickup__unavailable{ opacity:.7; font-size:.9rem; }
  /* Complementary */
  .pdp-complementary{ margin: 1.25rem 0; }
  .pdp-complementary__grid{ display:grid; grid-template-columns: repeat(2, minmax(0,1fr)); gap: 1rem; }
  @media(min-width: 990px){ .pdp-complementary__grid{ grid-template-columns: repeat(4, minmax(0,1fr)); } }
  .comp-card{ text-decoration:none; color:inherit; display:block; border:1px solid #eee; border-radius: 8px; overflow:hidden; }
  .comp-card__media{ aspect-ratio: 1 / 1; background:#f6f6f6; }
  .comp-card__media img{ width:100%; height:100%; object-fit:cover; display:block; }
  .comp-card__info{ padding:.5rem .75rem; }
  .comp-card__title{ font-size:.95rem; margin:0 0 .25rem; }
  .comp-card__price{ font-size:.9rem; opacity:.85; }
  /* Countdown colors */
  .pdp-countdown{ --cd-text: inherit; --cd-bg: #f0f0f0; --cd-time: inherit; display:flex; align-items:center; gap:.5rem; padding:.5rem; border-radius: 6px; background: var(--cd-bg); }
  .pdp-countdown__label{ color: var(--cd-text); }
  .pdp-countdown__timer{ color: var(--cd-time); font-weight:600; }
  /* Inventory status colors */
  .inventory-status .inv--low{ color: var(--inv-low, #b45309); }
  .inventory-status .inv--high{ color: var(--inv-high, #166534); }
  /* Hide stock/inventory indicators on product page */
  .inventory-status, .stock-status { display: none !important; }
  
  /* Dots under slider in left-thumbs layout */
  .product-media--thumbs-left .slider-dots,
  #product-{{ section.id }}.dots--below-global .slider-dots{ position: static !important; display:flex; justify-content:center; align-items:center; gap:.5rem; margin-top:.75rem; }
  /* Swatch sizes and scroll */
  .swatches-scroll{ max-height: 140px; overflow:auto; padding-right: 4px; }
  .color-swatches.swatches--small_square .color-swatch{ width: 14px; height: 14px; border-radius: 4px; }
  .color-swatches.swatches--big_square .color-swatch{ width: 24px; height: 24px; border-radius: 6px; }
  .color-swatches.swatches--square_with_label .color-swatch{ width: 16px; height: 16px; border-radius: 4px; margin-right: .4rem; }
  .color-swatches.swatches--square_with_label .swatch-text{ font-size: .85rem; margin-right: .5rem; }
  /* Benefits */
  .pdp-benefits{ display:grid; grid-template-columns: repeat(3, minmax(0,1fr)); gap: 1rem; margin: 1rem 0; }
  .benefit{ display:flex; flex-direction:column; gap:.25rem; align-items:flex-start; }
  .benefit__title{ font-weight:600; }
  .benefit__link{ text-decoration: underline; }
  @media(max-width: 749px){ .pdp-benefits{ grid-template-columns: 1fr; } }
  /* Option accuracy */
  .pdp-option-accuracy{ margin: 1rem 0; }
  .oa__heading{ font-weight:600; margin-bottom: .5rem; }
  .oa__points{ display:flex; gap:.5rem; }
  .oa__point{ width: 12px; height: 12px; border-radius: 50%; background: #ddd; }
  .oa__point.is-active{ background: #111; }
  /* Progress range */
  .pdp-progress-range{ display:flex; flex-direction:column; gap:.5rem; margin: 1rem 0; }
  .pr__item{ display:flex; align-items:center; gap:.75rem; }
  .pr__label{ min-width: 8rem; font-size:.9rem; }
  .pr__bar{ flex:1; height: 6px; background: #eee; border-radius: 999px; overflow:hidden; }
  .pr__bar > span{ display:block; height: 100%; background: #111; }
  
  .product-summary .add-to-cart-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
  
  /* Dot Indicators */
  .slider-dots {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    position: absolute;
    left: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
  }
  
  @media (min-width: 768px) {
    .slider-dots {
      left: 2rem;
    }
  }
  
  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    border: none;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .dot.active {
    background: rgba(255, 255, 255, 0.9);
    transform: scale(1.33);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  .dot:hover:not(.active) {
    background: rgba(255, 255, 255, 0.6);
    transform: scale(1.17);
  }
  
  /* Product Info Section */
  .product-info {
    padding: 2rem 1rem;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }
  
  @media (min-width: 768px) {
    .product-info {
      width: 100%;
      max-width: none;
      padding: 3rem 2.5rem;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
    }
  }
  
  @media (min-width: 1200px) {
    .product-info {
      padding: 3rem 3rem 3rem 2rem;
    }
  }
  

  
  .product-description {
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #333;
  }
  
  /* Variant Selection */
  .variant-group {
    margin-bottom: 2rem;
  }
  
  .variant-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }
  
  /* Color Swatches */
  .color-swatches {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }
  
  .color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid #ddd;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
  }
  
  .color-swatch:hover,
  .color-swatch.selected {
    transform: scale(1.1);
    box-shadow: 0 0 0 2px white, 0 0 0 3px #000;
  }
  
  /* Size Selection */
  .size-options {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
  
  .size-option {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: #fff;
    min-width: 52px;
    height: 44px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
  }
  
  .size-option:hover,
  .size-option.selected {
    border-color: #000;
    background: #000;
    color: #fff;
  }
  
  /* Add to Cart */
  .add-to-cart-section {
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .add-to-cart-btn {
    width: 100%;
    height: 56px;
    padding: 0 24px;
    border-radius: 9999px;
    background: #000;
    color: white;
    border: none;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .add-to-cart-btn:hover {
    background: #333;
  }
  
  .add-to-cart-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  /* Enhanced Product Info Styles */
  .product-badge {
    display: inline-block;
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #000;
    margin-bottom: 0.75rem;
  }

  .product-info .product-title {
    font-size: 1.1rem;
    font-weight: 400;
    letter-spacing: 0.05em;
    margin: 0 0 0.75rem 0;
    line-height: 1.2;
  }

  .product-color {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 1.25rem;
  }

  .color-label {
    font-weight: 500;
  }

  /* Product Status */
  .product-status {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }

  .stock-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
  }

  .stock-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .stock-indicator.in-stock {
    background-color: #22c55e;
  }

  .stock-indicator.out-of-stock {
    background-color: #ef4444;
  }

  .points-program {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #0066cc;
  }

  .points-icon {
    color: #0066cc;
    flex-shrink: 0;
  }

  /* Enhanced Price Display */
  .product-info .product-price {
    display: flex;
    align-items: baseline;
    margin: 0;
  }

  .product-info .price {
    font-size: 1.1rem;
    font-weight: 700;
    color: #000;
    line-height: 1.1;
  }

  .product-info .compare-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
    margin-left: 0.5rem;
  }

  .pricing-notice {
    font-size: 0.75rem;
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.4;
  }

  .pricing-notice p {
    margin: 0 0 0.25rem 0;
  }

  .learn-more-link {
    color: #0066cc;
    text-decoration: underline;
  }

  /* Wishlist Button */
  .wishlist-btn {
    width: 100%;
    height: 52px;
    padding: 0 24px;
    background: #f1f1f1;
    color: #000;
    border: 1px solid #e5e5e5;
    border-radius: 9999px;
    font-size: 0.85rem;
    font-weight: 600;
    letter-spacing: 0.08em;
    text-transform: uppercase;
    cursor: pointer;
    transition: background-color 0.2s ease, border-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 12px;
  }

  .wishlist-btn:hover { background: #eaeaea; border-color: #d9d9d9; }

  /* Shipping Information */
  .shipping-info {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f9f9f9;
    border-radius: 4px;
  }

  .shipping-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
  }

  .shipping-item:last-child {
    margin-bottom: 0;
  }

  .shipping-icon {
    font-size: 1rem;
  }

  /* Collapsible Product Details */
  .product-details-accordion {
    margin-top: 2rem;
    border-top: 1px solid #e5e5e5;
  }

  .product-detail-section {
    border-bottom: 1px solid #e5e5e5;
  }

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 0;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    list-style: none;
  }

  .detail-header::-webkit-details-marker {
    display: none;
  }

  .detail-toggle {
    transition: transform 0.2s ease;
    font-size: 0.75rem;
  }

  .product-detail-section[open] .detail-toggle {
    transform: rotate(180deg);
  }

  .detail-content {
    padding-bottom: 1.25rem;
    font-size: 0.875rem;
    line-height: 1.6;
    color: #666;
  }

  /* Visually-hidden radio inputs (robust, no layout shift) */
  .variant-input {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    clip: rect(0 0 0 0) !important;
    clip-path: inset(50%) !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    pointer-events: none !important;
    opacity: 0 !important;
  }

  @media (max-width: 767px) {
    .slider-dots {
      flex-direction: row;
      position: static;
      justify-content: center;
      margin-top: 1.5rem;
      transform: none;
      gap: 1rem;
    }
    
    .dot {
      width: 8px;
      height: 8px;
    }
    
    .product-slider::after {
      content: '';
      position: absolute;
      bottom: 1rem;
      right: 1rem;
      width: 20px;
      height: 20px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23666'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M7 16l3-3m0 0l3 3m-3-3v6m0-6l3-3m-3 3L7 7'/%3e%3c/svg%3e");
      background-size: 12px;
      background-repeat: no-repeat;
      background-position: center;
      opacity: 0.6;
      pointer-events: none;
      animation: swipeHint 3s infinite;
    }
  }
  
  @keyframes swipeHint {
    0%, 80%, 100% { 
      opacity: 0.6; 
      transform: scale(1);
    }
    10%, 70% { 
      opacity: 0.8; 
      transform: scale(1.1);
    }
  }

  /* New Grid Layout Styles */
  #shopify-section-{{ section.id }} .product__grid { 
    display: grid; 
    gap: 24px; 
  }
  
  /* Debug: Force grid when any layout class is present */
  .product--layout-50-50 .product__grid,
  .product--layout-60-40 .product__grid {
    display: grid !important;
    gap: 24px !important;
  }

  @media (min-width: 768px) {
    /* Override the existing product-page grid when using new layouts */
    #shopify-section-{{ section.id }} .product--layout-50-50 .product-page,
    #shopify-section-{{ section.id }} .product--layout-60-40 .product-page {
      display: block !important;
      grid-template-columns: none !important;
      gap: 0 !important;
      min-height: auto !important;
    }
    
    /* Force the new grid layouts */
    #shopify-section-{{ section.id }} .product--layout-50-50 .product__grid {
      display: grid !important;
      grid-template-columns: minmax(0, 1fr) minmax(0, 1fr) !important;
      align-items: start;
      gap: 24px;
    }
    
    #shopify-section-{{ section.id }} .product--layout-60-40 .product__grid {
      display: grid !important;
      grid-template-columns: minmax(0, 3fr) minmax(0, 2fr) !important; /* ≈60/40 */
      align-items: start;
      gap: 24px;
    }
    
    /* Ensure media and info containers don't have conflicting styles */
    #shopify-section-{{ section.id }} .product--layout-50-50 .product__media,
    #shopify-section-{{ section.id }} .product--layout-60-40 .product__media {
      width: 100%;
      max-width: none;
      height: auto;
    }
    
    #shopify-section-{{ section.id }} .product--layout-50-50 .product__info,
    #shopify-section-{{ section.id }} .product--layout-60-40 .product__info {
      width: 100%;
      max-width: none;
      height: auto;
      padding: 2rem;
    }
    
    /* Reset any height constraints from the original design */
    #shopify-section-{{ section.id }} .product--layout-50-50 .product-media,
    #shopify-section-{{ section.id }} .product--layout-60-40 .product-media {
      height: auto !important;
      min-height: 400px;
      position: relative !important;
    }
    
    /* Optional sticky info for 50-50 and 60-40 only */
    #shopify-section-{{ section.id }} .product--layout-50-50 .product__info,
    #shopify-section-{{ section.id }} .product--layout-60-40 .product__info {
      position: sticky;
      /* Keep below header with the same extra gap */
      top: calc(var(--header-h, 0) + var(--pi-top-gap, 5px));
    }
    
    /* Fallback rules without section ID dependency */
    .product--layout-50-50 .product__grid {
      display: grid !important;
      grid-template-columns: minmax(0, 1fr) minmax(0, 1fr) !important;
      align-items: start;
      gap: 24px;
    }
    
    .product--layout-60-40 .product__grid {
      display: grid !important;
      grid-template-columns: minmax(0, 3fr) minmax(0, 2fr) !important;
      align-items: start;
      gap: 24px;
    }
    
    /* Reset conflicting styles */
    .product--layout-50-50 .product-page,
    .product--layout-60-40 .product-page {
      display: block !important;
      grid-template-columns: none !important;
      gap: 0 !important;
      min-height: auto !important;
    }
  }

  /* 2-column gallery (non-destructive to slider) */
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} .product--gallery-2cols .slider-track {
      display: grid !important;
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 12px;
    }
    
    #shopify-section-{{ section.id }} .product--gallery-2cols .slider-container { 
      display: contents; 
    }
    
    #shopify-section-{{ section.id }} .product--gallery-2cols .slider-image { 
      width: auto !important;
      position: static;
      opacity: 1;
    }
  }
{% endstyle %}

{% style %}
@media (min-width: 990px){
  /* (Removed grid split override; Runway uses overlay model) */
}
{% endstyle %}

{% style %}
@media (min-width: 990px){
  /* Make the split happen on the actual grid container */
  #product-{{ section.id }} .product--layout-50-50 .product__grid{
    display:grid;
    grid-template-columns:minmax(0,1fr) minmax(0,1fr);
    align-items:start;
    gap:24px;
  }
  #product-{{ section.id }} .product--layout-60-40 .product__grid{
    display:grid;
    grid-template-columns:minmax(0,3fr) minmax(0,2fr); /* ≈60/40 */
    align-items:start;
    gap:24px;
  }

  /* Neutralize the inner page grid so it can't fight the outer split */
  #product-{{ section.id }} .product--layout-50-50 .product-page,
  #product-{{ section.id }} .product--layout-60-40 .product-page{
    display:block !important;
    grid-template-columns:initial !important;
    min-height:auto !important;
    gap:0 !important;
  }

  /* Prevent inner containers from imposing heights that force stacking */
  #product-{{ section.id }} .product--layout-50-50 .product-media,
  #product-{{ section.id }} .product--layout-60-40 .product-media{
    height:auto !important;
    position:relative !important;
  }
  /* Sticky info for 50-50 and 60-40 only */
  #product-{{ section.id }} .product--layout-50-50 .product__info,
  #product-{{ section.id }} .product--layout-60-40 .product__info {
    position:sticky;
    top: calc(var(--header-h, 0) + var(--pi-top-gap, 5px));
  }
}
{% endstyle %}

{% style %}
@media (min-width: 990px){
  /* Apply the 2-col split on the actual grid container */
  .product[data-layout="50-50"] > .product__grid{
    display:grid;
    grid-template-columns: minmax(0,1fr) minmax(0,1fr);
    align-items:start;
    gap:24px;
  }
  .product[data-layout="60-40"] > .product__grid{
    display:grid;
    grid-template-columns: minmax(0,3fr) minmax(0,2fr); /* ≈60/40 */
    align-items:start;
    gap:24px;
  }

  /* Neutralize the inner page/grid so it can't override the split - but NOT for stack layout */
  .product[data-layout="50-50"] .product-page,
  .product[data-layout="60-40"] .product-page {
    display:block !important;
    grid-template-columns: initial !important;
    gap:0 !important;
    min-height:auto !important;
  }
  .product[data-layout="50-50"] .product-media,
  .product[data-layout="60-40"] .product-media {
    position:relative !important;
    height:auto !important;
  }

  /* Optional sticky info for 50-50 and 60-40 layouts only */
  .product[data-layout="50-50"] .product__info,
  .product[data-layout="60-40"] .product__info {
    position:sticky;
    top: calc(var(--header-h, 0) + var(--pi-top-gap, 5px));
  }
}
{% endstyle %}

{% style %}
@media (min-width: 990px){
  /* Hide dots on desktop when 2-column gallery is active */
  #product-{{ section.id }}.product--gallery-2cols .slider-dots { display: none !important; }
  
  /* Target the same element that holds the gallery flag */
  #product-{{ section.id }}.product--gallery-2cols .product-media{
    position: static !important;
    top: auto !important;
    height: auto !important;
  }
  #product-{{ section.id }}.product--gallery-2cols .product-slider{
    height: auto !important;
    overflow: visible !important;
  }
  #product-{{ section.id }}.product--gallery-2cols .slider-container{
    height: auto !important;
  }
  #product-{{ section.id }}.product--gallery-2cols .slider-track{
    display: grid !important;
    grid-template-columns: repeat(2, minmax(0,1fr));
    gap: 12px;
  }
  #product-{{ section.id }}.product--gallery-2cols .slider-image{
    position: static !important;
    width: 100% !important;
    height: auto !important;
    object-fit: cover;
  }
}
{% endstyle %}

{% style %}
/* Professional, consistent spacing for Runway panel */
@media (min-width: 990px){
  .product--layout-runway .product-info,
  .product[data-layout="stack"] .product-info {
    /* unified rhythm */
    --pi-gap: 16px;
    --pi-section-gap: 20px;
    --variant-h: 44px; /* base control height */
    --atc-h: calc(var(--variant-h) * 0.75); /* 25% reduction used across controls */
    gap: var(--pi-gap) !important;
  }

  /* Title + meta */
  .product--layout-runway .product-info .product-badge{ margin: 0 0 6px !important; }
  .product--layout-runway .product-info .product-title{ margin: 0 0 8px !important; line-height: 1.25; }
  .product--layout-runway .product-info .product-status{ margin: 2px 0 8px !important; gap: 6px; }

  /* Price block */
  .product--layout-runway .product-info .product-price{ justify-content: flex-start !important; margin: 0 0 10px !important; }

  /* Options */
  .product--layout-runway .product-info .variant-group{
    margin: 0 0 var(--pi-group-gap, 2rem) !important;
    gap: 6px; /* tighter space between label and controls */
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* ensure children (label, swatches) share the same left edge */
    padding-left: 0 !important;
  }
  .product--layout-runway .product-info .variant-group .variant-header{
    display: flex; align-items: baseline; justify-content: space-between; width: 100%;
  }
  .product--layout-runway .product-info .variant-group + .variant-group{ margin-top: 6px !important; }
  .product--layout-runway .product-info .variant-label{
    margin: 0 !important; /* rely on group gap for consistent spacing */
    padding-left: 0 !important;
  }
  .product--layout-runway .product-info .size-guide-link{
    appearance: none; background: transparent; border: 0; color: inherit; cursor: pointer;
    font-size: .85rem; text-decoration: underline; display: inline-flex; align-items: center; gap: .25rem; padding: 0;
  }
  .product--layout-runway .product-info .size-guide-link::after{
    content: "";
    display: inline-block; width: 12px; height: 12px; margin-left: 4px; background-color: currentColor;
    /* external-link icon drawn via mask so it inherits text color */
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M14 3h7v7h-2V6.414l-9.293 9.293-1.414-1.414L17.586 5H14V3z'/%3E%3Cpath fill='%23000' d='M5 5h6v2H7v10h10v-4h2v6H5V5z'/%3E%3C/svg%3E");
    -webkit-mask-repeat: no-repeat; -webkit-mask-position: center; -webkit-mask-size: contain;
            mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M14 3h7v7h-2V6.414l-9.293 9.293-1.414-1.414L17.586 5H14V3z'/%3E%3Cpath fill='%23000' d='M5 5h6v2H7v10h10v-4h2v6H5V5z'/%3E%3C/svg%3E");
            mask-repeat: no-repeat; mask-position: center; mask-size: contain;
  }
  /* Hide old under-options size guide button in Runway */
  .product--layout-runway .product-info .size-guide.size-guide--under_options{ display: none !important; }
  .product--layout-runway .product-info .size-options{ gap: 8px !important; }
  .product--layout-runway .product-info .color-swatches{ gap: 10px !important; }
  /* Ensure swatches align flush with the label edge */
  .product--layout-runway .product-info .variant-group .color-swatches{
    margin-left: 0 !important;
    padding-left: 0 !important;
    justify-content: flex-start !important;
    align-items: center !important;
    justify-self: start !important; /* grid alignment safety */
    position: relative; /* anchor for visually-hidden inputs */
    line-height: 0;     /* avoid tiny baseline offsets on first item */
  }
  /* Make color swatches square (no rounding) */
  .product--layout-runway .product-info .color-swatches .color-swatch{
    border-radius: 0 !important;
    box-sizing: border-box !important; /* keep size stable when borders apply */
    position: relative !important;     /* anchor pseudo highlight */
    width: var(--atc-h) !important;
    height: var(--atc-h) !important;
  }

  /* Remove scale/outer ring that pushes past the left edge; use inner ring instead */
  .product--layout-runway .product-info .color-swatches .color-swatch:hover,
  .product--layout-runway .product-info .color-swatches .color-swatch.selected{
    transform: none !important;
    box-shadow: none !important;
  }
  .product--layout-runway .product-info .color-swatches .color-swatch:hover::after,
  .product--layout-runway .product-info .color-swatches .color-swatch.selected::after{
    content: "";
    position: absolute;
    inset: 2px;                      /* draw inside, preserves outer edges */
    border: 2px solid #000;
    border-radius: 0;                /* square to match swatch */
    pointer-events: none;
  }

  /* Ensure size pills share the same height as swatches */
  .product--layout-runway .product-info .size-option{
    height: var(--atc-h) !important;
    min-width: var(--atc-h) !important; /* at least as wide as tall */
    padding: 0 12px !important;             /* keep comfortable text inset */
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 0 !important;            /* square corners */
  }
  .product--layout-runway .product-info .size-option.selected{ border-radius: 0 !important; }
  .product--layout-runway .product-info .pdp-qty{ margin: 14px 0 18px !important; }

  /* Primary actions */
  .product--layout-runway .product-info .add-to-cart-section{ margin-top: 3rem !important; gap: 12px !important; }
  .product--layout-runway .product-info .add-to-cart-section .atc-row{
    display: flex;
    flex-direction: column; /* stacked lines */
    align-items: flex-start;
    gap: 12px;
    position: relative; /* anchor for arrow submit */
  }
  .product--layout-runway .product-info .add-to-cart-section .qty-control{
    display: grid; grid-template-columns: 32px minmax(40px, 64px) 32px; align-items: stretch;
    height: var(--atc-h); border: 1px solid #d9d9d9; background: #fff;
  }
  /* Minimal inline qty (label + underlined field) */
  .product--layout-runway .product-info .add-to-cart-section .qty-inline{ display:flex; align-items: baseline; gap:8px; }
  .product--layout-runway .product-info .add-to-cart-section .qty-inline .qty-label{ font-size:.85rem; opacity:.85; }
  .product--layout-runway .product-info .add-to-cart-section .qty-inline input[type="number"]{
    width: 64px; height: 28px; border:0; border-bottom:1px solid currentColor; background: transparent; outline: none; font-size:.95rem; padding:0 4px; text-align:left;
    -moz-appearance: textfield;
  }
  .product--layout-runway .product-info .add-to-cart-section .qty-inline input[type=number]::-webkit-outer-spin-button,
  .product--layout-runway .product-info .add-to-cart-section .qty-inline input[type=number]::-webkit-inner-spin-button{ -webkit-appearance:none; margin:0; }

  /* Hover reveal when enabled */
  .product--layout-runway .product-info .add-to-cart-section .atc-row[data-qty-hover="true"] .qty-inline{
    opacity: 0; transform: translateY(-4px); pointer-events: none; height: 0; overflow: hidden; margin: 0;
    transition: opacity 180ms ease, transform 180ms ease;
  }
  .product--layout-runway .product-info .add-to-cart-section .atc-row[data-qty-hover="true"]:hover .qty-inline{
    opacity: 1; transform: translateY(0); pointer-events: auto; height: auto; margin-top: 6px;
  }
  .product--layout-runway .product-info .add-to-cart-section .qty-control input[type="number"]{
    width: 100%; height: 100%; border: none; outline: none; text-align: center; font-size: .95rem;
    -moz-appearance: textfield;
  }
  .product--layout-runway .product-info .add-to-cart-section .qty-control input[type=number]::-webkit-outer-spin-button,
  .product--layout-runway .product-info .add-to-cart-section .qty-control input[type=number]::-webkit-inner-spin-button{
    -webkit-appearance: none; margin: 0;
  }
  .product--layout-runway .product-info .add-to-cart-section .qty-btn{
    display:flex; align-items:center; justify-content:center; background:#000; color:#fff; border:none; cursor:pointer; width:32px; font-size: 1rem;
  }
  .product--layout-runway .product-info .add-to-cart-btn{
    height: 52px !important; /* match wishlist */
    border-radius: 9999px !important; /* same roundness as wishlist */
    display:flex; align-items:center; justify-content:center;
    padding: 0 24px !important; /* same horizontal padding */
    gap: 12px;
    width: 100% !important; /* match wishlist width */
  }
  /* Overlay qty control */
  .product--layout-runway .product-info .atc-combo{ position: relative; display: block; width: 100%; }
  .product--layout-runway .product-info .inbtn-qty-overlay{
    position: absolute; right: 16px; top: 50%; transform: translateY(-50%) translateX(8px);
    display: flex; align-items: center; gap: 6px; opacity: 0; pointer-events: none; transition: opacity 200ms ease, transform 200ms ease; color: #fff;
  }
  .product--layout-runway .product-info .atc-combo[data-qty-hover="true"]:hover .inbtn-qty-overlay,
  .product--layout-runway .product-info .atc-combo[data-qty-hover="true"]:focus-within .inbtn-qty-overlay{ opacity: 1; pointer-events: auto; transform: translateY(-50%) translateX(0); }
  .product--layout-runway .product-info .atc-combo[data-qty-hover="true"]:hover .add-to-cart-btn,
  .product--layout-runway .product-info .atc-combo[data-qty-hover="true"]:focus-within .add-to-cart-btn{ padding-right: calc(24px + 80px) !important; }
  .product--layout-runway .product-info .inbtn-qty__label{ font-size: inherit; font-weight: inherit; text-transform: uppercase; letter-spacing: .05em; color: #fff; }
  .product--layout-runway .product-info .inbtn-qty__input{ font-size: inherit; width: 56px; height: 28px; background: transparent; border: 0; border-bottom: 1px solid currentColor; color: currentColor; outline: none; text-align: left; padding: 0 4px; -moz-appearance: textfield; }
  .product--layout-runway .product-info .inbtn-qty__input::-webkit-outer-spin-button,
  .product--layout-runway .product-info .inbtn-qty__input::-webkit-inner-spin-button{ -webkit-appearance: none; margin: 0; }
  @media (max-width: 989px){ .product--layout-runway .product-info .inbtn-qty-overlay{ display: none !important; } }
  /* Hide standalone quantity block on desktop runway (we render inline in ATC row) */
  .product--layout-runway .product-info .pdp-qty{ display: none !important; }
  .product--layout-runway .product-info .wishlist-btn{ margin-top: 12px !important; }
  .product--layout-runway .product-info .size-guide{ margin-top: 8px !important; }

  /* Content blocks */
  .product--layout-runway .product-info .pdp-description{ margin: 12px 0 !important; line-height: 1.55; }
  .product--layout-runway .product-info .product-details-accordion{ margin-top: var(--pi-section-gap) !important; }
  .product--layout-runway .product-info .shipping-info{ margin: var(--pi-section-gap) 0 !important; }
}
{% endstyle %}

{% style %}
@media (min-width: 990px){
  /* Make the sticky top follow header mode:
     - If header is sticky (always), use header height as offset.
     - If header is natural flow, stick to top of viewport with small gap only. */
  body:has(.site-header.is-sticky-always) #product-{{ section.id }}.product--layout-runway .product-info,
  body:has(.site-header.is-sticky-always) #product-{{ section.id }}[data-layout="stack"] .product-info {
    top: calc(var(--header-h, 0) + var(--pi-top-gap, 5px)) !important;
  }
  body.header--natural-flow #product-{{ section.id }}.product--layout-runway .product-info,
  body.header--natural-flow #product-{{ section.id }}[data-layout="stack"] .product-info {
    top: var(--pi-top-gap, 5px) !important;
  }
}
{% endstyle %}
{% style %}
@media (max-width: 749px) {
  /* All mobile styles (dots or segments) – position within the slider */
  #product-{{ section.id }} .product-slider { position: relative; }

  #product-{{ section.id }} .product-slider .slider-dots {
    position: absolute !important;
    left: 50%;
    top: auto;
    bottom: 4%;                 /* ≈4% from the slider bottom */
    transform: translateX(-50%);/* center horizontally */
    margin: 0;
    flex-direction: row;
    gap: 6px;
    z-index: 20;
  }

  /* Hide dots entirely on mobile when merchant chooses 'none' */
  #product-{{ section.id }}.product--mobile-no-pagination .slider-dots { display: none !important; }

  /* Segments look (keep your dash visuals) */
  #product-{{ section.id }}.product--mobile-segments .dot {
    /* This is overridden by the later styles */
  }
  #product-{{ section.id }}.product--mobile-segments .dot.active {
    /* This is overridden by the later styles */
  }

  /* Hover/focus affordance for accessibility */
  #product-{{ section.id }}.product--mobile-segments .dot:focus-visible {
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }
}

/* Respect reduced motion */
@media (prefers-reduced-motion: reduce) {
  #product-{{ section.id }}.product--mobile-segments .dot {
    transition: none;
  }
}
{% endstyle %}

{% style %}
/* Mobile pagination: fixed dash length + tight, consistent gaps */
#product-{{ section.id }} {
  --seg-l: 18px;                          /* dash length (keep original feel) */
  --seg-h: 2px;                           /* dash height */
  --seg-gap: clamp(4px, 1.2vw, 7px);      /* small, consistent gaps */
}

@media (max-width: 749px){
  /* keep dots/segments inside slider and 4% up from bottom */
  #product-{{ section.id }} .product-slider{ position: relative; }
  #product-{{ section.id }} .product-slider .slider-dots{
    position: absolute !important;
    left: 50%;
    bottom: max(4%, env(safe-area-inset-bottom)) !important;
    top: auto !important;
    transform: translateX(-50%);
    gap: var(--seg-gap) !important;
    margin: 0 !important;
  }

  /* segments: same length for all; active = brighter + a hair thicker */
  #product-{{ section.id }} .slider-dots .dot{
    width: var(--seg-l) !important;
    height: var(--seg-h) !important;
    border-radius: 2px !important;
    background: rgba(255,255,255,.45) !important;
    box-shadow: none !important;
    transform: none !important;           /* avoid scale warping gaps */
    padding: 0 !important;
    margin: 0 !important;
  }
  #product-{{ section.id }} .slider-dots .dot.active{
    background: #fff !important;
    height: calc(var(--seg-h) + .5px) !important;  /* subtle emphasis */
  }
}
{% endstyle %}

{% style %}
#product-{{ section.id }} {
  /* Tunables */
  --seg-l: 18px;                 /* inactive length (keep original) */
  --seg-l-active: 30px;          /* active length – a bit longer */
  --seg-h: 2px;                  /* height */
  --seg-gap: clamp(6px, 1.6vw, 10px); /* tight, consistent spacing */
}

  @media (max-width: 749px){
  #product-{{ section.id }} .product-slider{ position: relative; }
  #product-{{ section.id }}.dots--below-mobile .slider-dots{
    position: static !important;
    display: flex; justify-content: center; gap: var(--seg-gap) !important; margin-top: .75rem !important; transform:none; left:auto; bottom:auto; top:auto;
  }
  #product-{{ section.id }}.product--mobile-segments:not(.dots--below-mobile) .slider-dots{
    position: absolute !important;
    left: 50%;
    bottom: max(4%, env(safe-area-inset-bottom)) !important;
    top: auto !important;
    transform: translateX(-50%);
    gap: var(--seg-gap) !important;
    margin: 0 !important;
    z-index: 20;
  }

  /* Dashes with animated width for the active item */
  #product-{{ section.id }}.product--mobile-segments .dot{
    width: var(--seg-l);
    height: var(--seg-h);
    border-radius: var(--seg-h);
    background: rgba(255,255,255,.45);
    box-shadow: none;
    padding: 0; margin: 0;
    transform: none;                /* prevents gap warping */
    will-change: width, background-color, opacity;
    transition:
      width 260ms cubic-bezier(.33,0,.2,1),
      background-color 200ms ease,
      opacity 200ms ease;
  }
  #product-{{ section.id }}.product--mobile-segments .dot.active{
    width: var(--seg-l-active);     /* longer active dash */
    background: #fff;               /* brighter */
    opacity: 1;
  }

  /* Optional: subtle hover/focus */
  #product-{{ section.id }}.product--mobile-segments .dot:focus-visible{
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }
}

/* Respect reduced motion */
@media (prefers-reduced-motion: reduce){
  #product-{{ section.id }}.product--mobile-segments .dot{
    transition: none;
  }
}
{% endstyle %}

{%- liquid
  assign classes = ''
  case section.settings.layout_style
    when '50-50'  then assign classes = classes | append: ' product--layout-50-50'
    when '60-40'  then assign classes = classes | append: ' product--layout-60-40'
    when 'stack'  then assign classes = classes | append: ' product--layout-runway'
  endcase
  if section.settings.gallery_two_cols
    assign classes = classes | append: ' product--gallery-2cols'
  endif
-%}

<script>
  // Set and track header height (includes announcement bar when grouped)
  (function() {
    const findHeader = () => (
      document.querySelector('.shopify-section-group-header-group') ||
      document.querySelector('.shopify-section-header-group') ||
      document.querySelector('header.header') ||
      document.querySelector('header[role="banner"]') ||
      document.querySelector('.header')
    );
    let headerEl = null;
    let lastH = -1;
    let ro = null;
    const applyH = (h) => {
      if (h === lastH) return;
      lastH = h;
      document.documentElement.style.setProperty('--header-h', h + 'px');
      document.dispatchEvent(new CustomEvent('runway:header-height', { detail: { height: h } }));
    };
    const measure = () => {
      const h = headerEl ? headerEl.offsetHeight : 0;
      applyH(h);
    };
    const connect = () => {
      const el = findHeader();
      if (el === headerEl) { measure(); return; }
      if (ro) { try { ro.disconnect(); } catch(e){} ro = null; }
      headerEl = el;
      if (headerEl && 'ResizeObserver' in window) {
        ro = new ResizeObserver(() => measure());
        ro.observe(headerEl);
      }
      measure();
    };
    // Initial connect + delayed retries to catch late mounts
    connect();
    if (document.readyState === 'loading') document.addEventListener('DOMContentLoaded', connect);
    window.addEventListener('load', connect);
    window.addEventListener('resize', measure, { passive: true });
    setTimeout(connect, 100);
    setTimeout(connect, 500);
    setTimeout(connect, 1000);
    // Fallback: watch DOM for header group replacements (rare but possible)
    if ('MutationObserver' in window) {
      const mo = new MutationObserver(connect);
      mo.observe(document.documentElement, { childList: true, subtree: true });
    }
  })();
</script>

<script>
// Enhance recommendation images (Recommended/Recently viewed) with responsive srcset
document.addEventListener('DOMContentLoaded', function() {
  const buildSrc = (url, w) => {
    if (!url) return url;
    if (/([?&])width=\d+/.test(url)) return url; // already parameterized
    const sep = url.includes('?') ? '&' : '?';
    return url + sep + 'width=' + w;
  };
  const applyResponsive = (img) => {
    if (!img || img.hasAttribute('srcset')) return; // already responsive
    const src = img.getAttribute('src');
    if (!src) return;
    const set = [400,600,800,1067].map(w => `${buildSrc(src, w)} ${w}w`).join(', ');
    img.setAttribute('srcset', set);
    img.setAttribute('sizes', '(min-width: 990px) calc(100vw / 4), 50vw');
    img.setAttribute('decoding', 'async');
    if (!img.hasAttribute('loading')) img.setAttribute('loading', 'lazy');
  };
  const scan = (root=document) => {
    root.querySelectorAll('.product-recs__inner .recs-item--simple img').forEach(applyResponsive);
  };
  scan();
  // Observe dynamic recommendation loads
  if ('MutationObserver' in window) {
    const mo = new MutationObserver(muts => {
      for (const m of muts) {
        if (m.addedNodes && m.addedNodes.length) {
          m.addedNodes.forEach(n => { if (n.nodeType === 1) scan(n); });
        }
      }
    });
    mo.observe(document.documentElement, { childList: true, subtree: true });
  }
});
</script>

  <script>
    // Compute and keep CSS --summary-height in sync so the slider fits above the fold on mobile
    (function(){
      const root = document.getElementById('product-{{ section.id }}');
      if (!root) return;

    const getHeaderH = () => {
      // Prefer inline style set by header script; fallback to computed
      const inline = document.documentElement.style.getPropertyValue('--header-h');
      if (inline) return parseInt(inline, 10) || 0;
      const comp = getComputedStyle(document.documentElement).getPropertyValue('--header-h');
      return parseInt(comp, 10) || 0;
    };

    const setVars = () => {
      const sm = root.querySelector('.product-summary');
      let sumH = 0;
      if (sm && getComputedStyle(sm).display !== 'none') {
        // Use bounding rect to account for responsive content
        sumH = Math.ceil(sm.getBoundingClientRect().height);
      }
      root.style.setProperty('--summary-height', sumH + 'px');

      // Ensure left-side thumbnails never exceed the available sticky height
      const media = root.querySelector('.product-media');
      const thumbs = root.querySelector('.product-media--thumbs-left .product-thumbs');
      if (media && thumbs) {
        const headerH = getHeaderH();
        const vh = (window.visualViewport && typeof visualViewport.height === 'number')
          ? visualViewport.height
          : window.innerHeight;
        const avail = Math.max(200, Math.floor(vh - headerH));
        thumbs.style.maxHeight = avail + 'px';
      }

      // Compute fold buffer so we can peek the next section heading + 30px margin
      let foldBuffer = 0;
      const MARGIN_BELOW = 30;
      try {
        const thisWrapper = root.closest('.shopify-section');
        let next = thisWrapper ? thisWrapper.nextElementSibling : null;
        while (next && !(next.classList && next.classList.contains('shopify-section'))) {
          next = next.nextElementSibling;
        }
        if (next) {
          const heading = next.querySelector('h1, h2, h3, [role="heading"], .section-title, .section__title, .title, .product-recs__heading, .recs__heading, .complementary__heading');
          const hRect = heading ? heading.getBoundingClientRect() : null;
          const grid = root.querySelector('.product__grid');
          const gridPB = grid ? parseFloat(getComputedStyle(grid).paddingBottom) || 0 : 0;
          const cont = next.querySelector('.page-width') || next;
          const nextPadTop = cont ? parseFloat(getComputedStyle(cont).paddingTop) || 0 : 0;
          const headingH = hRect ? Math.ceil(hRect.height) : 0;
          foldBuffer = Math.max(0, Math.round(headingH + nextPadTop + gridPB + MARGIN_BELOW));
        } else {
          // Fallback when no next section exists
          foldBuffer = 60; // show ~heading + margin
        }
      } catch(_e) {
        foldBuffer = 60;
      }
      root.style.setProperty('--fold-buffer', foldBuffer + 'px');
    };
    const schedule = () => requestAnimationFrame(setVars);

    document.addEventListener('runway:header-height', schedule);
    window.addEventListener('resize', schedule, { passive: true });
    if (window.visualViewport) visualViewport.addEventListener('resize', schedule);
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', schedule);
    } else {
      schedule();
    }
    window.addEventListener('load', schedule);
    setTimeout(schedule, 80);
    setTimeout(schedule, 400);
  })();
</script>

  <div id="product-{{ section.id }}"
     class="product{% if section.settings.layout_style == 'stack' %} product--layout-runway{% endif %}{{ classes }}{% if section.settings.mobile_pagination_style == 'segments' %} product--mobile-segments{% endif %}{% if section.settings.mobile_pagination_style == 'none' %} product--mobile-no-pagination{% endif %}{% if section.settings.product_info_bg == 'glass' %} pi-glass{% endif %}{% case section.settings.dots_position %}{% when 'below_global' %} dots--below-global{% when 'below_mobile' %} dots--below-mobile{% endcase %}"
     data-layout="{{ section.settings.layout_style }}"
     data-mobile-pagination="{{ section.settings.mobile_pagination_style }}">
  <div class="product__grid" style="
       --pad-top-desktop: {{ section.settings.desktop_offset_top | default: 0 }}px;
       --pad-bottom-desktop: {{ section.settings.desktop_offset_bottom | default: 0 }}px;
       --pad-top-mobile: {{ section.settings.mobile_offset_top | default: 0 }}px;
       --pad-bottom-mobile: {{ section.settings.mobile_offset_bottom | default: 0 }}px;
       {% if section.settings.ui_button_color != blank %}--ui-btn-fg: {{ section.settings.ui_button_color }};{% endif %}
       {% if section.settings.ui_button_bg != blank %}--ui-btn-bg: {{ section.settings.ui_button_bg }};{% endif %}
       {% if section.settings.ui_button_border != blank %}--ui-btn-bd: {{ section.settings.ui_button_border }};{% endif %}
       /* Runway responsive media controls */
       --runway-media-max: {{ section.settings.runway_media_max_px | default: 855 }}px;
       --runway-media-min: {{ section.settings.runway_media_min_px | default: 320 }}px;
       --runway-media-w: {{ section.settings.runway_media_vw | default: 60 }}vw;
    ">
    <div class="product__media">
      <section class="product-page" data-section="{{ section.id }}">
        <!-- Image Slider Section -->
        <div class="product-media{% if section.settings.show_thumbnails and product.media.size > 1 and section.settings.thumbnails_position == 'left' %} product-media--thumbs-left{% endif %}">
          {%- if section.settings.show_thumbnails and product.media.size > 1 -%}
            {%- comment -%} Thumbnails outside slider for left/bottom positions {%- endcomment -%}
            <div class="product-thumbs product-thumbs--{{ section.settings.thumbnails_position | default: 'bottom' }}">
              {%- for media in product.media limit: 12 -%}
                <button class="product-thumb{% if forloop.first %} is-active{% endif %}" data-thumb-index="{{ forloop.index0 }}" aria-label="Thumbnail {{ forloop.index }}">
                  {{ media | image_url: width: 120 | image_tag: loading: 'lazy', decoding: 'async', widths: '80, 100, 120', alt: media.alt | default: product.title }}
                </button>
              {%- endfor -%}
            </div>
          {%- endif -%}
          {%- liquid
            assign media_layout = section.settings.media_layout | default: 'stacked'
            assign media_height = section.settings.media_height | default: 'full'
            assign is_two_col = false
            assign is_big_first = false
            if media_layout == 'stacked_2_columns'
              assign is_two_col = true
            elsif media_layout == 'stacked_2_columns_big'
              assign is_two_col = true
              assign is_big_first = true
            endif
          -%}
          <div class="product-slider
                      {% if section.settings.layout_style == 'stack' %} runway{% endif %}
                      {% if media_layout == 'stacked' and section.settings.layout_style != 'stack' %} scroll-animation{% endif %}
                      {% if media_layout == 'carousel' and section.settings.layout_style != 'stack' %} swipe-animation{% endif %}
                      {% if is_two_col and section.settings.layout_style != 'stack' %} two-column{% endif %}
                      {% if is_big_first and section.settings.layout_style != 'stack' %} two-column--big-first{% endif %}
                      {% if section.settings.smooth_scroll_stacked %} scroll-smooth{% endif %}
                      media-height--{{ media_height }}
                      thumbnails--{{ section.settings.thumbnails_position | default: 'bottom' }}
                     " id="productSlider">
            <div class="slider-container">
              <div class="slider-track" id="sliderTrack">
                {%- for media in product.media limit: 6 -%}
                  {%- liquid
                    assign is_runway = false
                    if section.settings.layout_style == 'stack'
                      assign is_runway = true
                    endif
                  -%}
                  <img
                    src="{{ media | image_url: width: 1200 }}"
                    srcset="{{ media | image_url: width: 600 }} 600w,
                            {{ media | image_url: width: 900 }} 900w,
                            {{ media | image_url: width: 1200 }} 1200w,
                            {{ media | image_url: width: 1600 }} 1600w,
                            {{ media | image_url: width: 2000 }} 2000w"
                    sizes="{% if is_runway %}(min-width: 1600px) {{ section.settings.runway_media_max_px | default: 855 }}px, (min-width: 990px) {{ section.settings.runway_media_vw | default: 60 }}vw, 100vw{% else %}(min-width: 768px) 50vw, 100vw{% endif %}"
                    alt="{{ media.alt | default: product.title }}"
                    class="slider-image{% if forloop.first %} active{% endif %}"
                    data-full="{{ media | image_url: width: 2400 }}"
                    {% unless is_runway %}role="button"{% endunless %}
                    data-media-id="{{ media.id }}"
                    width="{{ media.width | default: 1200 }}"
                    height="{{ media.height | default: 1600 }}"
                    {% if forloop.first %}loading="eager" fetchpriority="high"{% else %}loading="lazy" fetchpriority="auto"{% endif %}
                    draggable="false"
                  >
                {%- endfor -%}
              </div>
            </div>
            
            {%- if product.media.size > 1 -%}
              <div class="slider-dots" role="listbox" aria-label="{{ 'accessibility.slider_pagination' | t | default: 'Slide pagination' }}">
                {%- assign total = product.media.size -%}
                {%- for media in product.media limit: 6 -%}
                  <button
                    class="dot{% if forloop.first %} active{% endif %}"
                    data-slide="{{ forloop.index0 }}"
                    aria-label="{{ 'accessibility.slide_n_of_m' | t: index: forloop.index, total: total | default: 'Slide ' | append: forloop.index | append: ' of ' | append: total }}"
                    {% if forloop.first %}aria-current="true"{% endif %}
                    title="{{ 'accessibility.go_to_slide' | t | default: 'Go to slide' }} {{ forloop.index }}"
                  ></button>
                {%- endfor -%}
              </div>
              {%- if section.settings.show_arrows_mobile and media_layout == 'carousel' -%}
                <button class="slider-arrow slider-arrow--prev" data-prev aria-label="Previous image">‹</button>
                <button class="slider-arrow slider-arrow--next" data-next aria-label="Next image">›</button>
              {%- endif -%}
            {%- endif -%}
          </div>
          
          <!-- Product Summary Below Slider -->
          <div class="product-summary">
            <h1 class="product-title">{{ product.title | escape }}</h1>
            
            <div class="product-price">
              <span id="productPriceSummary">{{ product.selected_or_first_available_variant.price | money }}</span>
              {%- if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
                <span class="original-price" style="text-decoration: line-through; margin-left: 0.5rem; color: #999;">
                  {{ product.selected_or_first_available_variant.compare_at_price | money }}
                </span>
              {%- endif -%}
            </div>

            <button 
              type="button" 
              class="add-to-cart-btn"
              id="addToCartBtnSummary"
              onclick="document.getElementById('addToCartBtn').click()"
              {% unless product.selected_or_first_available_variant.available %}disabled{% endunless %}
            >
              {%- if product.selected_or_first_available_variant.available -%}
                {{ 'products.product.add_to_cart' | t | default: 'Add to cart' }}
              {%- else -%}
                {{ 'products.product.sold_out' | t | default: 'Sold out' }}
              {%- endif -%}
            </button>
          </div>
        </div>
      </section>
    </div>
    {% capture product_info_html %}
    <div class="product__info">
      <!-- Product Info Section -->
      <div class="product-info">
        {% if section.blocks.size > 0 %}
          {%- comment -%} Outside-form blocks {%- endcomment -%}
          {%- if section.settings.show_new_badge -%}
            <div class="product-badge">{{ 'products.product.badge_new' | t | default: 'NEW' }}</div>
          {%- endif -%}
          {% for block in section.blocks %}
            {% case block.type %}
              {% when 'benefits' %}
                <div class="pdp-benefits" {{ block.shopify_attributes }}>
                  {% for i in (1..3) %}
                    {% assign icon_key = 'icon' | append: i %}
                    {% assign heading_key = 'heading' | append: i %}
                    {% assign link_label_key = 'link_label' | append: i %}
                    {% assign link_url_key = 'link_url' | append: i %}
                    {% assign icon = block.settings[icon_key] %}
                    {% assign heading = block.settings[heading_key] %}
                    {% assign label = block.settings[link_label_key] %}
                    {% assign url = block.settings[link_url_key] %}
                    {% if icon or heading or label %}
                      <div class="benefit">
                        {% if icon %}{{ icon | image_url: width: 36 | image_tag: width:36, height:36, alt:'' }}{% endif %}
                        {% if heading %}<div class="benefit__title">{{ heading }}</div>{% endif %}
                        {% if label and url %}<a class="benefit__link" href="{{ url }}">{{ label }}</a>{% endif %}
                      </div>
                    {% endif %}
                  {% endfor %}
                </div>
              {% when 'option_accuracy' %}
                <div class="pdp-option-accuracy" {{ block.shopify_attributes }}>
                  {% if block.settings.heading %}<div class="oa__heading">{{ block.settings.heading }}</div>{% endif %}
                  {% assign active = block.settings.active_state | default: 3 %}
                  <div class="oa__points">
                    {% for i in (1..6) %}
                      {% assign label_key = 'point_' | append: i %}
                      {% assign label = block.settings[label_key] %}
                      <div class="oa__point{% if i <= active %} is-active{% endif %}" aria-label="{{ label | default: 'Point' }}" title="{{ label | default: '' }}"></div>
                    {% endfor %}
                  </div>
                </div>
              {% when 'progress_range' %}
                <div class="pdp-progress-range" {{ block.shopify_attributes }}>
                  {% for i in (1..4) %}
                    {% assign show_key = 'show_' | append: i %}
                    {% assign label_key = 'label_' | append: i %}
                    {% assign value_key = 'value_' | append: i %}
                    {% if block.settings[show_key] %}
                      <div class="pr__item">
                        <div class="pr__label">{{ block.settings[label_key] | default: 'Item ' | append: i }}</div>
                        {% assign v = block.settings[value_key] | default: 0 %}
                        <div class="pr__bar"><span style="width: {{ v }}%"></span></div>
                      </div>
                    {% endif %}
                  {% endfor %}
                </div>
              {% when 'badges' %}
                <div class="pdp-badges" {{ block.shopify_attributes }}>
                  {%- for t in product.tags -%}
                    {%- if t contains 'label__' -%}
                      {%- assign rem = t | remove_first: 'label__' -%}
                      {%- assign parts = rem | split: ':' -%}
                      {%- assign tag_label = parts[0] -%}
                      {%- assign modifier = parts[1] | default: '' -%}
                      <span class="badge{% if modifier != '' %} badge--{{ modifier }}{% endif %}">{{ tag_label }}</span>
                    {%- endif -%}
                  {%- endfor -%}
                </div>
              {% when 'vendor_sku' %}
                <div class="pdp-vendor-sku" {{ block.shopify_attributes }}>
                  {% if block.settings.show_vendor and product.vendor != blank %}
                    <div class="pdp-vendor">{% if block.settings.vendor_link %}{{ product.vendor | link_to_vendor }}{% else %}{{ product.vendor }}{% endif %}</div>
                  {% endif %}
                  {% assign sku_val = product.selected_or_first_available_variant.sku %}
                  {% if block.settings.show_sku and sku_val != blank %}
                    {% assign sku_text = block.settings.sku_text | default: 'SKU: {SKU}' %}
                    {% assign sku_placeholder = '{' | append: 'SKU' | append: '}' %}
                    <div class="pdp-sku">{{ sku_text | replace: sku_placeholder, sku_val }}</div>
                  {% endif %}
                </div>
              {% when 'title' %}
                <h1 class="product-title" {{ block.shopify_attributes }}>{{ product.title | escape }}</h1>
              {% when 'price' %}
                <div class="product-price" {{ block.shopify_attributes }}>
                  <span class="price" id="productPrice">{{ product.selected_or_first_available_variant.price | money }}</span>
                  {% if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price %}
                    <span class="compare-price">{{ product.selected_or_first_available_variant.compare_at_price | money }}</span>
                  {% endif %}
                </div>
                {% if section.settings.show_taxes_included_message and shop.taxes_included %}
                  <div class="pricing-notice">
                    <p>{{ 'products.product.include_taxes' | t | default: 'Taxes included.' }}</p>
                  </div>
                {% endif %}
              {% when 'text' %}
                {% if block.settings.text != blank %}
                  <div class="pdp-text rte" {{ block.shopify_attributes }}>{{ block.settings.text }}</div>
                {% endif %}
            {% endcase %}
          {% endfor %}

          {%- comment -%} Product form with form-related blocks {%- endcomment -%}
          {% form 'product', product, class: 'product-form', id: 'product-form' %}
            {% for block in section.blocks %}
              {% case block.type %}
                {% when 'options' %}
                  {%- for option in product.options_with_values -%}
                    {%- liquid
                      assign option_name = option.name | downcase
                      assign is_color = false
                      if option_name contains 'color' or option_name contains 'colour'
                        assign is_color = true
                      endif
                    -%}
                    <div class="variant-group" {{ block.shopify_attributes }}>
                      <div class="variant-header">
                        <label class="variant-label">{{ option.name }}</label>
                        {%- assign on = option.name | downcase -%}
                        {%- if on contains 'size' and section.settings.show_size_guide -%}
                          <button type="button" class="size-guide-link" data-open-size-guide aria-label="Size guide">{{ 'products.product.size_guide' | t | default: 'Size Guide' }}</button>
                        {%- endif -%}
                      </div>
                      {% if block.settings.variant_picker == 'dropdown' %}
                        <select name="options[{{ option.name | escape }}]" class="variant-input" data-option-position="{{ option.position }}">
                          {%- for value in option.values -%}
                            <option value="{{ value | escape }}" {% if option.selected_value == value %}selected{% endif %}>{{ value }}</option>
                          {%- endfor -%}
                        </select>
                      {% else %}
                        {% if is_color %}
                          {% assign swatch_type = block.settings.color_swatch_type | default: 'small_square' %}
                          {% assign preview = block.settings.enable_color_swatch_preview | default: false %}
                          {% assign maxh = block.settings.enable_color_swatch_max_height | default: false %}
                          <div class="color-swatches swatches--{{ swatch_type }}" {% if preview %}data-preview="true"{% endif %} data-color-position="{{ option.position }}">
                            {% if maxh %}<div class="swatches-scroll">{% endif %}
                            {%- for value in option.values -%}
                              <input type="radio" name="options[{{ option.name | escape }}]" value="{{ value | escape }}" id="opt-{{ option.position }}-{{ forloop.index0 }}" class="variant-input" {% if option.selected_value == value %}checked{% endif %} data-option-position="{{ option.position }}">
                              <label for="opt-{{ option.position }}-{{ forloop.index0 }}" class="color-swatch swatch--{{ value | handle }}{% if option.selected_value == value %} selected{% endif %}" title="{{ value | escape }}" data-color-value="{{ value | escape }}"></label>
                              {% if swatch_type == 'square_with_label' %}<span class="swatch-text">{{ value }}</span>{% endif %}
                            {%- endfor -%}
                            {% if maxh %}</div>{% endif %}
                          </div>
                        {% else %}
                          <div class="size-options">
                            {%- for value in option.values -%}
                              <input type="radio" name="options[{{ option.name | escape }}]" value="{{ value | escape }}" id="opt-{{ option.position }}-{{ forloop.index0 }}" class="variant-input" {% if option.selected_value == value %}checked{% endif %} data-option-position="{{ option.position }}">
                              <label for="opt-{{ option.position }}-{{ forloop.index0 }}" class="size-option{% if option.selected_value == value %} selected{% endif %}">{{ value | escape }}</label>
                            {%- endfor -%}
                          </div>
                        {% endif %}
                      {% endif %}
                    </div>
                  {%- endfor -%}
                {% when 'quantity' %}
                  <div class="pdp-qty" {{ block.shopify_attributes }}>
                    <label for="Quantity-{{ section.id }}">{{ 'products.product.quantity' | t | default: 'Quantity' }}</label>
                    <input id="Quantity-{{ section.id }}" type="number" name="quantity" value="1" min="1" inputmode="numeric">
                  </div>
                {% when 'custom_field' %}
                  <div class="pdp-custom-field" {{ block.shopify_attributes }}>
                    {% if block.settings.required_text != blank %}
                      <label><input type="checkbox" id="cf-agree-{{ block.id }}"> {{ block.settings.required_text }}</label>
                    {% endif %}
                    <label for="cf-input-{{ block.id }}">{{ block.settings.field_label | default: 'Custom message' }}</label>
                    <input id="cf-input-{{ block.id }}" type="text" name="properties[{{ block.settings.field_label | default: 'Custom message' }}]">
                  </div>
                {% when 'add_to_cart' %}
                  <div class="add-to-cart-section" {{ block.shopify_attributes }}>
                    <div class="atc-row">
                      <div class="atc-combo"{% if section.settings.show_inline_qty %} data-qty-hover="true"{% endif %}>
                        <button type="submit" name="add" class="add-to-cart-btn" id="addToCartBtn" {% unless product.selected_or_first_available_variant.available %}disabled{% endunless %}>
                          <span class="atc-text">{% if product.selected_or_first_available_variant.available %}{{ 'products.product.add_to_cart' | t | default: 'Add to cart' }}{% else %}{{ 'products.product.sold_out' | t | default: 'Sold out' }}{% endif %}</span>
                        </button>
                        {% if section.settings.show_inline_qty %}
                        <div class="inbtn-qty-overlay" role="group" aria-label="{{ 'products.product.quantity' | t | default: 'Quantity' }}">
                          <span class="inbtn-qty__label">QTY</span>
                          <input id="QtyOverlay-{{ section.id }}" class="inbtn-qty__input" type="number" min="1" inputmode="numeric" data-qty-input>
                        </div>
                        {% endif %}
                      </div>
                    </div>
                    {% if block.settings.show_additional_payment_buttons %}
                      {{ form | payment_button }}
                    {% endif %}
                    {% if section.settings.show_pay_installments %}
                      <div class="pdp-payment-terms">{{ form | payment_terms }}</div>
                    {% endif %}
                    {%- if section.settings.show_size_guide -%}
                    <div class="size-guide size-guide--{{ section.settings.size_guide_button_placement | default: 'under_options' }}">
                      <button type="button" class="size-guide__btn" data-open-size-guide>
                        {%- if section.settings.size_guide_button_icon -%}
                          {%- assign ic = section.settings.size_guide_button_icon -%}
                          {{ ic | image_url: width: 24 | image_tag: loading: 'lazy', decoding: 'async', width: 24, height: 24, alt: ic.alt | default: '' }}
                        {%- endif -%}
                        <span>{{ section.settings.size_guide_button_label | default: 'Size guide' }}</span>
                      </button>
                    </div>
                    {%- endif -%}
                    {%- if section.settings.show_wishlist_btn -%}
                      <button type="button" class="wishlist-btn" id="addToWishlist">
                        <span class="wishlist-icon">♡</span>
                        {%- liquid
                          assign wl = section.settings.wishlist_button_label | strip
                          if wl == blank
                            assign wl = 'products.product.add_to_wishlist' | t | default: 'Add to wishlist'
                          endif
                        -%}
                        {{ wl }}
                      </button>
                    {%- endif -%}
                  </div>
              {% endcase %}
            {% endfor %}
            <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}" id="variantId">
          {% endform %}

          {%- comment -%} After-form blocks {%- endcomment -%}
          {% for block in section.blocks %}
            {% case block.type %}
              {% when 'description' %}
                {% if product.description != blank %}<div class="pdp-description rte" {{ block.shopify_attributes }}>{{ product.description }}</div>{% endif %}
                {% when 'inventory_status' %}
                {% if section.settings.show_stock_status %}
                {% assign v = product.selected_or_first_available_variant %}
                {% if v %}
                  {% assign qty = v.inventory_quantity | default: 0 %}
                  {% assign low = block.settings.low_stock_threshold | default: 3 %}
                  <div class="inventory-status" style="{% if block.settings.color_low_text %}--inv-low: {{ block.settings.color_low_text }};{% endif %}{% if block.settings.color_high_text %} --inv-high: {{ block.settings.color_high_text }};{% endif %}" {{ block.shopify_attributes }}>
                    {% if v.available %}
                      {% if qty <= low %}
                        {% if block.settings.low_stock_icon %}{% assign i = block.settings.low_stock_icon %}{{ i | image_url: width: 20 | image_tag: width: 20, height: 20, alt: '' }}{% endif %}
                        <span class="inv inv--low">{{ block.settings.low_stock_text | default: 'Low stock' }}</span>
                      {% else %}
                        {% if block.settings.high_stock_icon %}{% assign i = block.settings.high_stock_icon %}{{ i | image_url: width: 20 | image_tag: width: 20, height: 20, alt: '' }}{% endif %}
                        <span class="inv inv--high">{{ block.settings.high_stock_text | default: 'In stock' }}</span>
                      {% endif %}
                    {% else %}
                      <span class="inv inv--low">{{ 'products.product.sold_out' | t | default: 'Sold out' }}</span>
                    {% endif %}
                  </div>
                {% endif %}
                {% endif %}
              {% when 'sales_point' %}
                <div class="sales-point" {{ block.shopify_attributes }}>
                  {% if block.settings.icon %}{% assign i = block.settings.icon %}{{ i | image_url: width: 24 | image_tag: width:24, height:24, alt:'' }}{% endif %}
                  <span>{{ block.settings.text }}</span>
                </div>
              {% when 'discount' %}
                <div class="pdp-discount" {{ block.shopify_attributes }}>
                  {% if block.settings.heading != blank %}<h4>{{ block.settings.heading }}</h4>{% endif %}
                  {% if block.settings.text != blank %}<div class="rte">{{ block.settings.text }}</div>{% endif %}
                  {% if block.settings.code != blank %}
                    <button type="button" class="copy-code" data-code="{{ block.settings.code }}">{{ block.settings.code }}</button>
                  {% endif %}
                </div>
              {% when 'countdown' %}
                {% assign deadline = product.metafields.custom.promo_timer_date %}
                {% if deadline %}
                  <div class="pdp-countdown" data-deadline="{{ deadline }}" style="{% if block.settings.color_text %}--cd-text: {{ block.settings.color_text }};{% endif %}{% if block.settings.color_timer_bg %} --cd-bg: {{ block.settings.color_timer_bg }};{% endif %}{% if block.settings.color_timer_text %} --cd-time: {{ block.settings.color_timer_text }};{% endif %}" {{ block.shopify_attributes }}>
                    {% if block.settings.icon %}{% assign i = block.settings.icon %}{{ i | image_url: width: 24 | image_tag: width:24, height:24, alt:'' }}{% endif %}
                    {% if block.settings.text %}<span class="pdp-countdown__label">{{ block.settings.text }}</span>{% endif %}
                    <span class="pdp-countdown__timer">00:00:00</span>
                  </div>
                {% endif %}
              {% when 'product_combination' %}
                {% assign var_value = product.metafields.custom.variation_value %}
                {% assign var_products = product.metafields.custom.variation_products %}
                {% if var_products %}
                  <div class="pdp-combo" {{ block.shopify_attributes }}>
                    {% if block.settings.option_name %}<div class="pdp-combo__label">{{ block.settings.option_name }}</div>{% endif %}
                    <div class="pdp-combo__list">
                      {% for p in var_products %}
                        <a class="pdp-combo__item{% if p.id == product.id %} is-active{% endif %}" href="{{ p.url }}"{% if p.id == product.id %} aria-current="page"{% endif %}>{{ p.title }}</a>
                      {% endfor %}
                    </div>
                  </div>
                {% endif %}
              {% when 'drawers' %}
                <div class="pdp-drawers" {{ block.shopify_attributes }}>
                  {% if block.settings.include_description and product.description != blank %}
                    <details><summary>{{ 'products.product.description' | t | default: 'Description' }}</summary><div class="rte">{{ product.description }}</div></details>
                  {% endif %}
                  {% if block.settings.page1 %}
                    <details><summary>{{ block.settings.page1_label | default: block.settings.page1.title }}</summary><div class="rte">{{ block.settings.page1.content }}</div></details>
                  {% endif %}
                  {% if block.settings.page2 %}
                    <details><summary>{{ block.settings.page2_label | default: block.settings.page2.title }}</summary><div class="rte">{{ block.settings.page2.content }}</div></details>
                  {% endif %}
                </div>
              {% when 'socials' %}
                <div class="pdp-socials" {{ block.shopify_attributes }}>
                  {% assign share_url = shop.url | append: product.url %}
                  {% if block.settings.show_facebook %}
                    <a class="social social--fb" href="https://www.facebook.com/sharer/sharer.php?u={{ share_url | url_encode }}" target="_blank" rel="noopener" aria-label="Share on Facebook">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M22 12a10 10 0 1 0-11.5 9.9v-7H7.5V12h3V9.8c0-3 1.8-4.7 4.6-4.7 1.3 0 2.6.2 2.6.2v2.9h-1.5c-1.5 0-2 .9-2 1.9V12h3.4l-.5 2.9h-2.9v7A10 10 0 0 0 22 12z"/></svg>
                    </a>
                  {% endif %}
                  {% if block.settings.show_twitter %}
                    <a class="social social--x" href="https://twitter.com/intent/tweet?url={{ share_url | url_encode }}&text={{ product.title | url_encode }}" target="_blank" rel="noopener" aria-label="Share on X">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M3 3h4.6l5.1 6.8L17.9 3H21l-7 9.4L21 21h-4.6l-5.4-7.1L6.1 21H3l7.5-10L3 3z"/></svg>
                    </a>
                  {% endif %}
                  {% if block.settings.show_pinterest %}
                    {% assign img = product.featured_media | default: product.media.first %}
                    <a class="social social--pt" href="https://pinterest.com/pin/create/button/?url={{ share_url | url_encode }}&media={{ img | image_url: width: 800 | url_encode }}&description={{ product.title | url_encode }}" target="_blank" rel="noopener" aria-label="Share on Pinterest">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M12 2a9.9 9.9 0 0 0-3.6 19.2c-.1-1-.2-2.6 0-3.7l1.2-5c-.3-.6-.4-1.3-.4-2 0-1.8 1-3.1 2.2-3.1 1 0 1.4.8 1.4 1.7 0 1-.6 2.5-.9 3.8-.3 1.1.6 2.1 1.7 2.1 2 0 3.5-2.1 3.5-5.2 0-2.7-1.9-4.6-4.7-4.6-3.2 0-5.1 2.4-5.1 4.8 0 1 .4 2 .9 *******.1.2.1.3l-.3 1.2c0 .2-.1.3-.3.2-1-.4-1.7-1.6-1.7-3.1 0-2.2 1.8-4.9 5.4-4.9 2.9 0 4.9 2.1 4.9 4.9 0 3.1-1.7 5.5-4.2 5.5-1 0-1.9-.6-2.2-1.3l-.6 2.3c-.2.8-.8 1.7-1.1 ******* 1.7.3 2.6.3A9.9 9.9 0 0 0 12 2z"/></svg>
                    </a>
                  {% endif %}
                </div>
              {% when 'notify_me' %}
                {% unless product.selected_or_first_available_variant.available %}
                  <button type="button" class="notify-btn" data-open-notify {{ block.shopify_attributes }}>{{ block.settings.button_label | default: 'Notify me' }}</button>
                  <dialog id="Notify-{{ section.id }}" class="notify-dialog" hidden>
                    <form method="dialog"><button data-close aria-label="Close">×</button></form>
                    <form action="/contact#contact_form" method="post" class="notify-form">
                      <h3>{{ block.settings.heading | default: 'Back in stock' }}</h3>
                      <label>{{ block.settings.email_label | default: 'Email' }} <input type="email" name="contact[email]" required></label>
                      <input type="hidden" name="contact[tags]" value="notify_me">
                      <input type="hidden" name="contact[body]" value="{{ 'products.product.notify_me_message' | t: product_title: product.title | default: 'Notify me about: ' | append: product.title }}">
                      <button type="submit" class="button">{{ block.settings.submit_label | default: 'Send' }}</button>
                    </form>
                  </dialog>
                {% endunless %}
              {% when 'ask_question' %}
                <button type="button" class="ask-btn" data-open-ask {{ block.shopify_attributes }}>{{ block.settings.button_label | default: 'Ask a question' }}</button>
                <dialog id="Ask-{{ section.id }}" class="ask-dialog" hidden>
                  <form method="dialog"><button data-close aria-label="Close">×</button></form>
                  <form action="/contact#contact_form" method="post" class="ask-form">
                    <h3>{{ block.settings.heading | default: 'Question about this product' }}</h3>
                    <label>{{ block.settings.email_label | default: 'Email' }} <input type="email" name="contact[email]" required></label>
                    <label>{{ block.settings.name_label | default: 'Name' }} <input type="text" name="contact[name]" required></label>
                    {% if block.settings.phone_label != blank %}<label>{{ block.settings.phone_label }} <input type="tel" name="contact[phone]"></label>{% endif %}
                    <input type="hidden" name="contact[body]" value="{{ product.title }} — {{ request.host }}{{ product.url }}">
                    <label>{{ block.settings.message_label | default: 'Message' }} <textarea name="contact[message]"></textarea></label>
                    <button type="submit" class="button">{{ block.settings.submit_label | default: 'Send' }}</button>
                  </form>
                </dialog>
              {% when 'pickup_availability' %}
                <div class="pdp-pickup" data-pickup-block {{ block.shopify_attributes }}>
                  {% assign current_vid = product.selected_or_first_available_variant.id %}
                  {% for v in product.variants %}
                    {% assign avails = v.store_availabilities | where: 'available', true %}
                    <div id="PickupFor-{{ v.id }}" class="pickup__variant" {% unless v.id == current_vid %}hidden{% endunless %}>
                      {% if avails.size > 0 %}
                        <div class="pickup__header">{{ 'products.product.pickup_availability' | t | default: 'Pickup availability' }}</div>
                        <ul class="pickup__list">
                          {% for a in avails limit: 3 %}
                            <li class="pickup__item">
                              <span class="pickup__loc">{{ a.location.name }}</span>
                              <span class="pickup__eta">{{ a.pick_up_time | default: 'Ready in 24 hours' }}</span>
                            </li>
                          {% endfor %}
                        </ul>
                      {% else %}
                        <div class="pickup__unavailable">{{ 'products.product.pickup_unavailable' | t | default: 'Currently unavailable for pickup' }}</div>
                      {% endif %}
                    </div>
                  {% endfor %}
                </div>
              {% when 'complementary_products' %}
                <div class="pdp-complementary" data-complementary data-product-id="{{ product.id }}" data-limit="4" {{ block.shopify_attributes }}>
                  <div class="pdp-complementary__inner" aria-live="polite"></div>
                </div>
              {% when 'custom_liquid' %}
                {% if block.settings.custom_liquid %}<div {{ block.shopify_attributes }}>{{ block.settings.custom_liquid }}</div>{% endif %}
              {% when '@app' %}
                {%- comment -%} App blocks render automatically {%- endcomment -%}
            {% endcase %}
          {% endfor %}

          {%- comment -%} Section-level info not tied to blocks {%- endcomment -%}
          {%- if section.settings.show_points_program -%}
            <div class="points-program">
              <span class="points-icon">★</span>
              <span class="points-text">{{ section.settings.points_text }}</span>
            </div>
          {%- endif -%}
          {%- if section.settings.show_shipping_info -%}
            <div class="shipping-info">
              <div class="shipping-item">
                <span class="shipping-icon">📦</span>
                <span class="shipping-text">{{ 'sections.product.free_standard_over' | t: threshold: section.settings.free_standard_threshold | default: 'Free standard shipping over ' | append: section.settings.free_standard_threshold }}</span>
              </div>
              <div class="shipping-item">
                <span class="shipping-icon">⚡</span>
                <span class="shipping-text">Free express shipping over {{ section.settings.free_express_threshold }}</span>
              </div>
              {%- if section.settings.show_easy_returns -%}
                <div class="shipping-item">
                  <span class="shipping-icon">↩</span>
                  <span class="shipping-text">{{ 'sections.product.easy_returns' | t | default: 'Easy returns' }}</span>
                </div>
              {%- endif -%}
            </div>
          {%- endif -%}

          {%- comment -%} Collapsible sections (always available via section settings) {%- endcomment -%}
          <div class="product-details-accordion">
            {%- if section.settings.show_product_details -%}
              <details class="product-detail-section">
                <summary class="detail-header">
                  <span>{{ 'products.product.details' | t | default: 'Product Details' }}</span>
                  <span class="detail-toggle">▼</span>
                </summary>
                <div class="detail-content">
                  {%- if section.settings.product_details_content != blank -%}
                    {{ section.settings.product_details_content }}
                  {%- elsif product.description != blank -%}
                    {{ product.description }}
                  {%- else -%}
                    <p>Product details will be displayed here.</p>
                  {%- endif -%}
                </div>
              </details>
            {%- endif -%}

            {%- if section.settings.show_size_fit -%}
              <details class="product-detail-section">
                <summary class="detail-header">
                  <span>{{ 'products.product.size_fit' | t | default: 'Size & Fit' }}</span>
                  <span class="detail-toggle">▼</span>
                </summary>
                <div class="detail-content">
                  {%- if section.settings.size_fit_content != blank -%}
                    {{ section.settings.size_fit_content }}
                  {%- else -%}
                    <p>Size and fit information will be displayed here.</p>
                  {%- endif -%}
                </div>
              </details>
            {%- endif -%}

            {%- if section.settings.show_material_care -%}
              <details class="product-detail-section">
                <summary class="detail-header">
                  <span>{{ 'products.product.material_care' | t | default: 'Material & Care' }}</span>
                  <span class="detail-toggle">▼</span>
                </summary>
                <div class="detail-content">
                  {%- if section.settings.material_care_content != blank -%}
                    {{ section.settings.material_care_content }}
                  {%- else -%}
                    <p>Material and care instructions will be displayed here.</p>
                  {%- endif -%}
                </div>
              </details>
            {%- endif -%}

            {%- if section.settings.show_delivery_returns -%}
              <details class="product-detail-section">
                <summary class="detail-header">
                  <span>{{ 'products.product.delivery_returns' | t | default: 'Delivery & Returns' }}</span>
                  <span class="detail-toggle">▼</span>
                </summary>
                <div class="detail-content">
                  {%- if section.settings.delivery_returns_content != blank -%}
                    {{ section.settings.delivery_returns_content }}
                  {%- else -%}
                    <p>Delivery and returns information will be displayed here.</p>
                  {%- endif -%}
                </div>
              </details>
            {%- endif -%}

            {%- if section.settings.show_model_info -%}
              <details class="product-detail-section">
                <summary class="detail-header">
                  <span>Model Info</span>
                  <span class="detail-toggle">▼</span>
                </summary>
                <div class="detail-content">
                  {%- if section.settings.model_info_content != blank -%}
                    {{ section.settings.model_info_content }}
                  {%- else -%}
                    <p>Model information will be displayed here.</p>
                  {%- endif -%}
                </div>
              </details>
            {%- endif -%}
          </div>
        {% else %}
        {%- if section.settings.show_new_badge -%}
          <div class="product-badge">{{ 'products.product.badge_new' | t | default: 'NEW' }}</div>
        {%- endif -%}

        <h1 class="product-title">{{ product.title | escape }}</h1>

        {%- if product.selected_or_first_available_variant.sku != blank -%}
          <div class="product-color">
            <span class="color-label">{{ 'products.product.color' | t | default: 'Color' }}:</span>
            <span class="color-value">{{ product.selected_or_first_available_variant.option1 | default: 'Dusty Purple' }}</span>
          </div>
        {%- endif -%}

        <!-- Stock Status and Points -->
        <div class="product-status">
          {%- if section.settings.show_stock_status -%}
            <div class="stock-status">
              {%- if product.selected_or_first_available_variant.available -%}
                <span class="stock-indicator in-stock"></span>
                <span class="stock-text">{{ 'products.product.in_stock' | t | default: 'In Stock' }}</span>
              {%- else -%}
                <span class="stock-indicator out-of-stock"></span>
                <span class="stock-text">{{ 'products.product.out_of_stock' | t | default: 'Out of Stock' }}</span>
              {%- endif -%}
            </div>
          {%- endif -%}

          {%- if section.settings.show_points_program -%}
            <div class="points-program">
              <span class="points-icon">★</span>
              <span class="points-text">{{ section.settings.points_text }}</span>
            </div>
          {%- endif -%}
        </div>

        <!-- Price Display -->
        <div class="product-price">
          <span class="price" id="productPrice">{{ product.selected_or_first_available_variant.price | money }}</span>
          {%- if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
            <span class="compare-price">{{ product.selected_or_first_available_variant.compare_at_price | money }}</span>
          {%- endif -%}
        </div>
        {%- if section.settings.show_taxes_included_message and shop.taxes_included -%}
          <div class="pricing-notice">
            <p>{{ 'products.product.include_taxes' | t | default: 'Taxes included.' }}</p>
          </div>
        {%- endif -%}

        <!-- Pricing Notice -->
        <div class="pricing-notice">
          <p>All prices are now inclusive of all Canadian customs duties and tariffs</p>
          <a href="#" class="learn-more-link">{{ 'general.learn_more' | t | default: 'Learn more' }}</a>
        </div>

          <!-- Product Form -->
          {% form 'product', product, class: 'product-form', id: 'product-form' %}
          {%- for option in product.options_with_values -%}
            {%- liquid
              assign option_name = option.name | downcase
              assign is_color = false
              if option_name contains 'color' or option_name contains 'colour'
                assign is_color = true
              endif
            -%}
            
            <div class="variant-group">
              <div class="variant-header">
                <label class="variant-label">{{ option.name }}</label>
                {%- assign on2 = option.name | downcase -%}
                {%- if on2 contains 'size' and section.settings.show_size_guide -%}
                  <button type="button" class="size-guide-link" data-open-size-guide aria-label="Size guide">{{ 'products.product.size_guide' | t | default: 'Size Guide' }}</button>
                {%- endif -%}
              </div>
              
              {%- if is_color -%}
                <div class="color-swatches">
                  {%- for value in option.values -%}
                    <input 
                      type="radio" 
                      name="options[{{ option.name | escape }}]" 
                      value="{{ value | escape }}"
                      id="option-{{ option.position }}-{{ forloop.index0 }}"
                      class="variant-input"
                      {% if option.selected_value == value %}checked{% endif %}
                      data-option-position="{{ option.position }}"
                    >
                    <label 
                      for="option-{{ option.position }}-{{ forloop.index0 }}"
                      class="color-swatch swatch--{{ value | handle }}{% if option.selected_value == value %} selected{% endif %}"
                      style="background-color: {{ value | handle | replace: '-', '' }};"
                      title="{{ value | escape }}"
                    ></label>
                  {%- endfor -%}
                </div>
              {%- else -%}
                <div class="size-options">
                  {%- for value in option.values -%}
                    <input 
                      type="radio" 
                      name="options[{{ option.name | escape }}]" 
                      value="{{ value | escape }}"
                      id="option-{{ option.position }}-{{ forloop.index0 }}"
                      class="variant-input"
                      {% if option.selected_value == value %}checked{% endif %}
                      data-option-position="{{ option.position }}"
                    >
                    <label 
                      for="option-{{ option.position }}-{{ forloop.index0 }}"
                      class="size-option{% if option.selected_value == value %} selected{% endif %}"
                    >
                      {{ value | escape }}
                    </label>
                  {%- endfor -%}
                </div>
              {%- endif -%}
            </div>
          {%- endfor -%}

          {%- if section.settings.show_size_guide -%}
            <div class="size-guide size-guide--{{ section.settings.size_guide_button_placement | default: 'under_options' }}">
              <button type="button" class="size-guide__btn" data-open-size-guide>
                {%- if section.settings.size_guide_button_icon -%}
                  {%- assign ic = section.settings.size_guide_button_icon -%}
                  {{ ic | image_url: width: 24 | image_tag: loading: 'lazy', decoding: 'async', width: 24, height: 24, alt: ic.alt | default: '' }}
                {%- endif -%}
                <span>{{ section.settings.size_guide_button_label | default: 'Size guide' }}</span>
              </button>
            </div>
          {%- endif -%}

            <!-- Add to Cart Section -->
            <div class="add-to-cart-section">
              <div class="atc-row">
                <div class="atc-combo"{% if section.settings.show_inline_qty %} data-qty-hover="true"{% endif %}>
                  <button
                  type="submit"
                  name="add"
                  class="add-to-cart-btn"
                  id="addToCartBtn"
                  {% unless product.selected_or_first_available_variant.available %}disabled{% endunless %}
                >
                  <span class="atc-text">
                    {%- if product.selected_or_first_available_variant.available -%}
                    {{ 'products.product.add_to_cart' | t | default: 'Add to cart' }}
                    {%- else -%}
                    {{ 'products.product.sold_out' | t | default: 'Sold out' }}
                    {%- endif -%}
                  </span>
                  </button>
                  {% if section.settings.show_inline_qty %}
                  <div class="inbtn-qty-overlay" role="group" aria-label="{{ 'products.product.quantity' | t | default: 'Quantity' }}">
                    <span class="inbtn-qty__label">QTY</span>
                    <input id="QtyOverlay-{{ section.id }}-alt" class="inbtn-qty__input" type="number" min="1" inputmode="numeric" data-qty-input>
                  </div>
                  {% endif %}
                </div>
              </div>

              {%- if section.settings.show_wishlist_btn -%}
              <button type="button" class="wishlist-btn" id="addToWishlist">
                <span class="wishlist-icon">♡</span>
                {%- liquid
                  assign wl = section.settings.wishlist_button_label | strip
                  if wl == blank
                    assign wl = 'products.product.add_to_wishlist' | t | default: 'Add to wishlist'
                  endif
                -%}
                {{ wl }}
              </button>
              {%- endif -%}
              {% if section.settings.show_pay_installments %}
                <div class="pdp-payment-terms">{{ form | payment_terms }}</div>
              {% endif %}
            </div>

          <!-- Hidden variant input -->
          <input 
            type="hidden" 
            name="id" 
            value="{{ product.selected_or_first_available_variant.id }}"
            id="variantId"
          >
        {% endform %}

        <!-- Shipping Information -->
        {%- if section.settings.show_shipping_info -%}
          <div class="shipping-info">
            <div class="shipping-item">
              <span class="shipping-icon">📦</span>
              <span class="shipping-text">{{ 'sections.product.free_standard_over' | t: threshold: section.settings.free_standard_threshold | default: 'Free standard shipping over ' | append: section.settings.free_standard_threshold }}</span>
            </div>
            <div class="shipping-item">
              <span class="shipping-icon">⚡</span>
              <span class="shipping-text">Free express shipping over {{ section.settings.free_express_threshold }}</span>
            </div>
            {%- if section.settings.show_easy_returns -%}
              <div class="shipping-item">
                <span class="shipping-icon">↩</span>
                <span class="shipping-text">{{ 'sections.product.easy_returns' | t | default: 'Easy returns' }}</span>
              </div>
            {%- endif -%}
          </div>
        {%- endif -%}

        <!-- Collapsible Product Information Sections -->
        <div class="product-details-accordion">
          {%- if section.settings.show_product_details -%}
            <details class="product-detail-section">
              <summary class="detail-header">
                <span>{{ 'products.product.details' | t | default: 'Product Details' }}</span>
                <span class="detail-toggle">▼</span>
              </summary>
              <div class="detail-content">
                {%- if section.settings.product_details_content != blank -%}
                  {{ section.settings.product_details_content }}
                {%- elsif product.description != blank -%}
                  {{ product.description }}
                {%- else -%}
                  <p>Product details will be displayed here.</p>
                {%- endif -%}
              </div>
            </details>
          {%- endif -%}

          {%- if section.settings.show_size_fit -%}
            <details class="product-detail-section">
              <summary class="detail-header">
                <span>{{ 'products.product.size_fit' | t | default: 'Size & Fit' }}</span>
                <span class="detail-toggle">▼</span>
              </summary>
              <div class="detail-content">
                {%- if section.settings.size_fit_content != blank -%}
                  {{ section.settings.size_fit_content }}
                {%- else -%}
                  <p>Size and fit information will be displayed here.</p>
                {%- endif -%}
              </div>
            </details>
          {%- endif -%}

          {%- if section.settings.show_material_care -%}
            <details class="product-detail-section">
              <summary class="detail-header">
                <span>{{ 'products.product.material_care' | t | default: 'Material & Care' }}</span>
                <span class="detail-toggle">▼</span>
              </summary>
              <div class="detail-content">
                {%- if section.settings.material_care_content != blank -%}
                  {{ section.settings.material_care_content }}
                {%- else -%}
                  <p>Material and care instructions will be displayed here.</p>
                {%- endif -%}
              </div>
            </details>
          {%- endif -%}

          {%- if section.settings.show_delivery_returns -%}
            <details class="product-detail-section">
              <summary class="detail-header">
                <span>{{ 'products.product.delivery_returns' | t | default: 'Delivery & Returns' }}</span>
                <span class="detail-toggle">▼</span>
              </summary>
              <div class="detail-content">
                {%- if section.settings.delivery_returns_content != blank -%}
                  {{ section.settings.delivery_returns_content }}
                {%- else -%}
                  <p>Delivery and returns information will be displayed here.</p>
                {%- endif -%}
              </div>
            </details>
          {%- endif -%}

          {%- if section.settings.show_model_info -%}
            <details class="product-detail-section">
              <summary class="detail-header">
                <span>Model Info</span>
                <span class="detail-toggle">▼</span>
              </summary>
              <div class="detail-content">
                {%- if section.settings.model_info_content != blank -%}
                  {{ section.settings.model_info_content }}
                {%- else -%}
                  <p>Model information will be displayed here.</p>
                {%- endif -%}
              </div>
            </details>
          {%- endif -%}
        {% endif %}
  </div>
  </div>
  </div>
{% endcapture %}
{% unless section.settings.layout_style == 'stack' %}{{ product_info_html }}{% endunless %}
  </div>
{% if section.settings.layout_style == 'stack' %}{{ product_info_html }}{% endif %}
</div>

{%- if section.settings.show_size_guide -%}
  <dialog id="SizeGuide-{{ section.id }}" class="size-guide-dialog" hidden>
    <form method="dialog">
      <button class="size-guide__close" data-close aria-label="Close">×</button>
    </form>
    <div class="size-guide__inner">
      <h3 class="size-guide__title">{{ section.settings.size_guide_drawer_heading | default: 'Size guide' }}</h3>
      {%- assign sg_page = product.metafields.size_guide.page -%}
      {%- assign model_img = product.metafields.custom.size_guide_model_image -%}
      {%- assign model_info = product.metafields.custom.size_guide_model_info -%}
      {%- assign has_model_tab = false -%}
      {%- if model_img != blank or model_info != blank -%}
        {%- assign has_model_tab = true -%}
      {%- endif -%}
      {%- if has_model_tab -%}
        <div class="size-guide__tabs">
          <button type="button" class="sg-tab sg-tab--active" data-sg-tab="general">{{ section.settings.size_guide_tab_general_label | default: 'General' }}</button>
          <button type="button" class="sg-tab" data-sg-tab="model">{{ section.settings.size_guide_tab_model_label | default: 'Model' }}</button>
        </div>
      {%- endif -%}
      <div class="size-guide__panes">
        <div class="sg-pane sg-pane--general" {% if has_model_tab %}style="display:block"{% endif %}>
          {%- if sg_page and sg_page.content != blank -%}
            {{ sg_page.content }}
          {%- else -%}
            <p style="opacity:.7">Assign a page to product metafield size_guide.page</p>
          {%- endif -%}
        </div>
        {%- if has_model_tab -%}
        <div class="sg-pane sg-pane--model" style="display:none">
          {%- if model_img -%}
            {{ model_img | image_url: width: 600 | image_tag: loading: 'lazy', decoding: 'async', alt: product.title }}
          {%- endif -%}
          {%- if model_info != blank -%}
            <div class="rte">{{ model_info }}</div>
          {%- endif -%}
        </div>
        {%- endif -%}
      </div>
    </div>
    <script>
      (function(){
        const dlg = document.getElementById('SizeGuide-{{ section.id }}');
        if (!dlg) return;
        dlg.querySelectorAll('.sg-tab').forEach(btn => {
          btn.addEventListener('click', () => {
            dlg.querySelectorAll('.sg-tab').forEach(b => b.classList.toggle('sg-tab--active', b === btn));
            const tab = btn.getAttribute('data-sg-tab');
            dlg.querySelector('.sg-pane--general')?.style && (dlg.querySelector('.sg-pane--general').style.display = tab==='general' ? 'block' : 'none');
            dlg.querySelector('.sg-pane--model')?.style && (dlg.querySelector('.sg-pane--model').style.display = tab==='model' ? 'block' : 'none');
          });
        });
      })();
    </script>
    {% style %}
      /* Lightbox-style Size Guide */
      .size-guide-dialog {
        border: none;
        border-radius: 10px;
        background: #fff; /* ensure true white panel */
        color: rgb(var(--color-foreground));
        padding: 0;
        width: min(720px, 96vw);
        max-width: 96vw;
        max-height: min(85vh, 100vh);
        box-shadow: 0 20px 60px rgba(0,0,0,.4);
        position: fixed; /* reliable centering across browsers */
        top: 50%; left: 50%; transform: translate(-50%, -50%);
        overflow: hidden; /* contain inner scroll */
        z-index: 1000;
      }
      .size-guide-dialog::backdrop { background: rgba(0,0,0,.7); }

      .size-guide__inner { background: #fff; padding: 1rem 1.25rem; max-height: min(80vh, calc(100vh - 40px)); overflow: auto; }
      .size-guide__close { position: absolute; top: .5rem; right: .5rem; background: transparent; border: none; font-size: 1.25rem; cursor: pointer; }
      .size-guide__tabs { display: flex; gap: .5rem; margin: .5rem 0 1rem; }
      .sg-tab { padding: .4rem .8rem; border-radius: 999px; border:1px solid #ddd; background: #f7f7f7; cursor: pointer; }
      .sg-tab--active { background: #111; color: #fff; border-color: #111; }
      .size-guide__panes img{ max-width: 100%; height: auto; display: block; }
    {% endstyle %}
  </dialog>
  <div id="SizeGuideDrawer-{{ section.id }}" class="size-guide-drawer" hidden>
    <button class="size-guide__drawer-close" data-close aria-label="Close">×</button>
    <div class="size-guide__inner">
      <h3 class="size-guide__title">{{ section.settings.size_guide_drawer_heading | default: 'Size guide' }}</h3>
      {%- assign sg_page = product.metafields.size_guide.page -%}
      {%- assign model_img = product.metafields.custom.size_guide_model_image -%}
      {%- assign model_info = product.metafields.custom.size_guide_model_info -%}
      {%- assign has_model_tab = false -%}
      {%- if model_img != blank or model_info != blank -%}{%- assign has_model_tab = true -%}{%- endif -%}
      <div class="size-guide__content">
        {%- if sg_page and sg_page.content != blank -%}
          {{ sg_page.content }}
        {%- else -%}
          <p style="opacity:.7">Assign a page to product metafield size_guide.page</p>
        {%- endif -%}
        {%- if has_model_tab -%}
          <hr>
          {%- if model_img -%}{{ model_img | image_url: width: 800 | image_tag: loading: 'lazy', decoding: 'async', alt: product.title }}{%- endif -%}
          {%- if model_info != blank -%}<div class="rte">{{ model_info }}</div>{%- endif -%}
        {%- endif -%}
      </div>
    </div>
    {% style %}
      .size-guide-drawer{ position: fixed; top:0; right:0; bottom:0; width: min(520px, 90vw); background: #fff; color: rgb(var(--color-foreground)); box-shadow: -20px 0 40px rgba(0,0,0,.25); transform: translateX(100%); transition: transform .3s ease; z-index: 50; padding: 1rem; overflow:auto; }
      .size-guide-drawer[open]{ transform: translateX(0%); }
      .size-guide__drawer-close{ position: absolute; top:.5rem; left:.5rem; background: transparent; border:none; font-size: 1.25rem; }
    {% endstyle %}
  </div>
{%- endif -%}

<!-- Media lightbox for click-to-zoom -->
<dialog id="MediaLightbox-{{ section.id }}" class="media-lightbox" hidden>
  <form method="dialog"><button class="media-lightbox__close" data-close aria-label="Close">×</button></form>
  <img alt="" loading="eager">
  {% style %}
    .media-lightbox{ border:none; padding:0; max-width: 96vw; max-height: 96vh; background: transparent; }
    .media-lightbox::backdrop{ background: rgba(0,0,0,.6); }
    .media-lightbox img{ display:block; max-width: 96vw; max-height: 96vh; width: auto; height: auto; }
    .media-lightbox__close{ position:absolute; top:.25rem; right:.5rem; background: rgba(0,0,0,.6); color:#fff; border:none; width:32px; height:32px; border-radius:16px; cursor:pointer; }
  {% endstyle %}
</dialog>

{%- if section.settings.enable_sticky_atc -%}
  <div class="sticky-atc" data-sticky-atc hidden>
    <div class="sticky-atc__inner">
      <div class="sticky-atc__info">
        <span class="sticky-atc__title">{{ product.title | escape }}</span>
        <span class="sticky-atc__price">{{ product.selected_or_first_available_variant.price | money }}</span>
      </div>
      <button type="button" class="sticky-atc__btn" data-sticky-atc-btn>
        {{ 'products.product.add_to_cart' | t | default: 'Add to cart' }}
      </button>
    </div>
  </div>
  {% style %}
    .sticky-atc{ position: fixed; left: 0; right: 0; bottom: 0; z-index: 40; background: rgb(var(--color-background)); border-top: 1px solid #e5e5e5; box-shadow: 0 -8px 20px rgba(0,0,0,.06); }
    .sticky-atc[hidden]{ display:none !important; }
    .sticky-atc__inner{ max-width: var(--container-max, var(--page-width)); margin: 0 auto; padding: .75rem 1rem; display:flex; align-items:center; justify-content: space-between; gap: .75rem; }
    .sticky-atc__btn{ padding: .6rem 1rem; border-radius: 999px; background: rgb(var(--color-foreground)); color: rgb(var(--color-background)); border: 1px solid transparent; cursor: pointer; }
    .sticky-atc__title{ font-size: .95rem; }
    .sticky-atc__price{ font-weight: 600; margin-left: .5rem; }
  {% endstyle %}
  <script>
    (function(){
      const bar = document.querySelector('[data-sticky-atc]');
      const btn = document.querySelector('#addToCartBtn');
      const trigger = document.querySelector('[data-sticky-atc-btn]');
      if (!bar || !btn || !('IntersectionObserver' in window)) return;
      const io = new IntersectionObserver(entries => {
        entries.forEach(en => {
          if (en.isIntersecting) { bar.setAttribute('hidden',''); }
          else { bar.removeAttribute('hidden'); }
        });
      }, { root: null, threshold: 0 });
      io.observe(btn);
      trigger?.addEventListener('click', () => btn.click());
    })();
  </script>
{%- endif -%}

<script>
  document.addEventListener('DOMContentLoaded', function() {
  // Image Slider with Dot Navigation (scoped to this section)
  const sectionRoot = document.getElementById('product-{{ section.id }}');
  if (!sectionRoot) return;

  const slider = sectionRoot.querySelector('#productSlider') || sectionRoot.querySelector('.product-slider');
  if (!slider) return;

  const sliderTrack = slider.querySelector('#sliderTrack') || slider.querySelector('.slider-track');
  const dots = slider.querySelectorAll('.dot');
  const images = slider.querySelectorAll('.slider-image');
  if (!images.length) return;
  const isScrollAnimation = slider.classList.contains('scroll-animation');
  const isSwipeAnimation = slider.classList.contains('swipe-animation');
  const isRunway = slider.classList.contains('runway');
  let currentSlide = 0;
  let isScrolling = false;
  let lastScrollY = window.scrollY;

  // Sync UI helpers (dots + thumbnails) to an active index
  const thumbs = sectionRoot ? sectionRoot.querySelectorAll('.product-thumb[data-thumb-index]') : [];
  function updateActive(index) {
    // Clamp index
    const idx = Math.max(0, Math.min(images.length - 1, index));
    // Dots
    dots.forEach((d, i) => {
      if (!d) return;
      d.classList.toggle('active', i === idx);
      if (i === idx) d.setAttribute('aria-current', 'true'); else d.removeAttribute('aria-current');
    });
    // Thumbnails
    thumbs.forEach((t, i) => {
      if (!t) return;
      t.classList.toggle('is-active', i === idx);
      if (i === idx) t.setAttribute('aria-current', 'true'); else t.removeAttribute('aria-current');
    });
    currentSlide = idx;
  }

  // Runway layout: enable desktop drag-to-scroll and ensure the
  // right info panel does not cover the footer by pinning it to the
  // section bottom when reached.
  if (isRunway) {
    // Guard: don't double-bind if script re-runs
    if (!slider.hasAttribute('data-runway-init')) {
      slider.setAttribute('data-runway-init', '');
      // 1) Drag-to-scroll for mouse users
      let isDown = false;
      let startX = 0;
      let startScrollLeft = 0;
      const onMouseDown = (e) => {
        isDown = true;
        startX = e.clientX;
        startScrollLeft = slider.scrollLeft;
        sliderTrack?.classList.add('dragging');
      };
      const onMouseMove = (e) => {
        if (!isDown) return;
        e.preventDefault();
        const dx = e.clientX - startX;
        slider.scrollLeft = startScrollLeft - dx;
      };
      const onMouseUp = () => {
        if (!isDown) return;
        isDown = false;
        sliderTrack?.classList.remove('dragging');
      };
      // Mouse events (fallback only when PointerEvent is unavailable)
      if (!('PointerEvent' in window)) {
        slider.addEventListener('mousedown', onMouseDown);
        if (sliderTrack) sliderTrack.addEventListener('mousedown', onMouseDown);
        window.addEventListener('mousemove', onMouseMove, { passive: false });
        window.addEventListener('mouseup', onMouseUp);
      }
      slider.addEventListener('mouseleave', onMouseUp);
    }

    // Ensure pointer-based drag is bound even if previous init ran before
    if (!slider.hasAttribute('data-runway-pointer')) {
      slider.setAttribute('data-runway-pointer', '');
      let pdDown = false; let pdStartX = 0; let pdStartLeft = 0; let pdId = null; let pdType = 'mouse';
      let lastT = 0; let lastScroll = 0; let vel = 0; let momentumRAF = null;
      const stopMomentum = () => { if (momentumRAF) { cancelAnimationFrame(momentumRAF); momentumRAF = null; } };
      const startMomentum = () => {
        const maxScroll = Math.max(0, slider.scrollWidth - slider.clientWidth);
        const FRICTION = 0.92;           // per 16ms frame
        const VEL_EPS = 0.01;            // px/ms cutoff
        let prev = performance.now();
        const step = (now) => {
          const dt = Math.min(40, Math.max(0, now - prev)); // clamp dt to avoid spikes
          prev = now;
          // advance by velocity
          slider.scrollLeft = Math.min(maxScroll, Math.max(0, slider.scrollLeft + vel * dt));
          // apply friction decay based on dt (normalize to ~60fps)
          vel *= Math.pow(FRICTION, dt / 16);
          // stop when slow or at bounds
          if (Math.abs(vel) < VEL_EPS || slider.scrollLeft <= 0 || slider.scrollLeft >= maxScroll) {
            momentumRAF = null; return;
          }
          momentumRAF = requestAnimationFrame(step);
        };
        momentumRAF = requestAnimationFrame(step);
      };
      const onPDDown = (e) => {
        // Only handle mouse here; let native touch scrolling handle inertia on mobile
        if (e.pointerType && e.pointerType !== 'mouse') return;
        if (e.button != null && e.button !== 0) return;
        stopMomentum();
        pdDown = true;
        pdType = e.pointerType || 'mouse';
        pdId = e.pointerId;
        pdStartX = e.clientX;
        pdStartLeft = slider.scrollLeft;
        sliderTrack?.classList.add('dragging');
        slider.style.scrollBehavior = 'auto';
        try { slider.setPointerCapture?.(e.pointerId); } catch(_e){}
        e.preventDefault();
        lastT = performance.now();
        lastScroll = slider.scrollLeft;
        vel = 0;
      };
      const onPDMove = (e) => {
        if (!pdDown) return;
        e.preventDefault();
        const dx = e.clientX - pdStartX;
        slider.scrollLeft = pdStartLeft - dx;
        const now = performance.now();
        const dt = Math.min(40, Math.max(1, now - lastT));
        // instantaneous velocity in px/ms
        const inst = (slider.scrollLeft - lastScroll) / dt;
        // smooth velocity (EMA)
        vel = vel * 0.75 + inst * 0.25;
        // cap extreme velocity
        const MAX_V = 2.5; // px/ms
        if (vel > MAX_V) vel = MAX_V; else if (vel < -MAX_V) vel = -MAX_V;
        lastT = now;
        lastScroll = slider.scrollLeft;
      };
      const onPDUp = (e) => {
        if (!pdDown) return;
        pdDown = false;
        sliderTrack?.classList.remove('dragging');
        slider.style.scrollBehavior = '';
        try { if (pdId != null) slider.releasePointerCapture?.(pdId); } catch(_e){}
        // Only simulate momentum for mouse interactions
        if ((pdType === 'mouse') && Math.abs(vel) > 0.05) {
          startMomentum();
        } else {
          stopMomentum();
        }
      };
      // Pointer events for smooth drag
      slider.addEventListener('pointerdown', onPDDown);
      slider.addEventListener('pointermove', onPDMove, { passive: false });
      window.addEventListener('pointerup', onPDUp);
      slider.addEventListener('pointercancel', onPDUp);
      slider.addEventListener('dragstart', (e)=> e.preventDefault());
    }

    // 2) Floating info panel: compute release point and toggle fixed/released states
    const infoWrap = sectionRoot ? sectionRoot.querySelector('.product__info') : null;
    if (infoWrap) {
      // Desktop only for Runway behavior
      if (window.innerWidth < 990) {
        // Ensure default (non-fixed) state on mobile
        infoWrap.classList.remove('is-fixed');
        infoWrap.classList.remove('is-released');
        return;
      }
      // Ensure section root is the containing block for absolute positioning
      sectionRoot.style.position = sectionRoot.style.position || 'relative';
      // If the info panel lives inside the grid, move it to be a direct child of the section root
      if (infoWrap.parentElement && infoWrap.parentElement !== sectionRoot) {
        try { sectionRoot.appendChild(infoWrap); } catch(_e){}
      }
      const getVarPx = (el, name) => {
        const v = getComputedStyle(el).getPropertyValue(name);
        const n = parseFloat(v);
        return Number.isFinite(n) ? n : 0;
      };

      const getHeaderH = () => getVarPx(document.documentElement, '--header-h');

      const getSliderH = () => {
        // Mirrors CSS var: 100vh/dvh - header - fold
        const vh = (window.visualViewport && typeof visualViewport.height === 'number')
          ? visualViewport.height
          : window.innerHeight;
        return Math.max(200, Math.floor(vh - getHeaderH() - getVarPx(sectionRoot, '--fold-buffer')));
      };

      // Find the element that marks where the rail should stop sticking
      const findReleaseAnchor = () => {
        // 1) explicit merchant marker
        const marked = document.querySelector('[data-rail-end]');
        if (marked) return marked;

        // 2) heuristic: look for common "mates" after the product section
        const thisShopifySection = sectionRoot.closest('.shopify-section');
        let next = thisShopifySection ? thisShopifySection.nextElementSibling : null;
        // Skip non-section siblings
        while (next && !next.classList?.contains('shopify-section')) next = next.nextElementSibling;

        // Try to expand to include a couple of likely follow-ons
        const candidates = [];
        let cursor = next;
        const MAX_CHECK = 6;
        let checks = 0;

        while (cursor && checks < MAX_CHECK) {
          candidates.push(cursor);
          // stop early if we spot common rail mates
          if (cursor.querySelector('.product-recs, .product-recs__inner, .shop-the-look, .keep-exploring, [data-rail-mate]'))
            break;
          cursor = cursor.nextElementSibling;
          // keep only shopify sections
          while (cursor && !cursor.classList?.contains('shopify-section')) cursor = cursor.nextElementSibling;
          checks++;
        }
        // Anchor = the last candidate we found, else fallback to just the next section or the product section itself
        return candidates[candidates.length - 1] || next || thisShopifySection || sectionRoot;
      };

      const computeRelease = () => {
        const sliderH = getSliderH();
        // Authoritative height: set the CSS custom property used by styles to this px value
        // so JS math and CSS layout use the exact same number (avoids dvh/vh mismatches).
        sectionRoot.style.setProperty('--runway-slider-h', sliderH + 'px');
        // Also set inline height as a fallback in case styles change
        infoWrap.style.height = sliderH + 'px';

        // IMPORTANT: measure from the product root, not the outer .shopify-section wrapper.
        // The rail's absolute 'top' is relative to sectionRoot, so using the wrapper
        // would introduce an offset equal to wrapper padding/margins (observed ~636px).
        const sectionTop = sectionRoot.getBoundingClientRect().top + window.scrollY;

        // Prefer the inner content of the next section (e.g., recommendations grid),
        // not the outer .shopify-section wrapper which can include large padding.
        const anchor = findReleaseAnchor();
        let target = anchor;
        try {
          // Explicit merchant override
          const explicit = anchor.querySelector?.('[data-rail-bottom]');
          // Known inner containers that represent the visual bottom of content
          const knownInner = anchor.querySelector?.(
            ':scope > .product-recs__inner, .product-recs__inner, .keep-exploring__inner, .shop-the-look__inner, .benefits__inner, .section-inner'
          );
          target = explicit || knownInner || anchor;
        } catch(_) { target = anchor; }

        const rect = target.getBoundingClientRect();
        // Allow merchants to specify which edge should be used for release math.
        // Default is bottom; if data-rail-edge="top" (on target or anchor), use top.
        const edgeAttr = (target.getAttribute && target.getAttribute('data-rail-edge'))
          || (anchor.getAttribute && anchor.getAttribute('data-rail-edge'))
          || '';
        const useTopEdge = (edgeAttr + '').toLowerCase() === 'top'
          || (target.hasAttribute && target.hasAttribute('data-rail-end-top'))
          || (anchor.hasAttribute && anchor.hasAttribute('data-rail-end-top'));
        const anchorEdge = (useTopEdge ? rect.top : rect.top + rect.height) + window.scrollY;

        // Measure the natural height of the OUTER rail container so margins/padding are accounted for
        let outerNaturalH = sliderH;
        try {
          const prevH = infoWrap.style.getPropertyValue('height');
          const prevPri = infoWrap.style.getPropertyPriority('height');
          infoWrap.style.setProperty('height', 'auto', 'important');
          outerNaturalH = Math.max(outerNaturalH, Math.ceil(infoWrap.getBoundingClientRect().height));
          // restore
          if (prevH) infoWrap.style.setProperty('height', prevH, prevPri === 'important' ? 'important' : '');
          else infoWrap.style.removeProperty('height');
        } catch(_) {}

        const panelH = Math.max(sliderH, outerNaturalH);
        // Floor to avoid 1px visual gap from rounding
        const releaseTop = Math.max(0, Math.floor(anchorEdge - sectionTop - panelH));
        // Write to the product root (used by CSS) and also mirror to the wrapper for easier debugging
        sectionRoot.style.setProperty('--release-top', releaseTop + 'px');
        try { (sectionRoot.closest('.shopify-section') || {}).style?.setProperty('--release-top', releaseTop + 'px'); } catch(_e) {}
        return { releaseTop, sectionTop };
      };

      // Measure pixel-perfect right offset + lock width while fixed to eliminate sub-pixel shifts
      const readPxVar = (el, name, fallback = 0) => {
        const v = getComputedStyle(el).getPropertyValue(name);
        const n = parseFloat(v);
        return isNaN(n) ? fallback : n;
      };

      const getEdgeGap = () => {
        // Try section-local --pi-edge-gap, then fall back to a sensible default
        const local = readPxVar(sectionRoot, '--pi-edge-gap', NaN);
        if (!isNaN(local)) return local;
        // ≈ clamp(12px, 2.5vw, 22px) – approximate with 2.5vw bounded to [12,22]
        return Math.max(12, Math.min(22, Math.round(window.innerWidth * 0.025)));
      };

      const setRailRight = () => {
        // Section's box in the viewport
        const rect = sectionRoot.getBoundingClientRect();
        const gap = getEdgeGap();

        // Distance from section's right edge to viewport's right edge
        // This is what makes "absolute right" equal to "fixed right"
        const inset = Math.max(0, Math.round(window.innerWidth - rect.right + gap));

        sectionRoot.style.setProperty('--rail-right', inset + 'px');
      };

      const lockWidthIfFixed = () => {
        // When fixed, lock the pixel width so rounding doesn't change it
        const isFixed = infoWrap.classList.contains('is-fixed');
        if (isFixed) {
          const w = Math.round(infoWrap.getBoundingClientRect().width);
          // Only write if changed to avoid style churn
          if (infoWrap.style.width !== w + 'px') infoWrap.style.width = w + 'px';
        } else {
          // Let it be fluid again when absolute (released/initial)
          if (infoWrap.style.width) infoWrap.style.width = '';
        }
      };

      const refreshRailPosition = () => {
        setRailRight();
        lockWidthIfFixed();
      };

      // Toggle states based on scroll position
      let lastMode = '';
      const updateMode = () => {
        const { releaseTop, sectionTop } = computeRelease();
        const y = window.scrollY;
        const header = getHeaderH();

        // Enter fixed when the top of the rail reaches viewport top (header offset accounted for in CSS)
        const fixedStart = sectionTop - header;
        const fixedEnd   = sectionTop + releaseTop;

        let mode = 'natural';
        if (y >= fixedStart && y < fixedEnd)      mode = 'fixed';
        else if (y >= fixedEnd)                   mode = 'released';

        if (mode !== lastMode) {
          infoWrap.classList.toggle('is-fixed', mode === 'fixed');
          infoWrap.classList.toggle('is-released', mode === 'released');
          lastMode = mode;
        }
      };

      // Efficient observers
      const schedule = () => requestAnimationFrame(updateMode);
      const scheduleRailRefresh = () => requestAnimationFrame(refreshRailPosition);

      // Update mode on scroll
      window.addEventListener('scroll', schedule, { passive: true });

      // Update both mode and rail position on resize
      const onResize = () => {
        scheduleRailRefresh();
        schedule();
      };
      window.addEventListener('resize', onResize, { passive: true });
      window.addEventListener('orientationchange', onResize, { passive: true });
      if (window.visualViewport) {
        visualViewport.addEventListener('resize', onResize, { passive: true });
        visualViewport.addEventListener('scroll', onResize, { passive: true });
      }
      document.addEventListener('runway:header-height', onResize);
      window.addEventListener('load', onResize);
      document.addEventListener('DOMContentLoaded', onResize);

      // Watch for class changes (fixed <-> released) to lock/unlock width
      if ('MutationObserver' in window) {
        const classObserver = new MutationObserver(scheduleRailRefresh);
        classObserver.observe(infoWrap, { attributes: true, attributeFilter: ['class'] });
      }

      // Also re-measure when "mates" mutate (e.g., async recs)
      if ('MutationObserver' in window) {
        const contentObserver = new MutationObserver(schedule);
        contentObserver.observe(document.documentElement, { childList: true, subtree: true });
      }

      // If rail content can expand (accordion, errors), remeasure right offset
      if ('ResizeObserver' in window) {
        new ResizeObserver(scheduleRailRefresh).observe(infoWrap);
      }

      // Initial pass + retries to catch late layout
      requestAnimationFrame(() => {
        refreshRailPosition();
        schedule();
      });
      setTimeout(() => refreshRailPosition(), 80);
      setTimeout(() => refreshRailPosition(), 300);
    }
  }

  // Touch/swipe variables for mobile
  let touchStartX = 0;
  let touchStartY = 0;
  let touchEndX = 0;
  let touchEndY = 0;
  const minSwipeDistance = 50;

  // Helper: compute scrollLeft to center an image within the slider viewport
  const getImageScrollLeft = (img) => {
    if (!slider || !img) return 0;
    const maxScroll = Math.max(0, slider.scrollWidth - slider.clientWidth);
    const centerTarget = Math.round(img.offsetLeft + (img.clientWidth / 2) - (slider.clientWidth / 2));
    return Math.max(0, Math.min(maxScroll, centerTarget));
  };
  // Snap to nearest image center for Runway layout
  const snapToNearestRunway = () => {
    if (!isRunway || !slider || !images || images.length === 0) return;
    const sliderRect = slider.getBoundingClientRect();
    const scrollCenter = slider.scrollLeft + sliderRect.width / 2;
    let nearestIdx = 0;
    let minDist = Infinity;
    images.forEach((img, i) => {
      const rect = img.getBoundingClientRect();
      const imgCenter = slider.scrollLeft + (rect.left - sliderRect.left) + rect.width / 2;
      const dist = Math.abs(imgCenter - scrollCenter);
      if (dist < minDist) { minDist = dist; nearestIdx = i; }
    });
    const target = getImageScrollLeft(images[nearestIdx]);
    slider.scrollTo({ left: target, behavior: 'smooth' });
  };

  // Recenter the nearest image after viewport resizes so images remain aligned
  window.addEventListener('resize', () => {
    // Debounce to the next frame to allow layout to settle
    if (!isRunway) return;
    requestAnimationFrame(snapToNearestRunway);
  });

  // Dot click functionality (Runway: horizontal scroll to image)
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      if (isRunway) {
        if (slider && images[index]) {
          const targetScroll = getImageScrollLeft(images[index]);
          slider.scrollTo({ left: targetScroll, behavior: 'auto' });
          updateActive(index);
        }
      } else {
        goToSlide(index);
      }
    });
  });

  // Thumbnail click functionality
  thumbs.forEach((btn) => {
    btn.addEventListener('click', () => {
      const idx = parseInt(btn.getAttribute('data-thumb-index'), 10);
      if (Number.isNaN(idx)) return;
      if (!images[idx]) return; // guard when thumbs > images
      if (isRunway) {
        if (slider && images[idx]) {
          const targetScroll = getImageScrollLeft(images[idx]);
          slider.scrollTo({ left: targetScroll, behavior: 'auto' });
          updateActive(idx);
        }
      } else if (isScrollAnimation) {
        images[idx]?.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        updateActive(idx);
      } else {
        goToSlide(idx);
      }
    });
    btn.addEventListener('keydown', (e) => {
      const key = e.key;
      if (!['ArrowLeft','ArrowRight','ArrowUp','ArrowDown','Home','End',' ' , 'Enter'].includes(key)) return;
      e.preventDefault();
      const list = Array.from(thumbs);
      let i = list.indexOf(btn);
      if (key === 'ArrowLeft' || key === 'ArrowUp') i = Math.max(0, i-1);
      if (key === 'ArrowRight' || key === 'ArrowDown') i = Math.min(list.length-1, i+1);
      if (key === 'Home') i = 0;
      if (key === 'End') i = list.length-1;
      if (['ArrowLeft','ArrowRight','ArrowUp','ArrowDown','Home','End'].includes(key)) {
        list[i].focus(); list[i].click();
      }
      if (key === ' ' || key === 'Enter') { btn.click(); }
    });
  });

  // Mobile arrows (carousel)
  const prevBtn = slider ? slider.querySelector('.slider-arrow[data-prev]') : null;
  const nextBtn = slider ? slider.querySelector('.slider-arrow[data-next]') : null;
  if (prevBtn && nextBtn) {
    prevBtn.addEventListener('click', () => {
      const prevSlide = currentSlide > 0 ? currentSlide - 1 : images.length - 1;
      goToSlide(prevSlide);
    });
    nextBtn.addEventListener('click', () => {
      const nextSlide = currentSlide < images.length - 1 ? currentSlide + 1 : 0;
      goToSlide(nextSlide);
    });
  }

  // Enhanced scroll functionality
  let scrollTimeout;
  let scrollDirection = 0;
  
  if (isScrollAnimation) {
    // For scroll animation, listen to slider's internal scroll
    slider.addEventListener('scroll', () => {
      if (isScrolling) return;
      
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        handleSliderScroll();
      }, 100); // Debounce scroll events
    }, { passive: true });
  } else if (isRunway) {
    // Horizontal scroll tracking for Runway mode
    const onRunwayScroll = () => {
      if (!slider) return;
      // Pick the image with the largest visible overlap inside the slider viewport.
      const sRect = slider.getBoundingClientRect();
      let best = 0;
      let bestIdx = 0;
      for (let i = 0; i < images.length; i++) {
        const r = images[i].getBoundingClientRect();
        const visible = Math.max(0, Math.min(r.right, sRect.right) - Math.max(r.left, sRect.left));
        if (visible > best) { best = visible; bestIdx = i; }
      }
      // Edge bias: near hard edges, force first/last selection for stability
      const maxScroll = Math.max(0, slider.scrollWidth - slider.clientWidth);
      if (slider.scrollLeft <= 1) bestIdx = 0;
      else if (maxScroll - slider.scrollLeft <= 1) bestIdx = images.length - 1;
      if (bestIdx !== currentSlide) updateActive(bestIdx);
    };
    slider.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        onRunwayScroll();
      }, 48); // tighter sync without being too chatty
    }, { passive: true });

    // Important: do NOT hijack vertical wheel scrolling on Runway.
    // Trackpads with horizontal intent (deltaX) will still scroll slider natively.
    // Vertical wheel should bubble to the page for normal down/up scrolling.
  } else {
    // For fade animation, listen to window scroll
    window.addEventListener('scroll', () => {
      if (isScrolling) return;
      
      const currentScrollY = window.scrollY;
      scrollDirection = currentScrollY > lastScrollY ? 1 : -1;
      lastScrollY = currentScrollY;
      
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        handleScrollChange();
      }, 16); // ~60fps for smooth updates
    }, { passive: true });
  }

  function handleSliderScroll() {
    const sliderScrollTop = slider.scrollTop;
    const sliderHeight = slider.clientHeight;
    
    // Calculate which image should be active based on scroll position
    let activeIndex = 0;
    for (let i = 0; i < images.length; i++) {
      const imageTop = images[i].offsetTop;
      const imageHeight = images[i].clientHeight;
      
      if (sliderScrollTop >= imageTop - sliderHeight / 2 && 
          sliderScrollTop < imageTop + imageHeight - sliderHeight / 2) {
        activeIndex = i;
        break;
      }
    }
    
    if (activeIndex !== currentSlide) updateActive(activeIndex);
  }

  function handleScrollChange() {
    const sliderElement = slider;
    if (!sliderElement) return;
    const sliderRect = sliderElement.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    
    // Check if slider is in viewport
    if (sliderRect.top < windowHeight && sliderRect.bottom > 0) {
      // Calculate scroll progress within the slider area
      const sliderVisibleHeight = Math.min(sliderRect.bottom, windowHeight) - Math.max(sliderRect.top, 0);
      const visibilityRatio = sliderVisibleHeight / windowHeight;
      
      if (visibilityRatio > 0.3) { // Only when slider is significantly visible
        const scrollProgress = Math.max(0, Math.min(1, (windowHeight - sliderRect.top) / windowHeight));
        const targetSlide = Math.floor(scrollProgress * images.length);
        const clampedSlide = Math.max(0, Math.min(images.length - 1, targetSlide));
        
        if (clampedSlide !== currentSlide) {
          goToSlide(clampedSlide);
        }
      }
    }
  }

  // Touch/swipe functionality for mobile
  slider.addEventListener('touchstart', (e) => {
    touchStartX = e.changedTouches[0].screenX;
    touchStartY = e.changedTouches[0].screenY;
  }, { passive: true });

  slider.addEventListener('touchend', (e) => {
    touchEndX = e.changedTouches[0].screenX;
    touchEndY = e.changedTouches[0].screenY;
    
  // Handle swipes for fade animation and swipe animation; Runway uses native drag/scroll
  if (!isScrollAnimation && !isRunway) {
      handleSwipe();
    }
  }, { passive: true });

  // Runway: after native touch scroll ends, snap to nearest image
  // No snap on Runway after touch end (smooth free scroll)

  function handleSwipe() {
    const swipeDistanceX = touchEndX - touchStartX;
    const swipeDistanceY = touchEndY - touchStartY;
    
    // Check if it's a horizontal swipe (not vertical scroll)
    if (Math.abs(swipeDistanceX) > Math.abs(swipeDistanceY) && Math.abs(swipeDistanceX) > minSwipeDistance) {
      if (swipeDistanceX > 0) {
        // Swipe right - previous image
        const prevSlide = currentSlide > 0 ? currentSlide - 1 : images.length - 1;
        goToSlide(prevSlide);
      } else {
        // Swipe left - next image
        const nextSlide = currentSlide < images.length - 1 ? currentSlide + 1 : 0;
        goToSlide(nextSlide);
      }
    }
    // Vertical swipe for image navigation (alternative to scroll)
    else if (Math.abs(swipeDistanceY) > minSwipeDistance && Math.abs(swipeDistanceY) > Math.abs(swipeDistanceX)) {
      if (swipeDistanceY < 0) {
        // Swipe up - next image
        const nextSlide = currentSlide < images.length - 1 ? currentSlide + 1 : 0;
        goToSlide(nextSlide);
      } else {
        // Swipe down - previous image
        const prevSlide = currentSlide > 0 ? currentSlide - 1 : images.length - 1;
        goToSlide(prevSlide);
      }
    }
  }

  // Mouse wheel support for desktop
  slider.addEventListener('wheel', (e) => {
    if (isScrollAnimation || isRunway) {
      // Let the scroll animation handle wheel events naturally
      return;
    }
    
    e.preventDefault();
    
    if (isScrolling) return;
    
    const delta = e.deltaY > 0 ? 1 : -1;
    
    if (delta > 0) {
      // Scroll down - next image
      const nextSlide = currentSlide < images.length - 1 ? currentSlide + 1 : 0;
      goToSlide(nextSlide);
    } else {
      // Scroll up - previous image
      const prevSlide = currentSlide > 0 ? currentSlide - 1 : images.length - 1;
      goToSlide(prevSlide);
    }
  }, { passive: false });

  function goToSlide(index) {
    if (index === currentSlide || index < 0 || index >= images.length) return;
    
    isScrolling = true;
    
    if (isScrollAnimation) {
      // Scroll animation - scroll to specific image
      const targetImage = images[index];
      if (targetImage) {
        targetImage.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start',
          inline: 'nearest'
        });
      }
    } else {
      // Fade animation - toggle opacity
      images[currentSlide].classList.remove('active');
      images[index].classList.add('active');
    }
    
    // Sync UI
    updateActive(index);
    
    // Reset scrolling flag after transition
    setTimeout(() => {
      isScrolling = false;
    }, 400);
  }

  // Keyboard navigation
  document.addEventListener('keydown', (e) => {
    const sliderRect = slider.getBoundingClientRect();
    const isSliderVisible = sliderRect.top < window.innerHeight && sliderRect.bottom > 0;
    
    if (!isSliderVisible) return;
    
    if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
      e.preventDefault();
      const prevSlide = currentSlide > 0 ? currentSlide - 1 : images.length - 1;
      goToSlide(prevSlide);
    } else if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
      e.preventDefault();
      const nextSlide = currentSlide < images.length - 1 ? currentSlide + 1 : 0;
      goToSlide(nextSlide);
    }
  });

  // Click-to-zoom (lightbox) - disabled in Runway mode
  if ({{ section.settings.enable_zoom | json }} && !isRunway) {
    images.forEach(img => {
      img.addEventListener('click', () => {
        const src = img.getAttribute('data-full') || img.currentSrc || img.src;
        const dlg = document.getElementById('MediaLightbox-{{ section.id }}');
        const view = dlg?.querySelector('img');
        if (!dlg || !view) return;
        view.src = src;
        if (dlg.showModal) dlg.showModal(); else dlg.removeAttribute('hidden');
      });
    });
  }

  // Color swatch hover preview (when enabled)
  const previewContainers = document.querySelectorAll('.color-swatches[data-preview="true"][data-color-position]');
  const revertDelay = {{ section.settings.revert_delay_ms | default: 0 }};
  previewContainers.forEach(container => {
    const pos = parseInt(container.getAttribute('data-color-position'), 10) || 1;
    let before = null; let revertTimer = null;
    container.querySelectorAll('.color-swatch[data-color-value]').forEach(lbl => {
      lbl.addEventListener('mouseenter', () => {
        const val = lbl.getAttribute('data-color-value');
        if (!val) return;
        before = currentSlide;
        // Find a variant with this color and its media id
        const match = variants.find(v => {
          const opt = pos === 1 ? v.option1 : pos === 2 ? v.option2 : v.option3;
          return opt === val && v.featured_media && v.featured_media.id;
        });
        if (!match || !match.featured_media || !match.featured_media.id) return;
        const mediaId = String(match.featured_media.id);
        const targetIndex = Array.from(images).findIndex(img => img.getAttribute('data-media-id') === mediaId);
        if (targetIndex >= 0) goToSlide(targetIndex);
      });
      lbl.addEventListener('mouseleave', () => {
        if (before == null) return;
        clearTimeout(revertTimer);
        if (revertDelay > 0) {
          revertTimer = setTimeout(() => { goToSlide(before); before = null; }, revertDelay);
        } else {
          goToSlide(before); before = null;
        }
      });
    });
  });

  // Variant Selection and Product Updates
  const form = document.getElementById('product-form');
  const variantInputs = document.querySelectorAll('.variant-input');
  // Quantity controls (ATC row)
  const qtyInputs = sectionRoot.querySelectorAll('.add-to-cart-section .qty-control input[type="number"], .add-to-cart-section .qty-inline input[type="number"], .add-to-cart-section input[type="number"][data-qty-input]');
  const qtyMinusBtns = sectionRoot.querySelectorAll('.add-to-cart-section .qty-btn.qty-minus');
  const qtyPlusBtns = sectionRoot.querySelectorAll('.add-to-cart-section .qty-btn.qty-plus');
  // Prevent in-button quantity input from triggering ATC submit
  const inBtnQtyInputs = sectionRoot.querySelectorAll('.inbtn-qty__input');
  inBtnQtyInputs.forEach((inp) => {
    ['click','mousedown','mouseup','pointerdown','pointerup','touchstart'].forEach(ev => {
      inp.addEventListener(ev, (e) => { e.stopPropagation(); });
    });
    inp.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); e.stopPropagation(); }
    });
  });
  // Focus inline qty on hover so caret blinks; desktop only
  const atcCombos = sectionRoot.querySelectorAll('.atc-combo[data-qty-hover="true"]');
  atcCombos.forEach(combo => {
    const input = combo.querySelector('.inbtn-qty__input');
    const btn = combo.querySelector('.add-to-cart-btn');
    if (!input || !btn) return;
    const focusIt = () => { if (window.innerWidth >= 990) { try { input.focus({ preventScroll: true }); } catch(_){} } };
    combo.addEventListener('mouseenter', focusIt);
    btn.addEventListener('click', (e) => {
      if (window.innerWidth < 990) return;
      const wrap = combo.querySelector('.inbtn-qty-overlay');
      if (!wrap) return;
      const style = getComputedStyle(wrap);
      const visible = parseFloat(style.opacity || '0') > 0;
      const val = parseInt(input.value, 10);
      const hasValue = Number.isFinite(val) && val >= 1;
      if (visible && !hasValue) { e.preventDefault(); e.stopPropagation(); focusIt(); }
    });
    // Press Enter in qty field to submit when valid
    input.addEventListener('keydown', (e) => {
      if (e.key !== 'Enter') return;
      const val = parseInt(input.value, 10);
      const hasValue = Number.isFinite(val) && val >= 1;
      if (!hasValue) { e.preventDefault(); return; }
      e.preventDefault();
      const hidden = ensureHiddenQty(); if (hidden) hidden.value = String(val);
      const formEl = btn.closest('form') || document.getElementById('product-form');
      try { formEl?.requestSubmit ? formEl.requestSubmit() : formEl?.submit(); } catch(_){}
    });
  });

  const ensureHiddenQty = () => {
    if (!form) return null;
    let hiddenQty = form.querySelector('.pdp-qty input[name="quantity"]');
    if (!hiddenQty) hiddenQty = form.querySelector('#QtyHidden-{{ section.id }}');
    if (!hiddenQty) {
      hiddenQty = document.createElement('input');
      hiddenQty.type = 'hidden';
      hiddenQty.name = 'quantity';
      hiddenQty.id = 'QtyHidden-{{ section.id }}';
      hiddenQty.value = '1';
      form.appendChild(hiddenQty);
    }
    return hiddenQty;
  };

  const syncQty = (val) => {
    const v = Math.max(1, parseInt(val || '1', 10) || 1);
    qtyInputs.forEach(i => { i.value = v; });
    const target = ensureHiddenQty();
    if (target) target.value = String(v);
  };
  const currentVal = () => parseInt(qtyInputs[0]?.value || '1', 10) || 1;
  qtyMinusBtns.forEach(b => b.addEventListener('click', () => { const v = currentVal() - 1; syncQty(v); }));
  qtyPlusBtns.forEach(b => b.addEventListener('click', () => { const v = currentVal() + 1; syncQty(v); }));
  qtyInputs.forEach(i => i.addEventListener('change', () => syncQty(i.value)));
  // Ensure hidden quantity exists; keep visible blank until user types
  ensureHiddenQty();
  const priceElement = document.getElementById('productPrice');
  const priceSummaryElement = document.getElementById('productPriceSummary');
  const addToCartBtn = document.getElementById('addToCartBtn');
  const addToCartBtnSummary = document.getElementById('addToCartBtnSummary');
  const variantIdInput = document.getElementById('variantId');

  // Product variant data
  const variants = [
    {%- for variant in product.variants -%}
      {
        "id": {{ variant.id }},
        "price": {{ variant.price }},
        "compare_at_price": {{ variant.compare_at_price | default: 0 }},
        "available": {{ variant.available | json }},
        "option1": {{ variant.option1 | json }},
        "option2": {{ variant.option2 | json }},
        "option3": {{ variant.option3 | json }},
        "featured_media": {
          {%- if variant.featured_media -%}
            "id": {{ variant.featured_media.id }}
          {%- else -%}
            "id": null
          {%- endif -%}
        }
      }{%- unless forloop.last -%},{%- endunless -%}
    {%- endfor -%}
  ];

  // Handle variant selection
  variantInputs.forEach(input => {
    input.addEventListener('change', updateProduct);
  });

  const T_ADD = {{ 'products.product.add_to_cart' | t | json }};
  const T_SOLD = {{ 'products.product.sold_out' | t | json }};
  const T_NO_RECS = {{ 'products.facets.no_results' | t | json }};

  function updateProduct() {
    const selectedOptions = [];
    
    // Get selected values for each option
    for (let i = 1; i <= 3; i++) {
      const checkedInput = document.querySelector(`input[data-option-position="${i}"]:checked`);
      if (checkedInput) {
        selectedOptions.push(checkedInput.value);
      }
    }

    // Find matching variant
    const selectedVariant = variants.find(variant => {
      return selectedOptions.every((option, index) => {
        const variantOption = index === 0 ? variant.option1 : 
                             index === 1 ? variant.option2 : 
                             variant.option3;
        return variantOption === option;
      });
    });

    if (selectedVariant) {
      // Update price
      const formattedPrice = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(selectedVariant.price / 100);
      
      priceElement.textContent = formattedPrice;
      if (priceSummaryElement) {
        priceSummaryElement.textContent = formattedPrice;
      }
      
      // Update add to cart button
      variantIdInput.value = selectedVariant.id;
      
      if (selectedVariant.available) {
        addToCartBtn.disabled = false;
        addToCartBtn.textContent = T_ADD || 'Add to cart';
        if (addToCartBtnSummary) {
          addToCartBtnSummary.disabled = false;
          addToCartBtnSummary.textContent = T_ADD || 'Add to cart';
        }
      } else {
        addToCartBtn.disabled = true;
        addToCartBtn.textContent = T_SOLD || 'Sold out';
        if (addToCartBtnSummary) {
          addToCartBtnSummary.disabled = true;
          addToCartBtnSummary.textContent = T_SOLD || 'Sold out';
        }
      }

      // Update URL without page reload
      const url = new URL(window.location);
      url.searchParams.set('variant', selectedVariant.id);
      window.history.replaceState({}, '', url);

      // Toggle pickup availability for the selected variant
      document.querySelectorAll('[data-pickup-block] .pickup__variant').forEach(el => el.setAttribute('hidden',''));
      const pv = document.getElementById(`PickupFor-${selectedVariant.id}`);
      if (pv) pv.removeAttribute('hidden');
    }

    // Update visual selection states
    updateSelectionStyles();
  }

  function updateSelectionStyles() {
    // Update color swatch selections
    document.querySelectorAll('.color-swatch').forEach(swatch => {
      swatch.classList.remove('selected');
    });
    document.querySelectorAll('.color-swatches input:checked').forEach(input => {
      document.querySelector(`label[for="${input.id}"]`).classList.add('selected');
    });

    // Update size option selections
    document.querySelectorAll('.size-option').forEach(option => {
      option.classList.remove('selected');
    });
    document.querySelectorAll('.size-options input:checked').forEach(input => {
      document.querySelector(`label[for="${input.id}"]`).classList.add('selected');
    });
  }

  // Initialize selection styles
  updateSelectionStyles();

  // Countdown timers
  (function(){
    const pads = n => String(n).padStart(2,'0');
    const toParts = ms => {
      const s = Math.max(0, Math.floor(ms/1000));
      const d = Math.floor(s/86400);
      const h = Math.floor((s%86400)/3600);
      const m = Math.floor((s%3600)/60);
      const sec = s%60;
      return {d,h,m,sec};
    };
    const els = document.querySelectorAll('.pdp-countdown[data-deadline]');
    if (!els.length) return;
    function tick(){
      els.forEach(el => {
        const t = el.querySelector('.pdp-countdown__timer');
        const end = new Date(el.getAttribute('data-deadline')).getTime();
        const now = Date.now();
        const parts = toParts(end - now);
        if (isNaN(end)) return;
        t.textContent = (parts.d>0? parts.d+':':'') + pads(parts.h)+':'+pads(parts.m)+':'+pads(parts.sec);
      });
    }
    tick();
    setInterval(tick, 1000);
  })();

  // Copy code buttons
  document.querySelectorAll('.copy-code[data-code]').forEach(btn => {
    btn.addEventListener('click', () => {
      const code = btn.getAttribute('data-code');
      navigator.clipboard?.writeText(code);
      btn.textContent = code + ' ✓';
      setTimeout(() => btn.textContent = code, 1200);
    });
  });

  // Notify / Ask dialogs
  (function(){
    const notifyBtn = document.querySelector('[data-open-notify]');
    const askBtn = document.querySelector('[data-open-ask]');
    const notifyDlg = document.getElementById('Notify-{{ section.id }}');
    const askDlg = document.getElementById('Ask-{{ section.id }}');
    if (notifyBtn && notifyDlg) notifyBtn.addEventListener('click', () => notifyDlg.showModal ? notifyDlg.showModal() : notifyDlg.removeAttribute('hidden'));
    if (askBtn && askDlg) askBtn.addEventListener('click', () => askDlg.showModal ? askDlg.showModal() : askDlg.removeAttribute('hidden'));
    notifyDlg?.querySelector('[data-close]')?.addEventListener('click', () => notifyDlg.close ? notifyDlg.close() : notifyDlg.setAttribute('hidden',''));
    askDlg?.querySelector('[data-close]')?.addEventListener('click', () => askDlg.close ? askDlg.close() : askDlg.setAttribute('hidden',''));
  })();

  // Complementary products fetch
  (function(){
    document.querySelectorAll('[data-complementary][data-product-id]').forEach(root => {
      const prodId = root.getAttribute('data-product-id');
      const limit = root.getAttribute('data-limit') || '4';
      const url = `/recommendations/products.json?product_id=${encodeURIComponent(prodId)}&limit=${encodeURIComponent(limit)}&intent=complementary`;
      fetch(url, { credentials: 'same-origin' })
        .then(r => r.json())
        .then(json => {
          const wrap = root.querySelector('.pdp-complementary__inner');
          if (!wrap) return;
          const items = Array.isArray(json.products) ? json.products : [];
          if (!items.length) { wrap.innerHTML = `<small>${T_NO_RECS || 'No recommendations.'}</small>`; return; }
          const grid = document.createElement('div');
          grid.className = 'pdp-complementary__grid';
          items.forEach(p => {
            const card = document.createElement('a');
            card.className = 'comp-card';
            card.href = p.url;
            const media = document.createElement('div');
            media.className = 'comp-card__media';
            if (p.images && p.images[0]) {
              const img = document.createElement('img');
              img.src = p.images[0];
              img.alt = p.title || '';
              media.appendChild(img);
            }
            const info = document.createElement('div');
            info.className = 'comp-card__info';
            const title = document.createElement('div');
            title.className = 'comp-card__title';
            title.textContent = p.title;
            const price = document.createElement('div');
            price.className = 'comp-card__price';
            price.textContent = (p.price_min ? (p.price_min/100).toLocaleString(undefined, {style:'currency', currency: (p.price_min_currency || 'USD')}) : '');
            info.appendChild(title);
            info.appendChild(price);
            card.appendChild(media);
            card.appendChild(info);
            grid.appendChild(card);
          });
          wrap.innerHTML = '';
          wrap.appendChild(grid);
        })
        .catch(() => { /* ignore */ });
    });
  })();

  // Size guide dialog/drawer
  const sgBtns = document.querySelectorAll('[data-open-size-guide]');
  const sgDialog = document.getElementById('SizeGuide-{{ section.id }}');
  const sgDrawer = document.getElementById('SizeGuideDrawer-{{ section.id }}');
  const openSizeGuide = () => {
    const placement = '{{ section.settings.size_guide_button_placement | default: 'under_options' }}';
    if (placement === 'drawer' && sgDrawer) {
      sgDrawer.removeAttribute('hidden');
      sgDrawer.setAttribute('open','');
    } else if (sgDialog) {
      // Always unhide first, then open to avoid invisible modal capturing clicks
      sgDialog.removeAttribute('hidden');
      if (typeof sgDialog.showModal === 'function') {
        if (!sgDialog.open) sgDialog.showModal();
      } else {
        sgDialog.setAttribute('open','');
      }
    }
  };
  sgBtns.forEach(btn => btn.addEventListener('click', openSizeGuide));
  sgDialog?.querySelector('[data-close]')?.addEventListener('click', () => {
    try { sgDialog.close?.(); } catch(_e){}
    sgDialog.setAttribute('hidden','');
  });
  // Ensure hidden resets on programmatic close (e.g., ESC key)
  sgDialog?.addEventListener('close', () => { sgDialog.setAttribute('hidden',''); });
  sgDrawer?.querySelector('[data-close]')?.addEventListener('click', () => { sgDrawer.removeAttribute('open'); sgDrawer.setAttribute('hidden',''); });
});
</script>

{% schema %}
{
  "name": "Product",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Desktop layout"
    },
    {
      "type": "select",
      "id": "layout_style",
      "label": "Desktop layout",
      "default": "stack",
      "options": [
        { "value": "stack",  "label": "Runway: horizontal media + floating info" },
        { "value": "50-50",  "label": "Two-column 50/50" },
        { "value": "60-40",  "label": "Two-column 60/40" }
      ],
      "info": "Selecting ‘Runway’ enables a horizontal, draggable media gallery on desktop with a sticky info column on the right. Mobile remains single column."
    },
    {
      "type": "checkbox",
      "id": "gallery_two_cols",
      "label": "Media gallery: two columns on desktop",
      "info": "No effect when Runway layout is selected",
      "default": true
    },
    { "type": "header", "content": "Runway media (desktop)" },
    { "type": "range", "id": "runway_media_max_px", "label": "Max media width (px)", "min": 600, "max": 1100, "step": 5, "default": 855 },
    { "type": "range", "id": "runway_media_min_px", "label": "Min media width (px)", "min": 280, "max": 500, "step": 5, "default": 320 },
    { "type": "range", "id": "runway_media_vw", "label": "Preferred width (vw)", "min": 40, "max": 80, "step": 1, "default": 60 },
    {
      "type": "header",
      "content": "Product Information"
    },
    {
      "type": "select",
      "id": "product_info_bg",
      "label": "Product info background",
      "default": "solid",
      "options": [
        { "value": "solid", "label": "Solid (opaque)" },
        { "value": "glass", "label": "Glass (blurred)" }
      ],
      "info": "Glass uses backdrop-filter for a frosted look. Some browsers may reduce performance or not support it."
    },
    {
      "type": "checkbox",
      "id": "show_new_badge",
      "label": "Show 'NEW' badge",
      "default": true,
      "info": "Display a 'NEW' badge above the product title"
    },
    {
      "type": "checkbox",
      "id": "show_stock_status",
      "label": "Show stock status",
      "default": true,
      "info": "Display 'In Stock' or 'Out of Stock' status"
    },
    {
      "type": "checkbox",
      "id": "show_points_program",
      "label": "Show points program",
      "default": false,
      "info": "Display points earned for this product"
    },
    {
      "type": "checkbox",
      "id": "show_wishlist_btn",
      "label": "Show 'Add to wishlist' button",
      "default": true
    },
    {
      "type": "text",
      "id": "wishlist_button_label",
      "label": "Wishlist button label",
      "default": "Add to wishlist"
    },
    {
      "type": "text",
      "id": "points_text",
      "label": "Points program text",
      "default": "6200 Oner Points",
      "info": "Text to display for points program"
    },
    {
      "type": "select",
      "id": "mobile_pagination_style",
      "label": "Mobile pagination style",
      "default": "dots",
      "options": [
        { "value": "dots",     "label": "Dots (default)" },
        { "value": "segments", "label": "Segments (lines)" },
        { "value": "none",     "label": "Hidden on mobile" }
      ],
      "info": "Applies to screens under 750px. Desktop pagination remains unchanged."
    },
    {
      "type": "header",
      "content": "Slider animation"
    },
    {
      "type": "select",
      "id": "slider_animation",
      "label": "Animation style",
      "options": [
        {
          "value": "fade",
          "label": "Fade"
        },
        {
          "value": "scroll",
          "label": "Vertical scroll"
        },
        {
          "value": "swipe",
          "label": "Swipe (mobile only)"
        }
      ],
      "default": "fade",
      "info": "Choose transition style. Swipe enables touch navigation on mobile devices."
    },
    {
      "type": "select",
      "id": "dots_position",
      "label": "Slider dots position",
      "default": "overlay",
      "options": [
        { "value": "overlay", "label": "Overlay (default)" },
        { "value": "below_mobile", "label": "Below image on mobile" },
        { "value": "below_global", "label": "Below image (all screens)" }
      ]
    },
    {
      "type": "header",
      "content": "Shipping & Features"
    },
    {
      "type": "checkbox",
      "id": "show_shipping_info",
      "label": "Show shipping information",
      "default": true,
      "info": "Display free shipping thresholds and delivery info"
    },
    {
      "type": "text",
      "id": "free_standard_threshold",
      "label": "Free standard shipping threshold",
      "default": "$120 CAD",
      "info": "Amount needed for free standard shipping"
    },
    {
      "type": "text",
      "id": "free_express_threshold",
      "label": "Free express shipping threshold",
      "default": "$220 CAD",
      "info": "Amount needed for free express shipping"
    },
    {
      "type": "checkbox",
      "id": "show_easy_returns",
      "label": "Show easy returns",
      "default": true,
      "info": "Display easy returns message"
    },
    {
      "type": "header",
      "content": "Collapsible Sections"
    },
    {
      "type": "checkbox",
      "id": "show_product_details",
      "label": "Show Product Details section",
      "default": true
    },
    {
      "type": "richtext",
      "id": "product_details_content",
      "label": "Product Details content",
      "default": "<p>Product details will be displayed here.</p>",
      "info": "Leave blank to use product description"
    },
    {
      "type": "checkbox",
      "id": "show_size_fit",
      "label": "Show Size & Fit section",
      "default": true
    },
    {
      "type": "richtext",
      "id": "size_fit_content",
      "label": "Size & Fit content",
      "default": "<p>Size and fit information will be displayed here.</p>"
    },
    {
      "type": "checkbox",
      "id": "show_material_care",
      "label": "Show Material & Care section",
      "default": true
    },
    {
      "type": "richtext",
      "id": "material_care_content",
      "label": "Material & Care content",
      "default": "<p>Material and care instructions will be displayed here.</p>"
    },
    {
      "type": "checkbox",
      "id": "show_delivery_returns",
      "label": "Show Delivery & Returns section",
      "default": true
    },
    {
      "type": "richtext",
      "id": "delivery_returns_content",
      "label": "Delivery & Returns content",
      "default": "<p>Delivery and returns information will be displayed here.</p>"
    },
    {
      "type": "checkbox",
      "id": "show_model_info",
      "label": "Show Model Info section",
      "default": true
    },
    {
      "type": "richtext",
      "id": "model_info_content",
      "label": "Model Info content",
      "default": "<p>Model information will be displayed here.</p>"
    },
    {
      "type": "header",
      "content": "Color swatch preview"
    },
    {
      "type": "checkbox",
      "id": "enable_color_hover_preview",
      "label": "Enable color hover preview",
      "default": true,
      "info": "Hover over color swatches to preview variant images"
    },
    {
      "type": "range",
      "id": "revert_delay_ms",
      "min": 0,
      "max": 1000,
      "step": 100,
      "unit": "ms",
      "label": "Preview revert delay",
      "default": 0,
      "info": "Delay before reverting hover preview (0 = instant)"
    },
    {
      "type": "header",
      "content": "Product media"
    },
    {
      "type": "checkbox",
      "id": "enable_zoom",
      "label": "Enable image zoom",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "label": "Hide sold out variants",
      "default": false
    },
    { "type": "header", "content": "Media" },
    { "type": "checkbox", "id": "show_thumbnails", "label": "Show thumbnails", "default": true },
    { "type": "checkbox", "id": "show_arrows_mobile", "label": "Show arrows on mobile", "default": true },
    { "type": "select", "id": "media_layout", "label": "Media layout (non-Runway)", "default": "stacked", "options": [
      { "value": "carousel", "label": "Carousel" },
      { "value": "stacked", "label": "Stacked (vertical scroll)" },
      { "value": "stacked_2_columns", "label": "Stacked 2 columns" },
      { "value": "stacked_2_columns_big", "label": "Stacked 2 columns (big image)" }
    ], "info": "When ‘Runway’ desktop layout is selected, this gallery setting applies only to mobile and non-Runway layouts."},
    { "type": "select", "id": "thumbnails_position", "label": "Thumbnails position (carousel)", "default": "bottom", "options": [
      { "value": "left", "label": "Left" },
      { "value": "bottom", "label": "Bottom" }
    ]},
    { "type": "select", "id": "media_height", "label": "Media height", "default": "full", "options": [
      { "value": "full", "label": "Full size" },
      { "value": "square", "label": "Square" },
      { "value": "fit_screen", "label": "Fit to screen" }
    ]},
    { "type": "checkbox", "id": "smooth_scroll_stacked", "label": "Make smooth scroll for stacked gallery", "default": true },

    { "type": "header", "content": "General" },
    { "type": "checkbox", "id": "show_taxes_included_message", "label": "Show message of taxes included", "default": true },
    { "type": "checkbox", "id": "show_pay_installments", "label": "Show pay installments", "default": false },
    { "type": "checkbox", "id": "enable_sticky_atc", "label": "Enable sticky add to cart", "default": true },
    { "type": "checkbox", "id": "show_inline_qty", "label": "Show inline quantity input (hover)", "default": false, "info": "When enabled, a compact quantity input appears near the Add to Cart button on hover." },

    { "type": "header", "content": "Size guide" },
    { "type": "checkbox", "id": "show_size_guide", "label": "Show size guide", "default": false },
    { "type": "select", "id": "size_guide_button_placement", "label": "Button placement", "default": "under_options", "options": [
      { "value": "under_options", "label": "Under product options" },
      { "value": "drawer", "label": "Drawer" }
    ]},
    { "type": "text", "id": "size_guide_button_label", "label": "Button label", "default": "Size guide" },
    { "type": "image_picker", "id": "size_guide_button_icon", "label": "Button icon" },
    { "type": "text", "id": "size_guide_drawer_heading", "label": "Drawer heading", "default": "Size guide" },
    { "type": "text", "id": "size_guide_tab_general_label", "label": "General tab label", "default": "General" },
    { "type": "text", "id": "size_guide_tab_model_label", "label": "Model tab label", "default": "Model" },

    { "type": "header", "content": "Colors" },
    { "type": "color", "id": "ui_button_color", "label": "UI button color" },
    { "type": "color", "id": "ui_button_bg", "label": "UI button background color" },
    { "type": "color", "id": "ui_button_border", "label": "UI button border color" },

    { "type": "header", "content": "Layout" },
    { "type": "select", "id": "container_width", "label": "Container width", "default": "inherit", "options": [
      { "value": "inherit", "label": "Inherit" },
      { "value": "narrow", "label": "Narrow" },
      { "value": "wide", "label": "Wide" }
    ]},
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "blocks": [
    { "type": "benefits", "name": "Benefits", "limit": 2, "settings": [
      { "type": "image_picker", "id": "icon1", "label": "Icon 1" },
      { "type": "text", "id": "heading1", "label": "Heading 1" },
      { "type": "text", "id": "link_label1", "label": "Link label 1" },
      { "type": "url", "id": "link_url1", "label": "Link URL 1" },
      { "type": "image_picker", "id": "icon2", "label": "Icon 2" },
      { "type": "text", "id": "heading2", "label": "Heading 2" },
      { "type": "text", "id": "link_label2", "label": "Link label 2" },
      { "type": "url", "id": "link_url2", "label": "Link URL 2" },
      { "type": "image_picker", "id": "icon3", "label": "Icon 3" },
      { "type": "text", "id": "heading3", "label": "Heading 3" },
      { "type": "text", "id": "link_label3", "label": "Link label 3" },
      { "type": "url", "id": "link_url3", "label": "Link URL 3" }
    ] },
    { "type": "option_accuracy", "name": "Option accuracy", "settings": [
      { "type": "text", "id": "heading", "label": "Heading" },
      { "type": "range", "id": "active_state", "label": "Active state", "min": 1, "max": 6, "step": 1, "default": 3 },
      { "type": "text", "id": "point_1", "label": "Point 1" },
      { "type": "text", "id": "point_2", "label": "Point 2" },
      { "type": "text", "id": "point_3", "label": "Point 3" },
      { "type": "text", "id": "point_4", "label": "Point 4" },
      { "type": "text", "id": "point_5", "label": "Point 5" },
      { "type": "text", "id": "point_6", "label": "Point 6" }
    ] },
    { "type": "progress_range", "name": "Progress range", "settings": [
      { "type": "checkbox", "id": "show_1", "label": "Show item 1", "default": true },
      { "type": "text", "id": "label_1", "label": "Label 1", "default": "Feature 1" },
      { "type": "range", "id": "value_1", "label": "Value 1", "min": 0, "max": 100, "step": 5, "default": 60 },
      { "type": "checkbox", "id": "show_2", "label": "Show item 2", "default": true },
      { "type": "text", "id": "label_2", "label": "Label 2", "default": "Feature 2" },
      { "type": "range", "id": "value_2", "label": "Value 2", "min": 0, "max": 100, "step": 5, "default": 80 },
      { "type": "checkbox", "id": "show_3", "label": "Show item 3", "default": false },
      { "type": "text", "id": "label_3", "label": "Label 3" },
      { "type": "range", "id": "value_3", "label": "Value 3", "min": 0, "max": 100, "step": 5, "default": 40 },
      { "type": "checkbox", "id": "show_4", "label": "Show item 4", "default": false },
      { "type": "text", "id": "label_4", "label": "Label 4" },
      { "type": "range", "id": "value_4", "label": "Value 4", "min": 0, "max": 100, "step": 5, "default": 20 }
    ] },
    { "type": "badges", "name": "Badges" },
    { "type": "vendor_sku", "name": "Vendor and SKU", "settings": [
      { "type": "checkbox", "id": "show_sku", "label": "Show SKU", "default": true },
      { "type": "text", "id": "sku_text", "label": "SKU text", "default": "SKU: {SKU}" },
      { "type": "checkbox", "id": "show_vendor", "label": "Show vendor", "default": true },
      { "type": "checkbox", "id": "vendor_link", "label": "Make vendor a link", "default": true }
    ] },
    { "type": "title", "name": "Title" },
    { "type": "text", "name": "Text", "settings": [ { "type": "richtext", "id": "text", "label": "Text" } ] },
    { "type": "description", "name": "Description" },
    { "type": "price", "name": "Price" },
    { "type": "options", "name": "Options", "settings": [
      { "type": "select", "id": "variant_picker", "label": "Variant picker", "default": "buttons", "options": [
        { "value": "buttons", "label": "Buttons" },
        { "value": "dropdown", "label": "Dropdown" }
      ]},
      { "type": "select", "id": "color_swatch_type", "label": "Color swatch type", "default": "small_square", "options": [
        { "value": "small_square", "label": "Small square" },
        { "value": "big_square", "label": "Big square" },
        { "value": "square_with_label", "label": "Square with label" }
      ]},
      { "type": "checkbox", "id": "enable_color_swatch_preview", "label": "Enable color swatch preview", "default": true },
      { "type": "checkbox", "id": "enable_color_swatch_max_height", "label": "Enable color swatch max height", "default": false }
    ] },
    { "type": "quantity", "name": "Quantity" },
    { "type": "add_to_cart", "name": "Add to cart", "settings": [
      { "type": "checkbox", "id": "show_additional_payment_buttons", "label": "Show additional payment buttons", "default": false }
    ] },
    { "type": "inventory_status", "name": "Inventory status", "settings": [
      { "type": "text", "id": "low_stock_text", "label": "Low stock text", "default": "Low stock" },
      { "type": "image_picker", "id": "low_stock_icon", "label": "Low stock icon" },
      { "type": "text", "id": "high_stock_text", "label": "High stock text", "default": "In stock" },
      { "type": "image_picker", "id": "high_stock_icon", "label": "High stock icon" },
      { "type": "range", "id": "low_stock_threshold", "label": "Low stock threshold", "min": 1, "max": 50, "step": 1, "default": 3 },
      { "type": "color", "id": "color_low_text", "label": "Low stock color" },
      { "type": "color", "id": "color_high_text", "label": "High stock color" }
    ] },
    { "type": "sales_point", "name": "Sales point", "settings": [
      { "type": "image_picker", "id": "icon", "label": "Icon image" },
      { "type": "text", "id": "text", "label": "Text" }
    ] },
    { "type": "discount", "name": "Discount", "settings": [
      { "type": "text", "id": "heading", "label": "Heading" },
      { "type": "richtext", "id": "text", "label": "Text" },
      { "type": "text", "id": "code", "label": "Discount code" }
    ] },
    { "type": "countdown", "name": "Countdown timer", "settings": [
      { "type": "text", "id": "text", "label": "Text before timer" },
      { "type": "image_picker", "id": "icon", "label": "Icon" },
      { "type": "color", "id": "color_text", "label": "Text color" },
      { "type": "color", "id": "color_timer_text", "label": "Timer text color" },
      { "type": "color", "id": "color_timer_bg", "label": "Timer background" }
    ] },
    { "type": "product_combination", "name": "Product combination", "settings": [
      { "type": "text", "id": "option_name", "label": "Option name", "default": "Color" }
    ] },
    { "type": "custom_field", "name": "Custom field", "settings": [
      { "type": "text", "id": "field_label", "label": "Field label", "default": "Custom message" },
      { "type": "text", "id": "required_text", "label": "Required text" }
    ] },
    { "type": "socials", "name": "Socials", "settings": [
      { "type": "checkbox", "id": "show_facebook", "label": "Show Facebook", "default": true },
      { "type": "checkbox", "id": "show_twitter", "label": "Show X (Twitter)", "default": true },
      { "type": "checkbox", "id": "show_pinterest", "label": "Show Pinterest", "default": true }
    ] },
    { "type": "drawers", "name": "Drawers", "settings": [
      { "type": "checkbox", "id": "include_description", "label": "Include product description", "default": true },
      { "type": "page", "id": "page1", "label": "Custom drawer 1 page" },
      { "type": "text", "id": "page1_label", "label": "Custom drawer 1 label" },
      { "type": "page", "id": "page2", "label": "Custom drawer 2 page" },
      { "type": "text", "id": "page2_label", "label": "Custom drawer 2 label" }
    ] },
    { "type": "notify_me", "name": "Notify me", "settings": [
      { "type": "text", "id": "button_label", "label": "Button label", "default": "Notify me" },
      { "type": "text", "id": "heading", "label": "Heading", "default": "Back in stock" },
      { "type": "text", "id": "email_label", "label": "Email label", "default": "Email" },
      { "type": "text", "id": "submit_label", "label": "Submit label", "default": "Send" }
    ] },
    { "type": "ask_question", "name": "Ask a question", "settings": [
      { "type": "text", "id": "button_label", "label": "Button label", "default": "Ask a question" },
      { "type": "text", "id": "heading", "label": "Heading", "default": "Question about this product" },
      { "type": "text", "id": "email_label", "label": "Email label", "default": "Email" },
      { "type": "text", "id": "name_label", "label": "Name label", "default": "Name" },
      { "type": "text", "id": "phone_label", "label": "Phone label" },
      { "type": "text", "id": "message_label", "label": "Message label", "default": "Message" },
      { "type": "text", "id": "submit_label", "label": "Submit label", "default": "Send" }
    ] },
    { "type": "pickup_availability", "name": "Pickup availability" },
    { "type": "complementary_products", "name": "Complementary products" },
    { "type": "custom_liquid", "name": "Custom liquid", "settings": [ { "type": "liquid", "id": "custom_liquid", "label": "Custom liquid" } ] },
    { "type": "@app" }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}

{% style %}
/* --- SEGMENTS: consistent gaps + longer active with width animation --- */
#product-{{ section.id }} {
  --seg-l: 18px;                  /* inactive length */
  --seg-l-active: 30px;           /* active length (Gentle Monster vibe) */
  --seg-h: 2px;                   /* bar height */
  --seg-gap: clamp(6px, 1.6vw, 10px);
}

@media (max-width: 749px){
  /* keep pagination on the slider, 4% from bottom */
  #product-{{ section.id }} .product-slider{ position: relative; }
  #product-{{ section.id }} .product-slider .slider-dots{
    position: absolute !important;
    left: 50%;
    bottom: max(4%, env(safe-area-inset-bottom)) !important;
    top: auto !important;
    transform: translateX(-50%);
    gap: var(--seg-gap) !important;
    margin: 0 !important;
    z-index: 20;
  }

  /* Stronger, scoped rules override the earlier fixed-width + !important block */
  #product-{{ section.id }}.product--mobile-segments .product-slider .slider-dots .dot{
    width: var(--seg-l) !important;
    height: var(--seg-h) !important;
    border-radius: var(--seg-h) !important;
    background: rgba(255,255,255,.45) !important;
    box-shadow: none !important;
    padding: 0 !important; margin: 0 !important;
    transform: none !important;        /* keeps the gaps uniform */
    will-change: width, background-color, opacity;
    transition:
      width 260ms cubic-bezier(.33,0,.2,1),
      background-color 200ms ease,
      opacity 200ms ease;
  }
  #product-{{ section.id }}.product--mobile-segments .product-slider .slider-dots .dot.active{
    width: var(--seg-l-active) !important; /* longer active dash */
    background: #fff !important;
    opacity: 1 !important;
  }
}

/* --- SWIPE MODE: no fade, instant switch --- */
#product-{{ section.id }} .product-slider.swipe-animation .slider-image{
  transition: none !important;     /* disable fade */
}
/* keep inactive hidden / active visible; JS already toggles .active */
#product-{{ section.id }} .product-slider.swipe-animation .slider-image{ opacity: 0; }
#product-{{ section.id }} .product-slider.swipe-animation .slider-image.active{ opacity: 1; }
{% endstyle %}

<script>
// Wishlist functionality
document.addEventListener('DOMContentLoaded', function() {
  const wishlistBtn = document.getElementById('addToWishlist');
  if (wishlistBtn) {
    wishlistBtn.addEventListener('click', function() {
      // Add wishlist functionality here
      const icon = this.querySelector('.wishlist-icon');
      if (icon.textContent === '♡') {
        icon.textContent = '♥';
        this.style.color = '#e74c3c';
      } else {
        icon.textContent = '♡';
        this.style.color = '#000';
      }
    });
  }

  // Collapsible sections animation
  const detailSections = document.querySelectorAll('.product-detail-section');
  detailSections.forEach(section => {
    const summary = section.querySelector('.detail-header');
    if (summary) {
      summary.addEventListener('click', function() {
        // Small delay to allow the details element to toggle
        setTimeout(() => {
          const toggle = this.querySelector('.detail-toggle');
          if (section.hasAttribute('open')) {
            toggle.style.transform = 'rotate(180deg)';
          } else {
            toggle.style.transform = 'rotate(0deg)';
          }
        }, 10);
      });
    }
  });
});
</script>
