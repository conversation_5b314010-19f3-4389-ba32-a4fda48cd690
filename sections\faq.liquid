{% comment %}
  FAQ section: accordion with Category and Q&A blocks
{% endcomment %}

{%- liquid
  assign heading = section.settings.heading

  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="faq color-{{ section.settings.color_scheme | default: 'background-1' }}" data-section="{{ section.id }}">
  <div class="faq__inner page-width">
    {% if heading %}
      <h2 class="faq__heading">{{ heading }}</h2>
    {% endif %}

    <div class="faq__list" data-accordion-list>
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when 'category' -%}
            {%- assign cat_heading = block.settings.heading -%}
            {% if cat_heading %}
              <h3 class="faq__category" {{ block.shopify_attributes }}>{{ cat_heading }}</h3>
            {% endif %}
          {%- when 'qa' -%}
            {%- liquid
              assign q = block.settings.heading
              assign a = block.settings.text
              assign icon = block.settings.icon
              assign icon_active = block.settings.icon_active
              assign pid = block.id | append: '-panel'
              assign hid = block.id | append: '-header'
            -%}
            {% if q and a %}
              <div class="faq-item" data-accordion {{ block.shopify_attributes }}>
                <button id="{{ hid }}" class="faq-item__header" type="button" aria-controls="{{ pid }}" aria-expanded="false">
                  {% if icon or icon_active %}
                    <span class="faq-item__icon" aria-hidden="true">
                      {% if icon %}
                        <span class="faq-item__icon-img faq-item__icon-img--default">
                          {{ icon | image_url: width: 56 | image_tag: widths: '28, 40, 56', sizes: '28px', loading: 'lazy', decoding: 'async', alt: '' }}
                        </span>
                      {% endif %}
                      {% if icon_active %}
                        <span class="faq-item__icon-img faq-item__icon-img--active">
                          {{ icon_active | image_url: width: 56 | image_tag: widths: '28, 40, 56', sizes: '28px', loading: 'lazy', decoding: 'async', alt: '' }}
                        </span>
                      {% endif %}
                    </span>
                  {% endif %}
                  <span class="faq-item__title">{{ q }}</span>
                  <span class="faq-item__arrow" aria-hidden="true">{% render 'icon-caret' %}</span>
                </button>
                <div id="{{ pid }}" class="faq-item__panel" role="region" aria-labelledby="{{ hid }}" hidden>
                  <div class="faq-item__text rte">{{ a }}</div>
                </div>
              </div>
            {% endif %}
        {%- endcase -%}
      {%- endfor -%}
    </div>
  </div>
</section>

<script>
(() => {
  const root = document.getElementById('shopify-section-{{ section.id }}');
  if (!root) return;
  root.addEventListener('click', (e) => {
    const header = e.target.closest('.faq-item__header');
    if (!header || !root.contains(header)) return;
    const item = header.closest('.faq-item');
    const panelId = header.getAttribute('aria-controls');
    const panel = panelId ? root.querySelector('#' + CSS.escape(panelId)) : null;
    const expanded = header.getAttribute('aria-expanded') === 'true';
    header.setAttribute('aria-expanded', expanded ? 'false' : 'true');
    if (panel) panel.hidden = expanded;
    item?.classList.toggle('is-open', !expanded);
  });
})();
</script>

{% style %}
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; } }

  #shopify-section-{{ section.id }} .faq__inner { max-width: var(--page-width); margin: 0 auto; padding: 0 1rem; }
  #shopify-section-{{ section.id }} .faq__heading { margin: 0 0 1rem; font-size: clamp(1.25rem, 3vw, 2rem); }
  #shopify-section-{{ section.id }} .faq__category { margin: 1.5rem 0 .5rem; font-size: 1.125rem; }

  #shopify-section-{{ section.id }} .faq-item { background: var(--faq-bg); color: var(--faq-text); border-radius: 12px; margin: .5rem 0; border: 1px solid transparent; }
  #shopify-section-{{ section.id }} .faq-item.is-open { background: var(--faq-bg-a); color: var(--faq-text-a); }

  #shopify-section-{{ section.id }} .faq-item__header { width: 100%; display: grid; grid-template-columns: auto 1fr auto; align-items: center; gap: .75rem; padding: .875rem 1rem; background: transparent; color: inherit; border: 0; text-align: left; cursor: pointer; }
  #shopify-section-{{ section.id }} .faq-item__title { font-weight: 600; }
  #shopify-section-{{ section.id }} .faq-item__icon { line-height: 0; display: inline-grid; }
  #shopify-section-{{ section.id }} .faq-item__icon-img--active { display: none; }
  #shopify-section-{{ section.id }} .faq-item.is-open .faq-item__icon-img--default { display: none; }
  #shopify-section-{{ section.id }} .faq-item.is-open .faq-item__icon-img--active { display: inline-block; }

  #shopify-section-{{ section.id }} .faq-item__arrow { display: inline-grid; place-items: center; width: 28px; height: 28px; border-radius: 6px; border: 1px solid var(--faq-arrow-b); background: var(--faq-arrow-bg); color: var(--faq-arrow); transition: transform .2s ease; }
  #shopify-section-{{ section.id }} .faq-item__arrow svg { transform: rotate(-90deg); width: 16px; height: 16px; }
  #shopify-section-{{ section.id }} .faq-item.is-open .faq-item__arrow { background: var(--faq-arrow-bg-a); color: var(--faq-arrow-a); border-color: var(--faq-arrow-b-a); }
  #shopify-section-{{ section.id }} .faq-item.is-open .faq-item__arrow svg { transform: rotate(90deg); }

  #shopify-section-{{ section.id }} .faq-item__panel { padding: 0 1rem 1rem; }
  #shopify-section-{{ section.id }} .faq-item__text { opacity: .95; }

  /* Color variables from settings */
  #shopify-section-{{ section.id }} .faq {
    --faq-bg: {{ section.settings.bg | default: 'transparent' | color_background }};
    --faq-text: {{ section.settings.text | default: '#111111' }};
    --faq-arrow: {{ section.settings.arrow | default: '#111111' }};
    --faq-arrow-bg: {{ section.settings.arrow_bg | default: '#ffffff' }};
    --faq-arrow-b: {{ section.settings.arrow_border | default: '#e5e5e5' }};
    --faq-bg-a: {{ section.settings.bg_active | default: 'transparent' | color_background }};
    --faq-text-a: {{ section.settings.text_active | default: '#111111' }};
    --faq-arrow-a: {{ section.settings.arrow_active | default: '#111111' }};
    --faq-arrow-bg-a: {{ section.settings.arrow_bg_active | default: '#ffffff' }};
    --faq-arrow-b-a: {{ section.settings.arrow_border_active | default: '#111111' }};
  }
{% endstyle %}

{% schema %}
{
  "name": "FAQ",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "text", "id": "heading", "label": "Heading" },

    { "type": "header", "content": "Colors" },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "background-1" },
    { "type": "color_background", "id": "bg", "label": "Background" },
    { "type": "color", "id": "text", "label": "Text" },
    { "type": "color", "id": "arrow", "label": "Arrow" },
    { "type": "color", "id": "arrow_bg", "label": "Arrow background" },
    { "type": "color", "id": "arrow_border", "label": "Arrow border" },
    { "type": "color_background", "id": "bg_active", "label": "Background (active)" },
    { "type": "color", "id": "text_active", "label": "Text (active)" },
    { "type": "color", "id": "arrow_active", "label": "Arrow (active)" },
    { "type": "color", "id": "arrow_bg_active", "label": "Arrow background (active)" },
    { "type": "color", "id": "arrow_border_active", "label": "Arrow border (active)" },

    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "blocks": [
    {
      "type": "category",
      "name": "Category",
      "settings": [
        { "type": "text", "id": "heading", "label": "Heading" }
      ]
    },
    {
      "type": "qa",
      "name": "Question and answer",
      "settings": [
        { "type": "text", "id": "heading", "label": "Heading" },
        { "type": "richtext", "id": "text", "label": "Text" },
        { "type": "image_picker", "id": "icon", "label": "Icon" },
        { "type": "image_picker", "id": "icon_active", "label": "Icon on active" }
      ]
    }
  ],
  "presets": [
    {
      "name": "FAQ",
      "blocks": [
        { "type": "category", "settings": { "heading": "Shipping" } },
        { "type": "qa", "settings": { "heading": "How long does shipping take?", "text": "<p>Most orders arrive in 2–5 business days.</p>" } },
        { "type": "qa", "settings": { "heading": "Do you ship internationally?", "text": "<p>Yes, we ship to most countries. Duties may apply.</p>" } },
        { "type": "category", "settings": { "heading": "Returns" } },
        { "type": "qa", "settings": { "heading": "What is your return policy?", "text": "<p>30-day hassle-free returns on unused items.</p>" } }
      ]
    }
  ]
}
{% endschema %}
