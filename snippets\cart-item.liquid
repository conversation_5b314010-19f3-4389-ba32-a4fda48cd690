{%- assign item = item -%}
<article class="cart-item" data-line-key="{{ item.key }}">
  <a href="{{ item.url }}" class="ci-media">
    {{ item.image | image_url: width: 300 | image_tag: alt: item.title }}
  </a>

  <div class="ci-main">
    <a href="{{ item.url }}"><h4 class="ci-title">{{ item.product.title }}</h4></a>
    <div class="ci-price">{{ item.final_price | money }}</div>

    {% if item.options_with_values.size > 0 %}
      <div class="ci-variants">
        {% for opt in item.options_with_values %}
          <span class="chip">{{ opt.value }}</span>
        {% endfor %}
      </div>
    {% endif %}

    <div class="ci-actions">
      <div class="qty">
        <button type="button" data-qty-minus aria-label="Decrease quantity">−</button>
        <input type="number" inputmode="numeric" min="0" value="{{ item.quantity }}" data-line-qty readonly>
        <button type="button" data-qty-plus aria-label="Increase quantity">＋</button>
      </div>
      <button type="button" class="link" data-save data-product-gid="{{ item.product.admin_graphql_api_id }}" data-handle="{{ item.product.handle }}">Save for later ❤</button>
      <button type="button" class="link" data-remove aria-label="Remove">Remove ✕</button>
    </div>

    <div class="ci-subtotal">
      <span>Subtotal</span>
      <strong data-line-subtotal>{{ item.final_line_price | money }}</strong>
    </div>
  </div>
</article>

{% style %}
.cart-item{ display:grid; grid-template-columns: 140px 1fr; gap:16px; padding:12px 0; border-bottom:1px solid #eee; }
.ci-media img{ border-radius:6px; width:140px; height:auto; }
.ci-title{ margin:0 0 4px; }
.ci-price{ color:#555; margin-bottom:4px; }
.ci-variants .chip{ display:inline-block; border:1px solid #ddd; border-radius:999px; padding:2px 8px; margin-right:6px; font-size:12px; }
.ci-actions{ display:flex; align-items:center; gap:12px; margin:8px 0; flex-wrap:wrap; }
.qty { display:inline-flex; align-items:center; border:1px solid #ddd; border-radius:999px; overflow:hidden; }
.qty button{ width:32px; height:32px; background:#f7f7f7; border:none; cursor:pointer; }
.qty input{ width:48px; text-align:center; border:none; background:transparent; }
.ci-subtotal{ display:flex; justify-content:space-between; align-items:center; margin-top:4px; }
.link{ background:none; border:none; color:#111; cursor:pointer; text-decoration:underline; padding:0; }
@media (max-width:700px){ .cart-item{ grid-template-columns: 90px 1fr; } }
{% endstyle %}