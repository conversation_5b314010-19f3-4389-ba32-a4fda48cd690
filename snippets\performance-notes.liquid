{%- comment -%}
  Performance Optimization Summary
  
  This theme implements "Prada-smooth" loading optimizations:
  
  1. CDN Warming (theme.liquid):
     - <link rel="preconnect"> to cdn.shopify.com
     - DNS prefetch for faster handshake
  
  2. LCP Image Priority (theme.liquid):
     - <link rel="preload"> for product page featured image
     - fetchpriority="high" and loading="eager"
     - Proper responsive srcset with consistent widths
  
  3. Above-the-fold Optimization (collection cards):
     - First row gets loading="eager" and fetchpriority="high"
     - Rest use loading="lazy" for bandwidth efficiency
  
  4. Consistent Image Widths (cache hits):
     - Product pages: 750, 1100, 1500, 1800, 2100, 2400
     - Collection cards: 400, 600, 800, 900
     - Same transforms = better CDN cache utilization
  
  5. CLS Prevention:
     - aspect-ratio: 1 on media containers
     - width/height attributes on all images
     - object-fit: cover for consistent layouts
  
  6. Smart Prefetching (variant-hover.js):
     - Prefetch images on swatch hover/focus
     - Deduplication with Set() to avoid double-loading
     - Touch-friendly fallbacks (focus instead of hover)
  
  7. Progressive Enhancement:
     - Works without JavaScript
     - Enhanced with hover preview and click selection
     - Graceful fallbacks for all interactions
  
  Performance Impact:
  - Faster LCP (Largest Contentful Paint)
  - Reduced CLS (Cumulative Layout Shift)
  - Better cache utilization
  - Smoother variant switching
  - Mobile-optimized touch interactions
{%- endcomment -%}

<!-- Performance optimizations are active across the theme -->
