<div class="reset-template-section" data-section-id="{{ section.id }}">
  <div class="reset-template-content">
    <div class="reset-template-inner">
      {% if section.settings.title != blank %}
        <h2 class="reset-template-title">{{ section.settings.title }}</h2>
      {% endif %}
      
      {% if section.settings.text != blank %}
        <div class="reset-template-text">{{ section.settings.text }}</div>
      {% endif %}
      
      <div class="reset-template-form">
        {% form 'reset_customer_password' %}
          {{ form.errors | default_errors }}
          
          <div class="form-field">
            <label for="password">{{ 'customer.reset_password.password' | t }}</label>
            <input type="password" name="customer[password]" id="password" class="{% if form.errors contains 'password' %}input-error{% endif %}" autocomplete="new-password">
          </div>
          
          <div class="form-field">
            <label for="password_confirmation">{{ 'customer.reset_password.password_confirm' | t }}</label>
            <input type="password" name="customer[password_confirmation]" id="password_confirmation" class="{% if form.errors contains 'password_confirmation' %}input-error{% endif %}" autocomplete="new-password">
          </div>
          
          <div class="form-action">
            <input type="submit" value="{{ 'customer.reset_password.submit' | t }}" class="btn">
          </div>
        {% endform %}
      </div>
    </div>
  </div>
</div>

{% stylesheet %}
  .reset-template-section {
    width: 100%;
    background-color: var(--color-background, #ffffff);
  }
  
  .reset-template-content {
    max-width: var(--page-width, 1200px);
    margin: 0 auto;
    padding: 0 var(--page-margin, 1rem);
  }
  
  .reset-template-inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: var(--desktop-offset-top, 40px) 0 var(--desktop-offset-bottom, 40px);
  }
  
  .reset-template-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--color-foreground, #000000);
  }
  
  .reset-template-text {
    font-size: 1rem;
    margin-bottom: 2rem;
    color: var(--color-foreground, #000000);
    max-width: 600px;
  }
  
  .reset-template-form {
    width: 100%;
    max-width: 400px;
  }
  
  .form-field {
    margin-bottom: 1.5rem;
    text-align: left;
  }
  
  .form-field label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }
  
  .form-field input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    font-size: 1rem;
  }
  
  .form-field input.input-error {
    border-color: #ff0000;
  }
  
  .form-action {
    margin-top: 1rem;
  }
  
  .btn {
    display: inline-block;
    padding: 0.75rem 2rem;
    background-color: var(--color-button, #000000);
    color: var(--color-button-text, #ffffff);
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    text-align: center;
    text-decoration: none;
    transition: background-color 0.2s ease;
  }
  
  .btn:hover {
    background-color: var(--color-button-hover, #333333);
  }
  
  @media screen and (max-width: 768px) {
    .reset-template-inner {
      padding: var(--mobile-offset-top, 20px) 0 var(--mobile-offset-bottom, 20px);
    }
    
    .reset-template-title {
      font-size: 1.5rem;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Reset Template",
  "tag": "section",
  "class": "reset-template-section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Reset Password"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text",
      "default": "<p>Enter a new password for your account.</p>"
    },
    {
      "type": "header",
      "content": "Desktop Spacing"
    },
    {
      "type": "range",
      "id": "desktop_offset_top",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Desktop offset top",
      "default": 40,
      "info": "Top indent of the section on the desktop"
    },
    {
      "type": "range",
      "id": "desktop_offset_bottom",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Desktop offset bottom",
      "default": 40,
      "info": "Bottom indent of the section on the desktop"
    },
    {
      "type": "header",
      "content": "Mobile Spacing"
    },
    {
      "type": "range",
      "id": "mobile_offset_top",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Mobile offset top",
      "default": 20,
      "info": "Top indent of the section on the mobile"
    },
    {
      "type": "range",
      "id": "mobile_offset_bottom",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Mobile offset bottom",
      "default": 20,
      "info": "Bottom indent of the section on the mobile"
    }
  ],
  "presets": [
    {
      "name": "Reset Template",
      "category": "Customer"
    }
  ]
}
{% endschema %}