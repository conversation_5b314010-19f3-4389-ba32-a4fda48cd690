# Project Intelligence: Shopify Skeleton Theme

## Critical Implementation Patterns

### 1. Conditional Settings Architecture
- Always use `visible_if` for merchant-friendly UX
- Pattern: `"visible_if": "{{ section.settings.setting_name != 'value' }}"`
- Apply to dependent settings to reduce configuration errors
- Use resource dependencies: `"visible_if": "{{ section.settings.logo != blank }}"`

### 2. Sticky Header System
- Use hybrid approach: CSS `position: sticky` for "always" mode
- JavaScript `IntersectionObserver` for "on-scroll-up" mode
- Always set `will-change: transform` and use transform for animations
- Respect reduced motion preferences with conditional CSS

### 3. Normalized Card Hovers
- Keep preview layer empty at render, populate only on hover
- Use same `srcset`/`sizes` across base, preview, and swatches
- Implement decode-first loading with proper race condition protection
- Use CSS variables for aspect ratio and object position control

### 4. Color Scheme System
- Use `color_scheme_group` type in settings schema
- Generate CSS variables dynamically in `css-variables.liquid`
- Apply schemes with `color-{{ section.settings.color_scheme }}` classes
- Use RGB variants for transparency effects

## Performance Optimization Patterns

### Image Loading
- Always use responsive `srcset` with proper `sizes` attributes
- Implement lazy loading with `loading="lazy"`
- Use decode-first approach for hover effects
- Optimize candidate widths for performance/quality balance

### JavaScript Performance
- Use passive event listeners for scroll events
- Implement efficient DOM query patterns
- Use modern APIs: IntersectionObserver, ResizeObserver
- Implement proper cleanup and memory management

### CSS Performance
- Constrain animations to `transform` and `opacity`
- Use efficient selector patterns (0 1 0 specificity)
- Leverage hardware acceleration with `transform3d(0,0,0)`
- Use CSS containment where appropriate

## Accessibility Patterns

### ARIA Implementation
- Always set proper `aria-expanded`, `aria-hidden`, `aria-controls`
- Implement comprehensive keyboard navigation
- Support screen readers with semantic markup
- Provide skip links and focus management

### Reduced Motion
- Respect `prefers-reduced-motion` media query
- Provide JavaScript detection for motion-sensitive users
- Disable animations when reduced motion preferred
- Ensure content remains accessible without animations

### Touch Device Optimization
- Use `@media (pointer: coarse)` for touch-specific styles
- Ensure adequate touch target sizes (min 45px)
- Support iOS safe areas with `env(safe-area-inset-top)`
- Disable hover effects on touch devices

## Development Workflow Patterns

### Theme Validation
- Always run `shopify theme check` before commits
- Fix all validation errors before deployment
- Maintain theme check compliance as priority

### Documentation Standards
- Keep USER_GUIDE.md focused on merchant instructions
- Use DEVELOPER_NOTES.md for technical implementation details
- Maintain comprehensive code comments for complex logic
- Use consistent markdown formatting

### Version Control
- Use semantic versioning for releases
- Write clear, descriptive commit messages
- Use feature branches for development
- Maintain changelog for each release

### Marketplace Compliance
- All code edits must be configurable via theme editor settings
- Use conditional settings (`visible_if`) for dynamic behavior
- Avoid hard-coded values; implement as settings_schema.json options
- Ensure all customizations are merchant-accessible without code changes
- Follow Shopify's theme guidelines for marketplace submission

## Merchant Experience Patterns

### Editor UX
- Use conditional settings to simplify interface
- Provide meaningful default values
- Include helpful info text for complex settings
- Use visual grouping with headers and paragraphs

### Block-Based Architecture
- Create reusable, nestable UI components
- Provide meaningful block names with emojis
- Include comprehensive settings for each block
- Support both simple and complex block types

### Mobile Configuration
- Provide mobile-specific options where appropriate
- Ensure all settings work on touch devices
- Test thoroughly on mobile breakpoints
- Provide mobile preview in theme editor

## Technical Debt Management

### Code Organization
- Refactor legacy navigation to use blocks exclusively
- Maintain consistent BEM naming conventions
- Organize JavaScript into modular components
- Keep CSS specificity predictable and minimal

### Testing Strategy
- Implement manual cross-browser testing checklist
- Consider adding automated testing framework
- Maintain performance monitoring regimen
- Regular accessibility compliance testing

### Maintenance Schedule
- Monthly theme check validation runs
- Quarterly browser compatibility testing
- Biannual performance audits
- Annual accessibility compliance review

## Extension Points

### Adding New Settings
- Follow existing patterns in settings_schema.json
- Use appropriate setting types (range, select, checkbox, etc.)
- Include conditional logic where appropriate
- Provide meaningful defaults and constraints

### Creating New Blocks
- Use consistent block type naming conventions
- Include comprehensive settings with validation
- Implement both desktop and mobile variants
- Provide proper accessibility features

### Customizing Color Schemes
- Extend color_scheme_group definition
- Add new color properties as needed
- Update CSS variable generation
- Test contrast ratios for accessibility

## Critical Files to Understand

### Core Configuration
- `config/settings_schema.json` - Theme settings and conditional logic
- `config/settings_data.json` - Default color schemes and values

### Key Components
- `sections/header.liquid` - Sticky header system and navigation
- `sections/collection.liquid` - Mixed content grid system
- `snippets/css-variables.liquid` - Dynamic theme variables

### JavaScript Modules
- `assets/variant-hover.js` - Normalized card hover system
- `assets/newsletter-popup.js` - Modal inert logic
- Header sticky behavior implementation

## Common Pitfalls to Avoid

### Performance Issues
- Avoid inline dimensions on images (breaks responsive behavior)
- Don't mix `min-height` with `height` (unpredictable sizing)
- Avoid base image swapping during hover (causes candidate re-selection)
- Don't use per-swatch revert handlers (causes thrashing)

### Accessibility Issues
- Don't forget ARIA attributes for interactive elements
- Ensure proper focus management for modals
- Support keyboard navigation throughout
- Respect reduced motion preferences

### Merchant Experience
- Don't overwhelm with too many settings
- Provide clear documentation for complex features
- Test all conditional logic thoroughly
- Ensure mobile configuration works correctly

This .clinerules file captures the essential patterns and insights for working effectively with the Shopify Skeleton Theme project.
