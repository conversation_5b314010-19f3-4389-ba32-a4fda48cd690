/* Image Zoom on Scroll — minimal, reusable styles */
.zoom-on-scroll {
  overflow: hidden;
  /* Allow custom tuning via CSS variables */
  --zoom-from: 1;
  --zoom-to: 1.2;
  --zoom-duration: 1600ms;
  --zoom-delay: 0ms;
  --zoom-ease: cubic-bezier(0.16, 1, 0.3, 1);
}

/* Reveal-on-scroll defaults */
[data-reveal-on-scroll] {
  --reveal-duration: 1200ms;
  --reveal-delay: 0ms;
  --reveal-ease: cubic-bezier(0.16, 1, 0.3, 1);
  --reveal-translate: 12px;
  opacity: 0;
  transform: translateY(var(--reveal-translate));
  transition:
    opacity var(--reveal-duration) var(--reveal-ease) var(--reveal-delay),
    transform var(--reveal-duration) var(--reveal-ease) var(--reveal-delay);
  will-change: opacity, transform;
}

[data-reveal-on-scroll].is-revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Text-specific reveal (independent from generic reveal) */
[data-text-reveal] {
  --text-reveal-duration: 1200ms;
  --text-reveal-delay: 0ms;
  --text-reveal-ease: cubic-bezier(0.16, 1, 0.3, 1);
  --text-reveal-translate: 12px;
  opacity: 0;
  transform: translateY(var(--text-reveal-translate));
  transition:
    opacity var(--text-reveal-duration) var(--text-reveal-ease) var(--text-reveal-delay),
    transform var(--text-reveal-duration) var(--text-reveal-ease) var(--text-reveal-delay);
  will-change: opacity, transform;
}

[data-text-reveal].is-text-revealed {
  opacity: 1;
  transform: translateY(0);
}

/*
 * IMPORTANT: Only scale media, never the wrapper
 *
 * If the data-attribute is on a wrapper (e.g., .hero__media),
 * scale its immediate media children so the visual growth stays
 * contained inside the wrapper box and cannot overlap adjacent sections.
 */
[data-zoom-on-scroll] > img,
[data-zoom-on-scroll] > picture,
[data-zoom-on-scroll] > video,
[data-zoom-on-scroll] img,
[data-zoom-on-scroll] picture,
[data-zoom-on-scroll] video {
  /* Establish a stable baseline so transitions always fire */
  transform: scale(var(--zoom-from, 1));
  transition: transform var(--zoom-duration, 1600ms) var(--zoom-ease, cubic-bezier(0.16, 1, 0.3, 1)) var(--zoom-delay, 0ms);
  will-change: transform;
  transform-origin: center center;
}

/* When the element carrying the attribute is itself the media */
img[data-zoom-on-scroll],
picture[data-zoom-on-scroll],
video[data-zoom-on-scroll] {
  transform: scale(var(--zoom-from, 1));
  transition: transform var(--zoom-duration, 1600ms) var(--zoom-ease, cubic-bezier(0.16, 1, 0.3, 1)) var(--zoom-delay, 0ms);
  will-change: transform;
  transform-origin: center center;
}

/* When zoom is triggered, animate from --zoom-from to --zoom-to */
[data-zoom-on-scroll].is-zoomed > img,
[data-zoom-on-scroll].is-zoomed > picture,
[data-zoom-on-scroll].is-zoomed > video,
[data-zoom-on-scroll].is-zoomed img,
[data-zoom-on-scroll].is-zoomed picture,
[data-zoom-on-scroll].is-zoomed video,
img[data-zoom-on-scroll].is-zoomed,
picture[data-zoom-on-scroll].is-zoomed,
video[data-zoom-on-scroll].is-zoomed {
  transform: scale(var(--zoom-to, 1.2));
}

/* (kept single rule above for is-zoomed state) */

/* Optional: graceful reduce motion */
@media (prefers-reduced-motion: reduce) {
  [data-zoom-on-scroll] { transition: none; }
  [data-reveal-on-scroll] { transition: none; opacity: 1; transform: none; }
  [data-text-reveal] { transition: none; opacity: 1; transform: none; }
}
