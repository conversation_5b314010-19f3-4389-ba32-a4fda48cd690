{% comment %}
  Main Account Section
  - Static section for customers/account template
  - Adds breadcrumbs, colors, and layout offsets to match login/register/reset styling
{% endcomment %}

{%- liquid
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="account account--dashboard color-{{ section.settings.color_scheme | default: 'background-1' }}" data-section="{{ section.id }}">
  <div class="account__inner page-width">
    {% render 'breadcrumbs' %}

    <h1 class="h2">{{ 'customers.account.title' | t | default: 'Account' }}</h1>

    <div class="account__grid">
      <div>
        <h2 class="h3">{{ 'customers.account.details' | t | default: 'Account details' }}</h2>
        <p>{{ customer.name }}<br>{{ customer.email }}</p>
        <p><a href="{{ routes.account_addresses_url | default: '/account/addresses' }}">{{ 'customers.account.view_addresses' | t | default: 'View addresses' }} ({{ customer.addresses_count }})</a></p>
      </div>

      <div>
        <h2 class="h3">{{ 'customers.account.orders' | t | default: 'Orders' }}</h2>
        {% if customer.orders_count == 0 %}
          <p>{{ 'customers.account.no_orders' | t | default: 'You have no orders yet.' }}</p>
        {% else %}
          <table role="table" class="account__orders-table">
            <thead role="rowgroup">
              <tr role="row">
                <th role="columnheader">{{ 'customers.account.order' | t | default: 'Order' }}</th>
                <th role="columnheader">{{ 'customers.account.date' | t | default: 'Date' }}</th>
                <th role="columnheader">{{ 'customers.account.payment' | t | default: 'Payment' }}</th>
                <th role="columnheader">{{ 'customers.account.fulfillment' | t | default: 'Fulfillment' }}</th>
                <th role="columnheader">{{ 'customers.account.total' | t | default: 'Total' }}</th>
              </tr>
            </thead>
            <tbody role="rowgroup">
              {% for order in customer.orders %}
                <tr role="row">
                  <td role="cell">{{ order.name | link_to: order.customer_url }}</td>
                  <td role="cell">{{ order.created_at | date: format: 'date' }}</td>
                  <td role="cell">{{ order.financial_status_label }}</td>
                  <td role="cell">{{ order.fulfillment_status_label }}</td>
                  <td role="cell">{{ order.total_price | money_with_currency }}</td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        {% endif %}
      </div>

      <p><a href="{{ routes.account_logout_url }}">{{ 'customers.account.logout' | t | default: 'Log out' }}</a></p>
    </div>
  </div>
</section>

{% style %}
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; } }

  #shopify-section-{{ section.id }} .account__inner { max-width: var(--page-width); margin: 0 auto; padding: 1.5rem 1rem; }
  @media (min-width: 990px) { #shopify-section-{{ section.id }} .account__inner { padding: 2rem 1.25rem; } }

  #shopify-section-{{ section.id }} .account__grid { display: grid; gap: 1.5rem; grid-template-columns: 1fr; }
  #shopify-section-{{ section.id }} .account__orders-table { width: 100%; border-collapse: collapse; }
  #shopify-section-{{ section.id }} .account__orders-table thead tr { text-align: left; border-bottom: 1px solid #e5e7eb; }
  #shopify-section-{{ section.id }} .account__orders-table tbody tr { border-bottom: 1px solid #f3f4f6; }
{% endstyle %}

{% schema %}
{
  "name": "Main account",
  "settings": [
    { "type": "header", "content": "Colors" },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "background-1" },
    { "type": "header", "content": "Layout" },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "presets": [ { "name": "Main account" } ]
}
{% endschema %}
