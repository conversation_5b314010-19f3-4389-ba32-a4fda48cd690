{% comment %}
  Shop by brand

  - Display brand logo cards as grid or carousel
  - Optional CTA button and descriptive copy
  - Carousel supports multi-row layout with snap scrolling and arrows
  - Layout controls for container width and offsets
{% endcomment %}

{%- liquid
  assign heading = section.settings.heading
  assign label = section.settings.label
  assign text = section.settings.text

  assign button_label = section.settings.button_label
  assign button_aria = section.settings.button_aria
  assign button_link = section.settings.button_link
  assign button_style = section.settings.button_style | default: 'primary'

  assign type = section.settings.type | default: 'grid'
  assign rows = section.settings.carousel_rows | default: 2

  assign container_width = section.settings.container_width | default: 'inherit'
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="shop-by-brand{% if section.settings.uniform_card_height %} sbb--uniform{% endif %}" data-section="{{ section.id }}">
  <div class="sbb__inner{% if container_width != 'inherit' %} sbb__inner--custom{% endif %} page-width"
       {% if container_width == 'narrow' %}style="--container-max: 90rem;"{% endif %}
       {% if container_width == 'wide' %}style="--container-max: 110rem;"{% endif %}
  >
    {% if label or heading or text or button_label %}
      <header class="sbb__header">
        <div class="sbb__header-left">
          {% if label %}<div class="sbb__eyebrow">{{ label }}</div>{% endif %}
          {% if heading %}<h2 class="sbb__title h2">{{ heading }}</h2>{% endif %}
          {% if text %}<div class="sbb__text rte">{{ text }}</div>{% endif %}
        </div>
        {% if button_label and button_link %}
          {% liquid
            assign cta_class = 'button'
            case button_style
              when 'primary'
                assign cta_class = 'button button--primary'
              when 'secondary'
                assign cta_class = 'button button--secondary'
              when 'link'
                assign cta_class = 'link'
            endcase
          %}
          <div class="sbb__header-right">
            <a class="sbb__cta {{ cta_class }}" href="{{ button_link }}"{% if button_aria %} aria-label="{{ button_aria | escape }}"{% endif %}>{{ button_label }}</a>
          </div>
        {% endif %}
      </header>
    {% endif %}

    {% assign has_blocks = false %}
    {% for block in section.blocks %}
      {% if block.type == 'brand' and block.settings.image %}{% assign has_blocks = true %}{% break %}{% endif %}
    {% endfor %}

    {% if has_blocks %}
      {% if type == 'carousel' %}
        <div class="sbb__carousel" style="--rows: {{ rows }}">
          <div class="sbb__nav" aria-hidden="true">
            <button class="sbb__nav-btn sbb__nav-btn--prev" type="button" aria-label="Previous" data-scroll-prev data-track="#BrandTrack-{{ section.id }}">‹</button>
            <button class="sbb__nav-btn sbb__nav-btn--next" type="button" aria-label="Next" data-scroll-next data-track="#BrandTrack-{{ section.id }}">›</button>
          </div>
          <div id="BrandTrack-{{ section.id }}" class="brand-track" role="list">
            {% for block in section.blocks %}
              {% if block.type == 'brand' and block.settings.image %}
                {% assign href = block.settings.link %}
                <div class="brand-item" role="listitem" {{ block.shopify_attributes }}>
                  {% if href %}
                    <a class="brand-card" href="{{ href }}" aria-label="{{ block.settings.image.alt | default: 'Brand' }}">
                      {{ block.settings.image | image_url: width: 240 | image_tag: widths: '140, 180, 240', sizes: '140px', alt: block.settings.image.alt | default: '' }}
                    </a>
                  {% else %}
                    <div class="brand-card" aria-label="{{ block.settings.image.alt | default: 'Brand' }}">
                      {{ block.settings.image | image_url: width: 240 | image_tag: widths: '140, 180, 240', sizes: '140px', alt: block.settings.image.alt | default: '' }}
                    </div>
                  {% endif %}
                </div>
              {% endif %}
            {% endfor %}
          </div>
        </div>
      {% else %}
        <ul class="brand-grid" role="list">
          {% for block in section.blocks %}
            {% if block.type == 'brand' and block.settings.image %}
              {% assign href = block.settings.link %}
              <li class="brand-grid__item" role="listitem" {{ block.shopify_attributes }}>
                {% if href %}
                  <a class="brand-card" href="{{ href }}" aria-label="{{ block.settings.image.alt | default: 'Brand' }}">
                    {{ block.settings.image | image_url: width: 320 | image_tag: widths: '160, 240, 320', sizes: '200px', alt: block.settings.image.alt | default: '' }}
                  </a>
                {% else %}
                  <div class="brand-card" aria-label="{{ block.settings.image.alt | default: 'Brand' }}">
                    {{ block.settings.image | image_url: width: 320 | image_tag: widths: '160, 240, 320', sizes: '200px', alt: block.settings.image.alt | default: '' }}
                  </div>
                {% endif %}
              </li>
            {% endif %}
          {% endfor %}
        </ul>
      {% endif %}
    {% else %}
      <p class="sbb__empty">Add brand blocks with images.</p>
    {% endif %}
  </div>
</section>

<script>
  (() => {
    const root = document.getElementById('shopify-section-{{ section.id }}');
    if (!root) return;
    const track = root.querySelector('#BrandTrack-{{ section.id }}');
    if (!track) return;

    const visible = 4; // approximate; arrows compute based on width
    const scrollByAmount = () => Math.ceil(track.clientWidth / Math.max(visible, 1));
    const update = () => {
      const maxScroll = track.scrollWidth - track.clientWidth - 1;
      const prev = root.querySelector('[data-scroll-prev][data-track="#BrandTrack-{{ section.id }}"]');
      const next = root.querySelector('[data-scroll-next][data-track="#BrandTrack-{{ section.id }}"]');
      if (prev) prev.disabled = track.scrollLeft <= 0;
      if (next) next.disabled = track.scrollLeft >= maxScroll;
    };
    const prevBtn = root.querySelector('[data-scroll-prev][data-track="#BrandTrack-{{ section.id }}"]');
    const nextBtn = root.querySelector('[data-scroll-next][data-track="#BrandTrack-{{ section.id }}"]');
    prevBtn?.addEventListener('click', () => { track.scrollBy({ left: -scrollByAmount(), behavior: 'smooth' }); });
    nextBtn?.addEventListener('click', () => { track.scrollBy({ left: scrollByAmount(), behavior: 'smooth' }); });
    track.addEventListener('scroll', update, { passive: true });
    window.addEventListener('resize', update);
    update();
  })();
</script>

{% style %}
  /* Offsets */
  #shopify-section-{{ section.id }} { margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px; }
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} { margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px; }
  }

  /* Section-scoped logo size variables */
  #shopify-section-{{ section.id }} .shop-by-brand {
    --logo-max-height-mobile: {{ section.settings.logo_max_height_mobile | default: 48 }}px;
    --logo-max-height-desktop: {{ section.settings.logo_max_height_desktop | default: 64 }}px;
    --card-min-height-mobile: {{ section.settings.card_height_mobile | default: 100 }}px;
    --card-min-height-desktop: {{ section.settings.card_height_desktop | default: 120 }}px;
  }

  .sbb__inner.page-width { max-width: var(--container-max, var(--page-width)); margin: 0 auto; padding-left: var(--page-margin, 1rem); padding-right: var(--page-margin, 1rem); }
  .sbb__header { display: grid; gap: .75rem; align-items: end; grid-template-columns: 1fr auto; margin-bottom: var(--heading-gap); }
  .sbb__eyebrow { font-size: .9rem; letter-spacing: .06em; text-transform: uppercase; opacity: .8; margin-bottom: .25rem; }
  .sbb__title { margin: 0; }
  .sbb__text { color: rgba(var(--color-foreground), .8); max-width: 60rem; }

  .brand-card { display: inline-flex; align-items: center; justify-content: center; width: 100%; min-height: 80px; padding: 1rem; border: 1px solid rgba(0,0,0,.12); border-radius: 8px; background: var(--color-background); color: inherit; text-decoration: none; }
  .sbb--uniform .brand-card { min-height: var(--card-min-height-mobile, 100px); }
  @media (min-width: 990px) { .sbb--uniform .brand-card { min-height: var(--card-min-height-desktop, 120px); } }
  .brand-card img { max-height: var(--logo-max-height-mobile, 48px); width: auto; height: auto; }
  @media (min-width: 990px) { .brand-card img { max-height: var(--logo-max-height-desktop, 64px); } }

  /* Grid */
  .brand-grid { display: grid; gap: 1rem; grid-template-columns: repeat(2, minmax(0, 1fr)); }
  @media (min-width: 750px) { .brand-grid { grid-template-columns: repeat(4, minmax(0, 1fr)); } }
  @media (min-width: 1200px) { .brand-grid { grid-template-columns: repeat(6, minmax(0, 1fr)); } }

  /* Carousel with rows */
  .sbb__carousel { position: relative; }
  .brand-track { display: grid; gap: 1rem; grid-auto-flow: column; grid-template-rows: repeat(var(--rows, 2), auto); overflow-x: auto; scroll-snap-type: x mandatory; padding: .25rem 0; }
  .brand-item { scroll-snap-align: start; }
  .sbb__nav { position: absolute; inset: 0; pointer-events: none; display: flex; justify-content: space-between; align-items: center; }
  .sbb__nav-btn { pointer-events: auto; border: 1px solid rgba(0,0,0,.2); background: rgba(255,255,255,.9); color: inherit; border-radius: 999px; width: 32px; height: 32px; line-height: 30px; text-align: center; font-size: 18px; cursor: pointer; }
  .sbb__nav-btn[disabled] { opacity: .4; cursor: default; }

  .sbb__empty { opacity: .7; }
{% endstyle %}

{% schema %}
{
  "name": "Shop by brand",
  "settings": [
    { "type": "header", "content": "General" },
    { "type": "text", "id": "heading", "label": "Heading", "default": "Shop by brand" },
    { "type": "text", "id": "label", "label": "Label" },
    { "type": "richtext", "id": "text", "label": "Text" },
    { "type": "text", "id": "button_label", "label": "Button label" },
    { "type": "text", "id": "button_aria", "label": "Button aria-label" },
    { "type": "url", "id": "button_link", "label": "Button link" },
    { "type": "select", "id": "button_style", "label": "Button style", "default": "primary", "options": [
      { "value": "primary", "label": "Primary" },
      { "value": "secondary", "label": "Secondary" },
      { "value": "link", "label": "Link" }
    ] },
    { "type": "select", "id": "type", "label": "Type", "default": "grid", "options": [
      { "value": "grid", "label": "Grid" },
      { "value": "carousel", "label": "Carousel" }
    ] },
    { "type": "range", "id": "carousel_rows", "label": "Carousel row count", "min": 1, "max": 4, "step": 1, "default": 2 },

    { "type": "header", "content": "Logos" },
    { "type": "range", "id": "logo_max_height_mobile", "label": "Logo max height (mobile)", "min": 24, "max": 120, "step": 2, "default": 48 },
    { "type": "range", "id": "logo_max_height_desktop", "label": "Logo max height (desktop)", "min": 24, "max": 160, "step": 2, "default": 64 },
    { "type": "checkbox", "id": "uniform_card_height", "label": "Uniform card height", "default": false },
    { "type": "range", "id": "card_height_mobile", "label": "Card min height (mobile)", "min": 60, "max": 240, "step": 5, "default": 100 },
    { "type": "range", "id": "card_height_desktop", "label": "Card min height (desktop)", "min": 80, "max": 320, "step": 5, "default": 120 },

    { "type": "header", "content": "Layout" },
    {
      "type": "select",
      "id": "container_width",
      "label": "Container width",
      "default": "inherit",
      "options": [
        { "value": "inherit", "label": "Inherit" },
        { "value": "narrow", "label": "Narrow" },
        { "value": "wide", "label": "Wide" }
      ]
    },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "blocks": [
    {
      "type": "brand",
      "name": "Brand",
      "limit": 24,
      "settings": [
        { "type": "image_picker", "id": "image", "label": "Image" },
        { "type": "url", "id": "link", "label": "Image link" }
      ]
    }
  ],
  "presets": [
    { "name": "Shop by brand", "blocks": [ { "type": "brand" }, { "type": "brand" }, { "type": "brand" }, { "type": "brand" } ] }
  ]
}
{% endschema %}
