/* Simple Gap Fix - Make announcement bar taller on desktop to cover the gap */

/* Increase announcement bar height on desktop to match the 3rem margin gap */
@media (min-width: 990px) {
  .announcement-bar__inner {
    padding: 3rem 0 !important; /* Matches the 3rem margin gap */
  }
}

/* Optional: Luxury header styling for overlay mode */
.header--overlay .header.site-header {
  background: transparent !important;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.header--overlay .header.site-header.is-fixed {
  background: rgba(255, 255, 255, 0.95) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Remove top margin/padding from blog pages */
.template-blog .main-content {
  margin-top: 0;
  padding-top: 0;
}

.template-blog .blog-section,
.template-blog [data-section-type="blog"] {
  margin-top: 0;
  padding-top: 0;
}

/* Remove top margin from first headings */
.template-blog h1:first-child,
.template-index h1:first-child {
  margin-top: 0;
  padding-top: 0;
}

/* Ensure no gap from any container elements */
.container:first-child,
.page-width:first-child {
  margin-top: 0;
  padding-top: 0;
}

/* Remove any default spacing from section wrappers */
.template-index .section:first-child,
.template-blog .section:first-child {
  margin-top: 0;
  padding-top: 0;
}
