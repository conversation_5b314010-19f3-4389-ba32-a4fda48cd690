{%- comment -%}
  Search Facets/Filters Snippet

  - Uses search.filters (Search & Discovery) to render filters on search results
  - Product-only search expected (type=product)
  - Optional color swatch presentation for color-like filters
{%- endcomment -%}

{%- liquid
  assign show_swatches = show_swatches | default: true
  assign current_terms = search.terms | escape
-%}

<form class="facets facets--search" action="{{ routes.search_url }}" method="get" id="SearchFacetsForm">
  <input type="hidden" name="q" value="{{ current_terms }}">
  <input type="hidden" name="type" value="product">

  {%- if search.filters != empty -%}
    {%- for filter in search.filters -%}
      <details class="facet" {% if filter.active_values.size > 0 %}open{% endif %}>
        <summary class="facet__summary">
          <span class="facet__label">{{ filter.label | escape }}</span>
          {% render 'icon-caret' %}
        </summary>

        <div class="facet__content">
          {%- case filter.type -%}
            {%- when 'list' -%}
              <ul class="facet__list" role="list">
                {%- assign is_color = false -%}
                {%- assign fl = filter.label | downcase -%}
                {%- if fl contains 'color' or fl contains 'colour' -%}
                  {%- assign is_color = true -%}
                {%- endif -%}
                {%- for value in filter.values -%}
                  <li class="facet__item">
                    <input
                      type="checkbox"
                      name="{{ value.param_name }}"
                      value="{{ value.value }}"
                      id="Facet-{{ filter.param_name }}-{{ forloop.index }}"
                      {% if value.active %}checked{% endif %}
                      {% if value.count == 0 and value.active == false %}disabled{% endif %}
                    >
                    <label for="Facet-{{ filter.param_name }}-{{ forloop.index }}" class="facet__label">
                      {%- if show_swatches and is_color -%}
                        {%- assign handle = value.label | handleize -%}
                        <span class="swatch swatch--{{ handle }}" aria-hidden="true"></span>
                      {%- endif -%}
                      <span class="facet__text">{{ value.label | escape }}</span>
                      {% if value.count %}<span class="facet__count">({{ value.count }})</span>{% endif %}
                    </label>
                  </li>
                {%- endfor -%}
              </ul>

            {%- when 'price_range' -%}
              <div class="facet__price">
                <div class="field">
                  <input
                    type="number"
                    name="{{ filter.min_value.param_name }}"
                    id="Facet-{{ filter.label | handle }}-GTE"
                    {%- if filter.min_value.value -%}
                      value="{{ filter.min_value.value | money_without_currency | replace: ',', '' }}"
                    {%- endif -%}
                    placeholder="0"
                    min="0"
                    max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                  >
                  <label for="Facet-{{ filter.label | handle }}-GTE">{{ 'products.facets.from' | t }}</label>
                </div>
                <div class="field">
                  <input
                    type="number"
                    name="{{ filter.max_value.param_name }}"
                    id="Facet-{{ filter.label | handle }}-LTE"
                    {%- if filter.max_value.value -%}
                      value="{{ filter.max_value.value | money_without_currency | replace: ',', '' }}"
                    {%- endif -%}
                    placeholder="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                    min="0"
                    max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                  >
                  <label for="Facet-{{ filter.label | handle }}-LTE">{{ 'products.facets.to' | t }}</label>
                </div>
              </div>
          {%- endcase -%}
        </div>
      </details>
    {%- endfor -%}
  {%- endif -%}

  <div class="facets__actions">
    <button type="submit" class="button">Apply</button>
    <a href="{{ routes.search_url }}?q={{ current_terms | url_encode }}&type=product" class="button button--secondary">Clear</a>
  </div>
</form>

<script>
  // Progressive enhancement: auto-submit on change
  (function(){
    const form = document.getElementById('SearchFacetsForm');
    if (!form) return;
    form.addEventListener('change', function(){
      const url = new URL(form.action, window.location.origin);
      const formData = new FormData(form);
      const params = new URLSearchParams(formData);
      url.search = params.toString();
      window.location.href = url.toString();
    });
  })();
</script>

{% style %}
  .facets--search { display: grid; gap: .75rem; }
  .facet { border: 1px solid rgba(var(--color-foreground), .15); border-radius: 8px; }
  .facet__summary { display:flex; align-items:center; justify-content: space-between; gap:.5rem; padding:.5rem .75rem; cursor:pointer; }
  .facet__content { padding: .5rem .75rem 1rem; }
  .facet__list { list-style: none; padding: 0; margin: 0; display: grid; gap: .25rem; }
  .facet__item { display: flex; align-items: center; gap: .5rem; }
  .facet__label { display: inline-flex; align-items: center; gap: .5rem; cursor: pointer; }
  .facet__count { color: rgba(var(--color-foreground), .6); font-size: .9em; }
  .facet__price { display:grid; grid-template-columns: 1fr 1fr; gap:.5rem; }
  .field input[type="number"] { width: 100%; padding: .5rem; border: 1px solid rgba(var(--color-foreground), .2); border-radius: 4px; }

  /* Simple swatch dot */
  .swatch { width: 16px; height: 16px; border-radius: 50%; border: 1px solid rgba(0,0,0,.1); display: inline-block; }

  .facets__actions { display:flex; gap: .5rem; align-items:center; }
{% endstyle %}

