{% comment %}
  Callout Banner Section
  - High-impact banner with focused messaging
  - Configurable height, alignment, overlay, and color schemes
  - Optional background image with responsive loading
  - Supports rich text content and optional CTA button
{% endcomment %}

{{ 'section-callout-banner.css' | asset_url | stylesheet_tag }}

<section
  id="CalloutBanner-{{ section.id }}"
  class="callout-banner color-{{ section.settings.color_scheme }} callout-banner--height-{{ section.settings.banner_height }} callout-banner--align-{{ section.settings.content_alignment }} callout-banner--width-{{ section.settings.content_width }} callout-banner--image-{{ section.settings.image_position }} {% if section.settings.image != blank or section.settings.show_overlay %}callout-banner--has-overlay{% endif %} full-width"
  style="--overlay-opacity: {{ section.settings.overlay_opacity | divided_by: 100.0 }};"
  role="region"
  aria-label="{{ section.settings.heading | strip_html | escape | default: 'Callout banner' }}"
>
  {%- if section.settings.image != blank -%}
    <div class="callout-banner__media{% if section.settings.enable_image_zoom %} zoom-on-scroll{% endif %}"{% if section.settings.enable_image_zoom %} data-zoom-on-scroll{% endif %}{% if section.settings.enable_image_reveal %} data-reveal-on-scroll{% endif %}>
      {{ section.settings.image
        | image_url: width: 3840
        | image_tag:
          widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840',
          sizes: '(min-width: 1200px) 1200px, 100vw',
          class: 'callout-banner__image',
          alt: '',
          loading: 'lazy',
          decoding: 'async'
      }}
    </div>
  {%- endif -%}

  {%- if section.settings.show_overlay -%}
    <div class="callout-banner__overlay" aria-hidden="true"{% if section.settings.enable_image_reveal %} data-reveal-on-scroll{% endif %}></div>
  {%- endif -%}

  <div class="callout-banner__inner {% if section.settings.content_width == 'boxed' %}page-width{% endif %}">
    <div class="callout-banner__content">
      {%- if section.settings.heading != blank -%}
        <h2 class="callout-banner__heading">{{ section.settings.heading }}</h2>
      {%- endif -%}
      {%- if section.settings.subheading != blank -%}
        <div class="callout-banner__subheading">{{ section.settings.subheading }}</div>
      {%- endif -%}
      {%- if section.settings.button_label != blank and section.settings.button_link != blank -%}
        <a class="button callout-banner__button" href="{{ section.settings.button_link }}">{{ section.settings.button_label | escape }}</a>
      {%- endif -%}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Callout banner",
  "tag": "section",
  "class": "section",
  "settings": [
    { "type": "checkbox", "id": "enable_image_reveal", "label": "Enable image reveal-on-scroll", "default": true },
    { "type": "checkbox", "id": "enable_image_zoom", "label": "Enable image zoom-on-scroll", "default": true },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "richtext",
      "id": "heading",
      "label": "Heading",
      "default": "<p>Make a statement with a <strong>callout banner</strong></p>"
    },
    {
      "type": "richtext",
      "id": "subheading",
      "label": "Subheading",
      "info": "Optional supporting text"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button label",
      "info": "Leave blank to hide button"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "header",
      "content": "Layout & style"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Background image"
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "Image position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "top",
      "info": "Controls which part of the image is visible"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    },
    {
      "type": "select",
      "id": "banner_height",
      "label": "Banner height",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "content_width",
      "label": "Content width",
      "options": [
        {
          "value": "full",
          "label": "Full width"
        },
        {
          "value": "boxed",
          "label": "Boxed"
        }
      ],
      "default": "boxed"
    },
    {
      "type": "radio",
      "id": "content_alignment",
      "label": "Content alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "checkbox",
      "id": "show_overlay",
      "label": "Show overlay",
      "default": true,
      "info": "Improves text readability over images"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "min": 0,
      "max": 90,
      "step": 5,
      "unit": "%",
      "label": "Overlay opacity",
      "default": 40
    }
  ],
  "presets": [
    {
      "name": "Callout banner"
    }
  ]
}
{% endschema %}
