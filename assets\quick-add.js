/* Quick Add modal: fetch product, render variant options, add to cart. */
(() => {
  const modal = document.getElementById('quick-add-modal');
  const body  = modal?.querySelector('[data-body]');
  if (!modal || !body) return;

  const $ = (s, r=document) => r.querySelector(s);
  const $$ = (s, r=document) => Array.from(r.querySelectorAll(s));

  // Minimal focus trap
  function trap(dialog) {
    const get = () =>
      $$('a,button,input,select,textarea,[tabindex]:not([tabindex="-1"])', dialog)
        .filter(el => !el.disabled && el.offsetParent !== null);
    function onKey(e) {
      if (e.key !== 'Tab') return;
      const f = get(); if (!f.length) return;
      const first = f[0], last = f[f.length-1];
      if (e.shiftKey && document.activeElement === first) { e.preventDefault(); last.focus(); }
      else if (!e.shiftKey && document.activeElement === last) { e.preventDefault(); first.focus(); }
    }
    dialog.addEventListener('keydown', onKey);
    dialog.addEventListener('close', () => dialog.removeEventListener('keydown', onKey), { once:true });
  }

  async function fetchProduct(url) {
    const res = await fetch(`${url}.js`, { credentials: 'same-origin' });
    if (!res.ok) throw new Error('Product fetch failed');
    return res.json();
  }

  // HTML template for the modal body
  function renderProduct(p, preselectedId) {
    const byId = new Map(p.variants.map(v => [String(v.id), v]));
    let variant = preselectedId ? byId.get(String(preselectedId)) : p.variants.find(v => v.available) || p.variants[0];

    const optionFields = p.options.map((name, i) => {
      const values = Array.from(new Set(p.variants.map(v => v.options[i])));
      const current = variant.options[i];
      return `
        <fieldset class="qa-fieldset">
          <legend>${name}</legend>
          <div class="qa-options" data-opt="${i}">
            ${values.map(v => `
              <label class="qa-chip">
                <input type="radio" name="opt-${i}" value="${v}" ${v===current?'checked':''}>
                <span>${v}</span>
              </label>
            `).join('')}
          </div>
        </fieldset>`;
    }).join('');

    const showCompare = (typeof window !== 'undefined' && window.themeSettings && window.themeSettings.show_compare_price) ? window.themeSettings.show_compare_price : true;
    const priceHtml = `
      <div class="qa-price">
        ${showCompare && variant.compare_at_price ? `<s>${formatCents(variant.compare_at_price, p)}</s>` : ''}
        <strong>${formatCents(variant.price, p)}</strong>
      </div>`;

    return `
      <div class="qa-header"><h2 class="qa-title">${p.title}</h2></div>
      <div class="qa-grid">
        <div class="qa-media">
          <img alt="" loading="eager" decoding="async"
               src="${variant.featured_image?.src || p.images[0] || ''}">
        </div>
        <div class="qa-form">
          ${optionFields}
          ${priceHtml}
          <div class="qa-qty"><label>Quantity <input type="number" min="1" value="1" inputmode="numeric"></label></div>
          <button class="button qa-add" data-vid="${variant.id}" ${variant.available?'':'disabled'}>
            <span class="qa-add__text">${variant.available ? 'Add to cart' : 'Sold out'}</span>
            <div class="loading-overlay hidden">
              <div class="loading-overlay__spinner">
                <svg aria-hidden="true" focusable="false" class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                  <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                </svg>
              </div>
            </div>
          </button>
          <div class="qa-status" aria-live="polite"></div>
        </div>
      </div>
      <script type="application/json" data-variants>${JSON.stringify(p.variants)}</script>
    `;
  }

  function formatCents(cents, p) {
    const iso = p?.price_min_currency || 'USD';
    return (cents/100).toLocaleString(undefined, { style:'currency', currency: iso });
  }

  function resolveVariant(variants, selections) {
    return variants.find(v => v.options.every((val, idx) => val === selections[idx]));
  }

  async function addToCart(variantId, qty=1) {
    const res = await fetch('/cart/add.js', {
      method: 'POST',
      headers: { 'Content-Type':'application/json', 'Accept':'application/json' },
      body: JSON.stringify({ items: [{ id: Number(variantId), quantity: Number(qty) }] })
    });
    const json = await res.json();
    if (!res.ok) throw new Error(json.description || 'Add to cart failed');
    return json;
  }

  // Show/hide loading state
  function setLoadingState(button, isLoading) {
    if (isLoading) {
      button.setAttribute('aria-busy', 'true');
      const overlay = button.querySelector('.loading-overlay');
      const text = button.querySelector('.quick-add__text, .qa-add__text');
      if (overlay) overlay.classList.remove('hidden');
      if (text) text.style.opacity = '0';
    } else {
      button.removeAttribute('aria-busy');
      const overlay = button.querySelector('.loading-overlay');
      const text = button.querySelector('.quick-add__text, .qa-add__text');
      if (overlay) overlay.classList.add('hidden');
      if (text) text.style.opacity = '1';
    }
  }

  // Global click handler for triggers
  document.addEventListener('click', async (e) => {
    const trigger = e.target.closest('[data-quick-add]');
    if (!trigger) return;
    e.preventDefault();

    const productUrl = trigger.dataset.productUrl;
    const productId = trigger.dataset.productId;
    const direct = trigger.dataset.direct === 'true';

    // Check if a color variant is selected on this card (wrapper owns the dataset)
    const cardRoot = trigger.closest('[data-product-card]') || trigger.closest('.card');
    const selectedVariantId = cardRoot?.dataset.selectedVariant;

    // Enhanced debug logging
    console.log('Quick Add clicked:', {
      trigger,
      cardRoot,
      direct,
      selectedVariantId,
      cardDataset: cardRoot?.dataset,
      allDataAttributes: cardRoot ? Object.keys(cardRoot.dataset) : 'No card root',
      selectedVariantFromDataset: cardRoot?.dataset?.selectedVariant,
      hasSelectedVariantAttribute: cardRoot?.hasAttribute('data-selected-variant')
    });

    // If a specific variant is selected, we can do direct add regardless of product complexity
    if (selectedVariantId) {
      try {
        setLoadingState(trigger, true);
        const p = await fetchProduct(productUrl);
        const selectedVariant = p.variants.find(variant => variant.id == selectedVariantId);
        
        console.log('Variant lookup:', {
          selectedVariantId,
          productVariants: p.variants.map(v => ({ 
            id: v.id, 
            available: v.available, 
            title: v.title,
            inventory_quantity: v.inventory_quantity 
          })),
          selectedVariant,
          selectedVariantAvailable: selectedVariant?.available,
          selectedVariantTitle: selectedVariant?.title
        });
        
        if (selectedVariant && selectedVariant.available) {
          console.log('✅ Adding selected variant to cart:', selectedVariant.title);
          await addToCart(selectedVariant.id, 1);
          setLoadingState(trigger, false);
          document.dispatchEvent(new CustomEvent('cart:updated'));
          return;
        } else {
          // Fallback to modal if selected variant is not available
          console.log('❌ Selected variant unavailable, opening modal:', {
            variantExists: !!selectedVariant,
            variantTitle: selectedVariant?.title,
            variantAvailable: selectedVariant?.available
          });
        }
      } catch (err) {
        setLoadingState(trigger, false);
        console.error('Direct add failed:', err);
        alert('Could not add to cart.');
        return;
      }
    }

    // Direct add path only when no explicit selection exists
    if (direct && !selectedVariantId) {
      try {
        setLoadingState(trigger, true);
        const p = await fetchProduct(productUrl);
        const v = p.variants.find(v => v.available) || p.variants[0];
        await addToCart(v.id, 1);
        setLoadingState(trigger, false);
        document.dispatchEvent(new CustomEvent('cart:updated'));
      } catch (err) {
        setLoadingState(trigger, false);
        console.error(err);
        alert('Could not add to cart.');
      }
      return;
    }

    // Modal path
    body.innerHTML = '<div class="qa-loading">Loading…</div>';
    if (modal.showModal) modal.showModal(); else modal.setAttribute('open','');
    trap(modal);

    try {
      const p = await fetchProduct(productUrl);
      // Pass selected variant ID to the modal
      body.innerHTML = renderProduct(p, selectedVariantId);

      const variants = JSON.parse($('[data-variants]', body).textContent);
      const form     = $('.qa-form', body);
      const addBtn   = $('.qa-add', form);
      const qty      = $('.qa-qty input', form);
      const img      = $('.qa-media img', body);
      const status   = $('.qa-status', form);

      function selections() {
        return $$('.qa-options', form).map(g => g.querySelector('input:checked')?.value);
      }

      function syncVariant() {
        const v = resolveVariant(variants, selections());
        if (!v) { 
          addBtn.disabled = true; 
          status.textContent = 'Unavailable'; 
          const textEl = addBtn.querySelector('.qa-add__text');
          if (textEl) textEl.textContent = 'Unavailable';
          return; 
        }
        addBtn.disabled = !v.available; 
        addBtn.dataset.vid = v.id;
        status.textContent = v.available ? '' : 'Sold out';
        const textEl = addBtn.querySelector('.qa-add__text');
        if (textEl) textEl.textContent = v.available ? 'Add to cart' : 'Sold out';
        if (v.featured_image?.src) img.src = v.featured_image.src;
        $('.qa-price', form)?.remove();
  const showCompare = (typeof window !== 'undefined' && window.themeSettings && window.themeSettings.show_compare_price) ? window.themeSettings.show_compare_price : true;
  const priceNode = document.createElement('div');
  priceNode.className = 'qa-price';
  priceNode.innerHTML = `${showCompare && v.compare_at_price ? `<s>${formatCents(v.compare_at_price, p)}</s>` : ''} <strong>${formatCents(v.price, p)}</strong>`;
        form.insertBefore(priceNode, $('.qa-qty', form));
      }

      form.addEventListener('change', (ev) => {
        if (ev.target.matches('input[type="radio"]')) syncVariant();
      });

      addBtn.addEventListener('click', async () => {
        setLoadingState(addBtn, true);
        try {
          await addToCart(addBtn.dataset.vid, qty.value || 1);
          setLoadingState(addBtn, false);
          modal.close();
          document.dispatchEvent(new CustomEvent('cart:updated'));
        } catch (err) {
          setLoadingState(addBtn, false);
          status.textContent = 'Could not add to cart.';
        }
      });

      // Close handlers
      modal.addEventListener('click', (ev) => { if (ev.target.hasAttribute('data-close')) modal.close(); });
      $('[data-close]', modal).addEventListener('click', () => modal.close());
    } catch (err) {
      console.error(err);
      body.innerHTML = '<div class="qa-error">Could not load product. Please open the product page.</div>';
    }
  });
})();
