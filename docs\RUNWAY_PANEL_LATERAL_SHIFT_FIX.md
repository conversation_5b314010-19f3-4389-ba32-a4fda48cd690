# Runway Panel Lateral Shift Fix

## Problem

The sticky panel exhibited a tiny lateral "jump" when transitioning between positioning states:

- **Before fixed (natural/released):** `position: absolute` with `right` measured against the **product section**
- **When fixed:** `position: fixed` with `right` measured against the **viewport edge**

Even after compensating for page padding, browsers can round `vw`/`%` math differently when flipping between these two reference boxes, causing sub-pixel shifts.

## Root Cause

The classic "two different reference boxes + sub-pixel rounding" issue has two components:

1. **Different containing blocks:**
   - **Absolute positioning:** Containing block = `.product` section
   - **Fixed positioning:** Containing block = viewport
   - Using percentage-based values (like `clamp(12px, 2.5vw, 22px)`) produces different pixel values

2. **Width reflow:**
   - When the rail becomes fixed, its width can subtly change due to reflow
   - This compounds the positioning shift by 1-2 pixels

## Solution

Two-part fix:

1. **Measure the rail's inline-end gap in pixels** and use the *same* value for all three states
2. **Lock the rail's width in pixels** while it's fixed to prevent reflow changes

### 1. CSS Changes

All three states use the same `--rail-right` variable (measured in pixels by JavaScript), plus guards against positioning and paint issues:

```css
/* Natural state (absolute) */
.product--layout-runway > .product__info,
.product[data-layout="stack"] > .product__info {
  position: absolute !important;
  /* Use pixel-measured right offset (set by JS) */
  right: var(--rail-right, var(--pi-edge-gap, clamp(12px, 2.5vw, 22px))) !important;
  /* Never inherit unexpected left positioning */
  left: auto !important;
  /* Guard against sub-pixel paint jitter */
  transform: translateZ(0);
  box-sizing: border-box;
}

/* Fixed state */
.product--layout-runway > .product__info.is-fixed,
.product[data-layout="stack"] > .product__info.is-fixed {
  position: fixed !important;
  /* Use the same pixel-measured right offset */
  right: var(--rail-right, var(--pi-edge-gap, clamp(12px, 2.5vw, 22px))) !important;
  /* Width is locked in pixels by JS to prevent reflow changes */
}

/* Released state (absolute) */
.product--layout-runway > .product__info.is-released,
.product[data-layout="stack"] > .product__info.is-released {
  position: absolute !important;
  /* Use the same pixel-measured right offset */
  right: var(--rail-right, var(--pi-edge-gap, clamp(12px, 2.5vw, 22px))) !important;
  /* Width is fluid again (JS clears inline width) */
}
```

### 2. JavaScript Changes

Measure the actual gap (in pixels) and lock width when fixed:

```javascript
// Helper to read a CSS variable in px
const readPxVar = (el, name, fallback = 0) => {
  const v = getComputedStyle(el).getPropertyValue(name);
  const n = parseFloat(v);
  return isNaN(n) ? fallback : n;
};

// Desired visual gap when nothing special is set (matches CSS fallback)
const getEdgeGap = () => {
  // Try section-local --pi-edge-gap, then fall back to a sensible default
  const local = readPxVar(sectionRoot, '--pi-edge-gap', NaN);
  if (!isNaN(local)) return local;
  // ≈ clamp(12px, 2.5vw, 22px) – approximate with 2.5vw bounded to [12,22]
  return Math.max(12, Math.min(22, Math.round(window.innerWidth * 0.025)));
};

// Measure pixel-perfect right offset based on section edge
const setRailRight = () => {
  // Section's box in the viewport
  const rect = sectionRoot.getBoundingClientRect();
  const gap = getEdgeGap();

  // Distance from section's right edge to viewport's right edge
  // This is what makes "absolute right" equal to "fixed right"
  const inset = Math.max(0, Math.round(window.innerWidth - rect.right + gap));

  sectionRoot.style.setProperty('--rail-right', inset + 'px');
};

// Lock width in pixels while fixed to prevent reflow changes
const lockWidthIfFixed = () => {
  const isFixed = infoWrap.classList.contains('is-fixed');
  if (isFixed) {
    const w = Math.round(infoWrap.getBoundingClientRect().width);
    // Only write if changed to avoid style churn
    if (infoWrap.style.width !== w + 'px') infoWrap.style.width = w + 'px';
  } else {
    // Let it be fluid again when absolute (released/initial)
    if (infoWrap.style.width) infoWrap.style.width = '';
  }
};

// Combined refresh function
const refreshRailPosition = () => {
  setRailRight();
  lockWidthIfFixed();
};

// Watch for class changes (fixed <-> released)
if ('MutationObserver' in window) {
  const classObserver = new MutationObserver(refreshRailPosition);
  classObserver.observe(infoWrap, { attributes: true, attributeFilter: ['class'] });
}

// Update on resize, orientation change, and visual viewport changes
window.addEventListener('resize', refreshRailPosition, { passive: true });
window.addEventListener('orientationchange', refreshRailPosition, { passive: true });
if (window.visualViewport) {
  visualViewport.addEventListener('resize', refreshRailPosition, { passive: true });
  visualViewport.addEventListener('scroll', refreshRailPosition, { passive: true });
}

// Update on header height changes
document.addEventListener('runway:header-height', refreshRailPosition);

// If rail content can expand (accordion, errors), remeasure
if ('ResizeObserver' in window) {
  new ResizeObserver(refreshRailPosition).observe(infoWrap);
}

// Initial pass + retries to catch late layout
requestAnimationFrame(refreshRailPosition);
setTimeout(refreshRailPosition, 80);
setTimeout(refreshRailPosition, 300);
```

## Why This Works

1. **Identical right offset:** We avoid `100vw` vs `100%` vs negative margins entirely. By measuring the actual gap (in pixels) based on the section's real edge and reusing it identically for all three states, there's no rounding mismatch.

2. **Width pinning:** When the rail becomes fixed, we lock its width in pixels. This prevents subtle reflow changes that can shift the rail by 1-2 pixels when it detaches from the section.

3. **Comprehensive updates:** The solution watches for:
   - Class changes (fixed ↔ released) via MutationObserver
   - Window resize and orientation changes
   - Visual viewport changes (iOS URL bar show/hide)
   - Rail content changes via ResizeObserver
   - Header height changes via custom event

4. **Safe measurement:** We measure the right offset based on the section's actual edge, not assumptions about container padding/margins, making it work with flexible themes.

### State Alignment

| State | Position | Containing Block | Right Value | Width | Final Position |
|-------|----------|------------------|-------------|-------|----------------|
| Natural | `absolute` | `.product` section | `--rail-right` (px) | Fluid | Exact pixel offset |
| Fixed | `fixed` | Viewport | `--rail-right` (px) | **Locked (px)** | Exact pixel offset |
| Released | `absolute` | `.product` section | `--rail-right` (px) | Fluid | Exact pixel offset |

All three states use the **same pixel right value**, and the fixed state has its **width locked** to prevent reflow changes.

## Scrollbar Considerations

The theme uses `html { scrollbar-gutter: stable both-edges; }` to reserve space for the scrollbar on both sides. This prevents 1-2px nudges on Windows when the scrollbar appears/disappears during page load or content changes.

**Why `both-edges`?**

- Prevents viewport width changes when scrollbar appears
- Eliminates lateral shifts during initial paint/font load
- Small trade-off: adds symmetric padding on both sides
- Essential for pixel-perfect alignment across all states

## Testing

To verify the fix:

1. Open a product page with runway layout on desktop (>990px)
2. Scroll down slowly and watch the panel transition from natural → fixed
3. Continue scrolling until the panel releases
4. The panel should stay perfectly aligned to the viewport edge throughout all transitions
5. No lateral shift or "jump" should be visible

### Debug Commands

```javascript
// Check current state
const rail = document.querySelector('.product__info');
console.log('Fixed:', rail.classList.contains('is-fixed'));
console.log('Released:', rail.classList.contains('is-released'));

// Check computed margin
console.log('Margin-right:', getComputedStyle(rail).marginRight);

// Check page margin variable
const section = document.querySelector('.product');
console.log('Page margin:', getComputedStyle(section).getPropertyValue('--page-margin'));
```

## Files Modified

- `sections/product.liquid` (lines ~205-229): Updated CSS to use `--rail-right` variable with positioning guards
- `sections/product.liquid` (lines ~3433-3471): Added `getEdgeGap()`, `setRailRight()`, `lockWidthIfFixed()`, and `refreshRailPosition()` functions
- `sections/product.liquid` (lines ~3495-3540): Updated event listeners with MutationObserver for class changes and comprehensive resize handling

## Related Documentation

- `docs/RUNWAY_STICKY_RAIL_IMPLEMENTATION.md` - Full sticky rail implementation
- `docs/RUNWAY_STICKY_RAIL_QUICK_REFERENCE.md` - Quick reference guide
- `docs/RUNWAY_PANEL_FINAL_SOLUTION.md` - Panel height solution

---

**Status:** ✅ Implemented
**Date:** 2025-10-08
**Impact:** Visual polish - eliminates lateral shift during sticky transitions
