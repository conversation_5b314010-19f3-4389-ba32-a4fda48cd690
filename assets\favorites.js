/**
 * Favorites Functionality for Product Cards
 * Handles adding/removing products from localStorage favorites
 * Provides visual feedback and manages state
 */

class FavoritesManager {
  constructor() {
    this.storageKey = 'shopify-favorites';
    this.favorites = this.getFavorites();
    this.init();
  }

  init() {
    // Initialize favorites on page load
    this.updateFavoritesDisplay();
    
    // Add event listeners for favorite buttons
    document.addEventListener('click', (e) => {
      if (e.target.closest('[data-favorite-toggle]')) {
        e.preventDefault();
        e.stopPropagation();
        this.handleFavoriteToggle(e.target.closest('[data-favorite-toggle]'));
      }
    });

    // Listen for storage changes from other tabs
    window.addEventListener('storage', (e) => {
      if (e.key === this.storageKey) {
        this.favorites = this.getFavorites();
        this.updateFavoritesDisplay();
      }
    });
  }

  /**
   * Get favorites from localStorage
   * @returns {Array} Array of favorite product objects
   */
  getFavorites() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Error reading favorites from localStorage:', error);
      return [];
    }
  }

  /**
   * Save favorites to localStorage
   * @param {Array} favorites - Array of favorite products
   */
  saveFavorites(favorites) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(favorites));
      this.favorites = favorites;
      
      // Dispatch custom event for other components
      window.dispatchEvent(new CustomEvent('favorites:updated', {
        detail: { favorites: this.favorites }
      }));
    } catch (error) {
      console.warn('Error saving favorites to localStorage:', error);
    }
  }

  /**
   * Check if a product is in favorites
   * @param {string|number} productId - Product ID to check
   * @returns {boolean}
   */
  isFavorite(productId) {
    return this.favorites.some(fav => fav.id == productId);
  }

  /**
   * Add product to favorites
   * @param {Object} productData - Product data object
   */
  addToFavorites(productData) {
    if (!this.isFavorite(productData.id)) {
      const newFavorites = [...this.favorites, {
        id: productData.id,
        handle: productData.handle,
        title: productData.title,
        url: productData.url,
        dateAdded: new Date().toISOString()
      }];
      this.saveFavorites(newFavorites);
      
      // Show success feedback
      this.showFeedback(productData.title, 'added');
    }
  }

  /**
   * Remove product from favorites
   * @param {string|number} productId - Product ID to remove
   */
  removeFromFavorites(productId) {
    const product = this.favorites.find(fav => fav.id == productId);
    const newFavorites = this.favorites.filter(fav => fav.id != productId);
    this.saveFavorites(newFavorites);
    
    // Show removal feedback
    if (product) {
      this.showFeedback(product.title, 'removed');
    }
  }

  /**
   * Toggle favorite status for a product
   * @param {HTMLElement} button - The favorite button element
   */
  handleFavoriteToggle(button) {
    const productId = button.dataset.productId;
    const productHandle = button.dataset.productHandle;
    const productTitle = button.dataset.productTitle;
    
    if (!productId || !productHandle || !productTitle) {
      console.warn('Missing product data for favorites toggle');
      return;
    }

    const productData = {
      id: productId,
      handle: productHandle,
      title: productTitle,
      url: `/products/${productHandle}`
    };

    // Add haptic feedback on supported devices
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }

    if (this.isFavorite(productId)) {
      this.removeFromFavorites(productId);
    } else {
      this.addToFavorites(productData);
    }

    // Update display immediately
    this.updateFavoritesDisplay();
  }

  /**
   * Update the visual state of all favorite buttons
   */
  updateFavoritesDisplay() {
    const favoriteButtons = document.querySelectorAll('[data-favorite-toggle]');
    
    favoriteButtons.forEach(button => {
      const productId = button.dataset.productId;
      const isFavorited = this.isFavorite(productId);
      
      button.setAttribute('data-favorited', isFavorited);
      button.setAttribute('aria-pressed', isFavorited);
      
      // Update aria-label for accessibility
      const productTitle = button.dataset.productTitle;
      const action = isFavorited ? 'remove_from_favorites' : 'add_to_favorites';
      button.setAttribute('aria-label', 
        `${action === 'add_to_favorites' ? 'Add' : 'Remove'} ${productTitle} ${action === 'add_to_favorites' ? 'to' : 'from'} favorites`
      );
    });
  }

  /**
   * Show user feedback for favorite actions
   * @param {string} productTitle - Product title
   * @param {string} action - 'added' or 'removed'
   */
  showFeedback(productTitle, action) {
    // Create and show toast notification
    const toast = document.createElement('div');
    toast.className = 'favorites-toast';
    toast.innerHTML = `
      <div class="favorites-toast__content">
        <svg class="favorites-toast__icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
        </svg>
        <span class="favorites-toast__text">
          ${productTitle} ${action === 'added' ? 'added to' : 'removed from'} favorites
        </span>
      </div>
    `;

    document.body.appendChild(toast);

    // Trigger animation
    requestAnimationFrame(() => {
      toast.classList.add('favorites-toast--show');
    });

    // Remove after delay
    setTimeout(() => {
      toast.classList.remove('favorites-toast--show');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }

  /**
   * Get all favorites (public method for other components)
   * @returns {Array}
   */
  getAllFavorites() {
    return [...this.favorites];
  }

  /**
   * Clear all favorites
   */
  clearAllFavorites() {
    this.saveFavorites([]);
    this.updateFavoritesDisplay();
  }

  /**
   * Get favorites count
   * @returns {number}
   */
  getFavoritesCount() {
    return this.favorites.length;
  }
}

// Initialize favorites manager when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.favoritesManager = new FavoritesManager();
  });
} else {
  window.favoritesManager = new FavoritesManager();
}

// Export for use in other scripts
window.FavoritesManager = FavoritesManager;
