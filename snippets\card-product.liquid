{%- comment -%}
  Enhanced Product Card with Color Swatch Support

  Purpose: Renders product cards with optional color variant swatches
  - Hover preview swaps card image to variant media
  - Click behavior configurable: update links or quick-add
  - Accessibility-first with radio inputs and proper labeling

  Usage: {% render 'card-product', product: product, show_swatches: true %}

  Parameters:
  - product: required product object
  - show_swatches: boolean, renders color swatches when true
  - enable_hover_preview: boolean, enables hover preview functionality
  - max_swatches: number, limits displayed swatches (default 5)
  - card_click_behavior: 'link_with_variant' or 'quick_add_variant'
  - show_quick_add: boolean, shows quick add button
  - show_favorites: boolean, shows heart icon for favorites
  - show_new_tag: boolean, shows new tag for recently added products
{%- endcomment -%}

{%- liquid
  # Handle alternative parameter names for product
  if card_product
    assign product = card_product
  endif
  
  assign show_swatches = show_swatches | default: false
  assign enable_hover_preview = enable_hover_preview | default: false
  assign max_swatches = max_swatches | default: 5
  
  comment
    TEMPORARY DEBUG - Remove this after testing
  endcomment
  assign card_click_behavior = card_click_behavior | default: 'link_with_variant'
  assign show_quick_add = show_quick_add | default: false
  assign enable_favorites = enable_favorites | default: false
  assign show_new_badges = show_new_badges | default: false
  assign grid_position = grid_position | default: 999
  assign columns = columns | default: 4
  assign enable_zoom_on_cards = enable_zoom_on_cards | default: false
  assign enable_reveal_on_cards = enable_reveal_on_cards | default: false
  assign reveal_duration = reveal_duration | default: blank
  assign reveal_delay = reveal_delay | default: blank
  assign reveal_ease = reveal_ease | default: blank

  # Base product URL within collection for card links
  assign base_card_url = product.url | within: collection

  # Above-the-fold optimization
  assign is_above_fold = false
  if grid_position < columns
    assign is_above_fold = true
  endif

  assign color_option = blank
  assign color_values = blank

  # Find color option
  for option in product.options_with_values
    assign option_name = option.name | downcase
    if option_name contains 'color' or option_name contains 'colour'
      assign color_option = option
      assign color_values = option.values
      break
    endif
  endfor

  # If no color option found and only one option exists, use it
  unless color_option
    if product.options_with_values.size == 1
      assign color_option = product.options_with_values[0]
      assign color_values = color_option.values
    endif
  endunless  # Determine default media for the card (prefer merchant's variant order)
  assign card_media = product.featured_media

  if color_option and color_option.values.size > 0
    assign first_color = color_option.values | first
    assign chosen_variant = blank
    assign chosen_pos = 9999
    for v in product.variants
      assign v_color = v.option1
      if color_option.position == 2
        assign v_color = v.option2
      elsif color_option.position == 3
        assign v_color = v.option3
      endif
      if v.available and v_color == first_color and v.position < chosen_pos
        assign chosen_variant = v
        assign chosen_pos = v.position
      endif
    endfor
    if chosen_variant and chosen_variant.featured_media
      assign card_media = chosen_variant.featured_media
    endif
  else
    assign fav = product.selected_or_first_available_variant
    if fav and fav.featured_media
      assign card_media = fav.featured_media
    endif
  endif


  # Build variant-to-media mapping for swatches
  assign variant_media_map = blank
  if show_swatches and color_option
    assign variant_media_map = ''
    for variant in product.variants
      if variant.available
        assign variant_color = variant.option1
        if color_option.position == 2
          assign variant_color = variant.option2
        elsif color_option.position == 3
          assign variant_color = variant.option3
        endif

        # Get the variant's featured media (single object, not a loop)
        assign variant_media = variant.featured_media

        # Fallback to product featured media
        unless variant_media
          assign variant_media = product.featured_media
        endunless

        if variant_media
          assign map_entry = variant.id | append: ':' | append: variant_media.id | append: ':' | append: variant_color | append: ';'
          assign variant_media_map = variant_media_map | append: map_entry
        endif
      endif
    endfor
  endif
-%}

<div
  class="card-wrapper underline-links-hover"
  data-product-card
  data-product-handle="{{ product.handle }}"
  {%- if enable_hover_preview -%}data-enable-hover-preview="true"{%- endif -%}
  {%- if card_click_behavior -%}data-card-click-behavior="{{ card_click_behavior }}"{%- endif -%}
>
  <div class="card card--{{ settings.card_style }}{% unless product.available %} card--sold-out{% endunless %}{% if product.compare_at_price > product.price and product.available %} card--on-sale{% endif %}">

    {%- comment -%} Card Media Configuration {%- endcomment -%}
    {%- liquid
      # Set aspect ratio from section setting or sensible default
      assign card_aspect = card_image_aspect_ratio | default: '3/4'

      # Calculate responsive image sizing based on grid columns
      # Use if/elsif to avoid multi-value 'when' which can error in some Liquid versions
      if columns <= 2
        assign base_image_width = 1200
        assign base_widths_attr = '800, 1000, 1200, 1400'
        assign sizes_attr = '(min-width: 990px) 50vw, 100vw'
      elsif columns == 3
        assign base_image_width = 1000
        assign base_widths_attr = '600, 800, 1000, 1200'
        assign sizes_attr = '(min-width: 990px) 33vw, 50vw'
      else
        assign base_image_width = 800
        assign base_widths_attr = '400, 600, 800, 1000'
        assign sizes_attr = '(min-width: 990px) calc(100vw / 4), 50vw'
      endif

      # Smart resolution adjustment for aspect ratio mismatches
      # This solves the problem where landscape images displayed in portrait containers
      # (or vice versa) lose resolution due to cropping. By detecting significant 
      # aspect ratio differences, we request higher resolution images to maintain
      # quality in the visible cropped area.
      assign crop_factor = 1.0
      if card_media and card_media.aspect_ratio
        # Parse target aspect ratio from card_aspect (e.g., "3/4" -> 0.75)
        assign aspect_parts = card_aspect | split: '/'
        if aspect_parts.size == 2
          assign target_width = aspect_parts[0] | times: 1.0
          assign target_height = aspect_parts[1] | times: 1.0
          assign target_aspect = target_width | divided_by: target_height
          
          # Calculate how much the source differs from target
          assign source_aspect = card_media.aspect_ratio
          
          # Determine crop factor - when significant cropping occurs, request higher resolution
          if source_aspect > target_aspect
            # Source is wider than target (landscape in portrait container)
            assign crop_factor = source_aspect | divided_by: target_aspect
          else
            # Source is taller than target (portrait in landscape container)  
            assign crop_factor = target_aspect | divided_by: source_aspect
          endif
          
          # Cap crop factor to avoid excessive image requests
          if crop_factor > 2.5
            assign crop_factor = 2.5
          endif
          
          # Only apply crop factor if significant (>20% difference)
          if crop_factor < 1.2
            assign crop_factor = 1.0
          endif
        endif
      endif

      # Apply crop factor to image sizing
      assign image_width = base_image_width | times: crop_factor | round
      
      # Apply crop factor to all width breakpoints
      assign width_list = base_widths_attr | split: ', '
      assign adjusted_widths = ''
      for width_str in width_list
        assign width_num = width_str | times: crop_factor | round
        if adjusted_widths != ''
          assign adjusted_widths = adjusted_widths | append: ', ' | append: width_num
        else
          assign adjusted_widths = width_num | append: ''
        endif
      endfor
      assign widths_attr = adjusted_widths

      # Better object position for fashion/clothing - show center for collection grids
      assign default_object_position = 'center'
    -%}

    <div class="card__media"
         style="--card-aspect: {{ card_aspect }}; --card-object-position: {{ product.metafields.theme.card_object_position | default: default_object_position }};">
      {%- assign loading_value = 'lazy' -%}
      {%- assign fetchpriority_value = 'auto' -%}

      {%- if is_above_fold -%}
        {%- assign loading_value = 'eager' -%}
        {%- assign fetchpriority_value = 'high' -%}
      {%- endif -%}

      {%- comment -%} Card media (no theme hover effect) {%- endcomment -%}
      <div class="media media--transparent{% if enable_zoom_on_cards %} zoom-on-scroll{% endif %}"
           {% if enable_zoom_on_cards %}data-zoom-on-scroll{% endif %}
           {% if enable_reveal_on_cards %}data-reveal-on-scroll{% endif %}
           {% if enable_reveal_on_cards and reveal_duration %}data-reveal-duration="{{ reveal_duration }}"{% endif %}
           {% if enable_reveal_on_cards and reveal_delay %}data-reveal-delay="{{ reveal_delay }}"{% endif %}
           {% if enable_reveal_on_cards and reveal_ease %}data-reveal-ease="{{ reveal_ease }}"{% endif %}>
        <a href="{{ base_card_url }}" class="full-unstyled-link card-media__link" aria-label="{{ product.title | escape }}">
          {{- card_media
            | image_url: width: image_width
            | image_tag:
              widths: widths_attr,
              sizes: sizes_attr,
              loading: loading_value,
              fetchpriority: fetchpriority_value,
              decoding: 'async',
              alt: product.title,
              class: 'motion-reduce card-media__primary'
          -}}
          {%- comment -%} Preview layer used for swatch hover crossfades {%- endcomment -%}
          <img class="card-media__preview" alt="" aria-hidden="true" hidden width="1" height="1">
        </a>

        {%- comment -%} Favorites Heart Icon {%- endcomment -%}
        {%- if enable_favorites -%}
          <button 
            class="card__favorite-btn" 
            type="button"
            data-product-id="{{ product.id }}"
            data-product-handle="{{ product.handle }}"
            data-product-title="{{ product.title | escape }}"
            aria-label="{{ 'products.product.add_to_favorites' | t: product_title: product.title }}"
            data-favorite-toggle
          >
            <svg class="card__favorite-icon card__favorite-icon--outline" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
            </svg>
            <svg class="card__favorite-icon card__favorite-icon--filled" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
            </svg>
          </button>
        {%- endif -%}

        {%- comment -%} NEW Tag {%- endcomment -%}
        {%- if show_new_badges -%}
          {%- liquid
            assign thirty_days_ago = 'now' | date: '%s' | minus: 2592000
            assign product_created = product.created_at | date: '%s'
            assign is_new_product = false
            if product_created >= thirty_days_ago
              assign is_new_product = true
            endif
          -%}
          
          {%- if is_new_product -%}
            <div class="card__new-tag">
              <span class="card__new-tag-text">{{ 'products.product.new' | t }}</span>
            </div>
          {%- endif -%}
        {%- endif -%}
      </div>
    </div>

    {%- comment -%} Card Content {%- endcomment -%}
    <div class="card__content">
      <div class="card__information">
        <h3 class="card__heading">
          {%- assign base_card_url = product.url | within: collection -%}
          <a href="{{ base_card_url }}"
             id="StandardCardNoMediaLink-{{ product.id }}"
             class="full-unstyled-link card-link"
             data-link-with-variant="true"
             aria-labelledby="StandardCardNoMediaLink-{{ product.id }} NoMediaStandardBadge-{{ product.id }}">
            {{ product.title | escape }}
          </a>
        </h3>
      </div>

      {%- comment -%} Price {%- endcomment -%}
      <div class="price">
        {%- render 'price', product: product, price_class: 'price--on-sale' -%}
      </div>

      {%- comment -%} Color Swatches {%- endcomment -%}
      {%- if show_swatches and color_option -%}
        <!-- DEBUG: SWATCH RENDERING STARTED -->
        <div class="card__swatches" role="group" aria-label="Color options">
          {%- assign swatch_count = 0 -%}
          {%- assign displayed_colors = blank -%}

          {%- for color_value in color_values -%}
            {%- unless displayed_colors contains color_value -%}
              {%- assign displayed_colors = displayed_colors | append: color_value | append: ',' -%}
              {%- assign swatch_count = swatch_count | plus: 1 -%}

              {%- if swatch_count <= max_swatches -%}
                {%- comment -%} Find variant for this color {%- endcomment -%}
                {%- assign color_variant = null -%}
                {%- for variant in product.variants -%}
                  {%- assign variant_color = variant.option1 -%}
                  {%- if color_option.position == 2 -%}
                    {%- assign variant_color = variant.option2 -%}
                  {%- elsif color_option.position == 3 -%}
                    {%- assign variant_color = variant.option3 -%}
                  {%- endif -%}

                  {%- if variant_color == color_value -%}
                    {%- assign color_variant = variant -%}
                  {%- endif -%}
                {%- endfor -%}

                {%- if color_variant -%}
                  {%- comment -%} Build responsive sources for this variant {%- endcomment -%}
                  {%- assign media_for_color = color_variant.featured_media | default: card_media -%}
                  {%- assign widths_list = widths_attr | replace: ' ', '' | split: ',' -%}
                  {%- capture variant_srcset -%}
                    {%- for w in widths_list -%}
                      {{ media_for_color | image_url: width: w }} {{ w }}w{% unless forloop.last %}, {% endunless %}
                    {%- endfor -%}
                  {%- endcapture -%}

                  <button
                    type="button"
                    class="swatch swatch--{{ color_value | handle }}{% unless color_variant.available %} swatch--sold-out{% endunless %}"
                    aria-label="{{ color_value | escape }}{% unless color_variant.available %} ({{ 'products.product.sold_out' | t }}){% endunless %}"
                    title="{{ color_value | escape }}{% unless color_variant.available %} - {{ 'products.product.sold_out' | t }}{% endunless %}"
                    data-variant-id="{{ color_variant.id }}"
                    data-media-id="{{ media_for_color.id }}"
                    data-color-name="{{ color_value | escape }}"
                    data-src="{{ media_for_color | image_url: width: image_width }}"
                    data-srcset="{{ variant_srcset | strip }}"
                    data-sizes="{{ sizes_attr | strip }}"
                    {% unless color_variant.available %}data-sold-out="true"{% endunless %}
                  >
                    <span class="visually-hidden">{{ color_value | escape }}{% unless color_variant.available %} ({{ 'products.product.sold_out' | t }}){% endunless %}</span>
                  </button>
                {%- endif -%}
              {%- endif -%}
            {%- endunless -%}
          {%- endfor -%}

          {%- if color_values.size > max_swatches -%}
            <div class="swatch-more">+{{ color_values.size | minus: max_swatches }}</div>
          {%- endif -%}
        </div>

        {%- comment -%} Sold Out Indicator (shown when sold-out swatch is clicked) {%- endcomment -%}
        <div class="swatch-sold-out-indicator" hidden>
          <span class="sold-out-text">{{ 'products.product.sold_out' | t }}</span>
        </div>
      {%- endif -%}

      {%- comment -%} Sale Badge (conditionally shown) {%- endcomment -%}
      {%- if settings.show_sale_badge and product.compare_at_price > product.price and product.available -%}
        <div class="card__badge">
          <span class="badge badge--bottom-left color-{{ settings.sale_badge_color_scheme }}">
            {{ 'products.product.on_sale' | t }}
          </span>
        </div>
      {%- endif -%}
    </div>

    {%- comment -%} Quick Add Button {%- endcomment -%}
    {%- if show_quick_add -%}
      <div class="quick-add no-js-hidden">
        {%- liquid
          assign is_trivial = false
          if product.has_only_default_variant or product.variants.size == 1
            assign is_trivial = true
          endif
        -%}
        <button
          class="button button--full-width button--secondary quick-add__button"
          data-quick-add
          data-product-url="{{ product.url | within: collection }}"
          data-product-id="{{ product.id }}"
          {% if is_trivial %}data-direct="true"{% endif %}
          aria-haspopup="dialog"
        >
          <span class="quick-add__text">
            {% if is_trivial %}{{ 'products.product.add_to_cart' | t }}{% else %}{{ 'products.product.choose_options' | t }}{% endif %}
          </span>
          <div class="loading-overlay hidden">
            <div class="loading-overlay__spinner">
              <svg
                aria-hidden="true"
                focusable="false"
                class="spinner"
                viewBox="0 0 66 66"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  class="path"
                  fill="none"
                  stroke-width="6"
                  cx="33"
                  cy="33"
                  r="30"
                ></circle>
              </svg>
            </div>
          </div>
        </button>
      </div>
    {%- endif -%}
  </div>
</div>

{%- comment -%}theme-check-disable RemoteAsset{%- endcomment -%}
{% style %}
  /* Responsive card media container */
  .card__media .media {
    position: relative;
    aspect-ratio: var(--card-aspect);
    overflow: hidden;
  }

  /* Mobile adjustment for taller aspect ratio to show more image */
  @media (max-width: 749px) {
    .card__media .media {
      aspect-ratio: var(--card-aspect-mobile, 3/5); /* Taller on mobile */
    }
  }

  /* Base and preview images positioned identically */
  .card__media .media .card-media__primary,
  .card__media .media .card-media__preview {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
    object-position: var(--card-object-position, center top);
  }

  /* Preview fades only */
  .card-media__preview {
    opacity: 0;
    pointer-events: none;
    transition: opacity 160ms ease;
  }

  /* Micro-optimizations for smooth fades */
  .card-media__primary,
  .card-media__preview {
    will-change: opacity;
    backface-visibility: hidden;
    contain: paint;
  }

  /* Card Content Styling */
  .card__content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  /* Align quick-add with card content padding so button lines up horizontally */
  .quick-add {
    padding: 0 0.5rem 1rem; /* match .card__content horizontal padding and add bottom spacing */
    box-sizing: border-box;
  }

  /* Card Information Styling */
  .card__information {
    margin: 0;
  }

  .card__heading {
    margin: 0;
    font-size: 1rem;
    font-weight: 400; /* normal weight, not bold */
    line-height: 1.2;
  }

  @media screen and (min-width: 990px) {
    .card__heading {
      font-size: 1.125rem; /* slightly larger on desktop */
    }
    .swatch {
      width: 0.875rem;
      height: 0.875rem;
    }
  }

  .card__heading a,
  .card-link {
    color: rgb(var(--color-foreground));
    text-decoration: none;
    display: block;
  }

  .card__heading a:hover,
  .card-link:hover {
    color: rgb(var(--color-foreground));
    text-decoration: none;
  }

  /* Override any parent underline styles for card links */
  .card-wrapper.underline-links-hover .card__heading a,
  .card-wrapper.underline-links-hover .card-link {
    text-decoration: none !important;
  }

  .card-wrapper.underline-links-hover .card__heading a:hover,
  .card-wrapper.underline-links-hover .card-link:hover {
    text-decoration: none !important;
  }

  /* Card Swatches Styling */
  .card__swatches {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    justify-content: flex-start;
  }

  /* Price Styling */
  .card__content .price {
    color: rgba(var(--color-foreground), 0.6); /* grey color */
    font-size: 1rem; /* Match product title size */
  }

  .card__badge {
    position: relative;
    margin: 0;
  }

  .swatch-wrapper {
    position: relative;
  }

  .swatch {
    display: block;
    width: 0.875rem;
    height: 0.875rem;
    min-width: 0.875rem; /* Prevent compression */
    min-height: 0.875rem; /* Prevent compression */
    max-width: 0.875rem; /* Prevent expansion */
    max-height: 0.875rem; /* Prevent expansion */
    border-radius: 50%;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    background-color: #ccc; /* Fallback background */
    flex-shrink: 0; /* Prevent flex compression */
    aspect-ratio: 1 / 1; /* Ensure perfect square aspect ratio */
    box-sizing: border-box; /* Include border in size calculation */
    
    /* Button-specific overrides */
    padding: 0; /* Remove default button padding */
    margin: 0; /* Remove default button margin */
    font-size: 0; /* Remove font size influence */
    line-height: 1; /* Reset line height */
    text-align: center; /* Center any content */
    vertical-align: top; /* Prevent baseline alignment issues */
    outline: none; /* Remove default focus outline */
  }

  .swatch:hover,
  .swatch:focus {
    transform: scale(1.1);
    box-shadow: 0 0 0 2px rgb(var(--color-background)), 0 0 0 4px rgb(var(--color-foreground));
  }

  .swatch-input:checked + .swatch {
    transform: scale(1.1);
    box-shadow: 0 0 0 2px rgb(var(--color-background)), 0 0 0 4px rgb(var(--color-foreground));
  }

  /* Sold-out swatch styling */
  .swatch--sold-out {
    opacity: 0.5;
    cursor: not-allowed;
    position: relative;
  }

  .swatch--sold-out::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1px;
    height: 120%;
    background-color: rgb(var(--color-foreground));
    transform: translate(-50%, -50%) rotate(45deg);
    pointer-events: none;
  }

  .swatch--sold-out:hover,
  .swatch--sold-out:focus {
    transform: none;
    box-shadow: none;
  }

  /* Sold-out indicator styling */
  .swatch-sold-out-indicator {
    display: flex;
    justify-content: flex-end;
    margin-top: 0.5rem;
    padding-right: 0; /* Remove padding since card__content now has padding */
  }

  /* Ensure hidden attribute always hides the indicator */
  .swatch-sold-out-indicator[hidden] { display: none !important; }


  .sold-out-text {
    font-size: 0.75rem;
    color: rgb(var(--color-foreground));
    opacity: 0.7;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  /* Enhanced color mappings with !important for debugging */
  .swatch--green,
  .swatch--forest-green,
  .swatch--olive,
  .swatch--forest { background-color: #4a5d3a !important; }

  .swatch--pink,
  .swatch--light-pink,
  .swatch--rose,
  .swatch--dusty-pink { background-color: #f8bbd9 !important; }

  .swatch--black { background-color: #000000 !important; }
  .swatch--white { background-color: #ffffff !important; border: 2px solid #e0e0e0 !important; }
  .swatch--red { background-color: #dc2626 !important; }
  .swatch--blue { background-color: #2563eb !important; }
  .swatch--yellow { background-color: #facc15 !important; }
  .swatch--purple { background-color: #9333ea !important; }
  .swatch--orange { background-color: #ea580c !important; }
  .swatch--brown { background-color: #92400e !important; }
  .swatch--gray,
  .swatch--grey { background-color: #6b7280 !important; }
  .swatch--navy { background-color: #1e3a8a !important; }
  .swatch--beige { background-color: #f5f5dc !important; }
  .swatch--cream { background-color: #fffdd0 !important; }

  /* Fallback for colors not mapped above - always visible */
  .swatch:not([class*="swatch--"]) {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%) !important;
    background-size: 4px 4px;
    background-position: 0 0, 0 2px, 2px -2px, -2px 0px;
    border: 2px solid #ccc !important;
  }

  .visually-hidden {
    position: absolute !important;
    overflow: hidden;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
    clip: rect(0 0 0 0);
    word-wrap: normal !important;
  }

  /* Favorites Button Styling */
  .card__favorite-btn {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    z-index: 2;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .card__favorite-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .card__favorite-btn:focus {
    outline: 2px solid rgb(var(--color-foreground));
    outline-offset: 2px;
  }

  .card__favorite-icon {
    color: rgb(var(--color-foreground));
    transition: all 0.2s ease;
  }

  .card__favorite-icon--filled {
    display: none;
    color: #e53e3e;
  }

  /* When favorited */
  .card__favorite-btn[data-favorited="true"] .card__favorite-icon--outline {
    display: none;
  }

  .card__favorite-btn[data-favorited="true"] .card__favorite-icon--filled {
    display: block;
  }

  .card__favorite-btn[data-favorited="true"]:hover .card__favorite-icon--filled {
    color: #c53030;
    transform: scale(1.1);
  }

  /* NEW Tag Styling */
  .card__new-tag {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    z-index: 2;
    background: rgb(var(--color-foreground));
    color: rgb(var(--color-background));
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1;
  }

  .card__new-tag-text {
    display: block;
  }

  /* Responsive adjustments */
  @media screen and (max-width: 749px) {
    .card__favorite-btn {
      width: 2rem;
      height: 2rem;
      top: 0.5rem;
      right: 0.5rem;
    }

    .card__favorite-icon {
      width: 16px;
      height: 16px;
    }

    .card__new-tag {
      top: 0.5rem;
      left: 0.5rem;
      padding: 0.125rem 0.375rem;
      font-size: 0.6875rem;
    }
  }
{% endstyle %}
