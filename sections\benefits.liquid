{% comment %}
  Benefits section: cards grid with optional background image, colors, and layout controls.
{% endcomment %}

{%- liquid
  assign bg_d = section.settings.bg_image_desktop
  assign bg_m = section.settings.bg_image_mobile

  assign container_width = section.settings.container_width | default: 'inherit'
  assign desktop_offset_top = section.settings.desktop_offset_top | default: 0
  assign desktop_offset_bottom = section.settings.desktop_offset_bottom | default: 0
  assign mobile_offset_top = section.settings.mobile_offset_top | default: 0
  assign mobile_offset_bottom = section.settings.mobile_offset_bottom | default: 0
-%}

<section class="benefits color-{{ section.settings.color_scheme | default: 'background-1' }}"
         data-section="{{ section.id }}"
         {% if section.settings.mark_as_rail_end %}data-rail-end data-rail-edge="{{ section.settings.rail_end_edge | default: 'top' }}"{% endif %}>
  <div class="benefits__bg" aria-hidden="true">
    {% if bg_d or bg_m %}
      <picture>
        {% if bg_m %}
          <source media="(max-width: 749px)" srcset="{{ bg_m | image_url: width: 1500 }} 1500w" sizes="100vw">
        {% endif %}
        {% if bg_d %}
          {{ bg_d | image_url: width: 2400 | image_tag: widths: '1200, 1600, 2000, 2400', sizes: '100vw', loading: 'lazy', decoding: 'async', alt: '' }}
        {% endif %}
      </picture>
    {% endif %}
  </div>

  <div class="benefits__inner{% if container_width != 'inherit' %} benefits__inner--custom{% endif %} page-width"{% if section.settings.mark_as_rail_end %} data-rail-bottom{% endif %}>
    <div class="benefits__grid">
      {%- for block in section.blocks -%}
        {%- liquid
          assign heading = block.settings.heading
          assign text = block.settings.text
          assign label = block.settings.link_label
          assign url = block.settings.link_url
          assign icon = block.settings.icon
        -%}
        <div class="benefit" {{ block.shopify_attributes }}>
          {% if icon %}
            <div class="benefit__icon">
              {{ icon | image_url: width: 96 | image_tag: widths: '48, 64, 96', sizes: '64px', loading: 'lazy', decoding: 'async', alt: '' }}
            </div>
          {% endif %}
          {% if heading %}
            <h3 class="benefit__title">{{ heading }}</h3>
          {% endif %}
          {% if text %}
            <div class="benefit__text rte">{{ text }}</div>
          {% endif %}
          {% if label and url %}
            <a class="benefit__link" href="{{ url }}">{{ label }}</a>
          {% endif %}
        </div>
      {%- endfor -%}
      {% if section.blocks.size == 0 %}
        <div class="benefit">
          <h3 class="benefit__title">Add benefits</h3>
          <div class="benefit__text">Use the editor to add up to six benefit items.</div>
        </div>
      {% endif %}
    </div>
  </div>
</section>

{% style %}
  #shopify-section-{{ section.id }} {
    margin-top: {{ mobile_offset_top }}px; margin-bottom: {{ mobile_offset_bottom }}px;
  }
  @media (min-width: 990px) {
    #shopify-section-{{ section.id }} {
      margin-top: {{ desktop_offset_top }}px; margin-bottom: {{ desktop_offset_bottom }}px;
    }
  }

  #shopify-section-{{ section.id }} .benefits {
    position: relative;
    background: {{ section.settings.section_bg | default: 'transparent' | color_background }};
    color: {{ section.settings.text_color | default: 'inherit' }};
  }
  #shopify-section-{{ section.id }} .benefits__bg { position: absolute; inset: 0; z-index: 0; }
  #shopify-section-{{ section.id }} .benefits__bg picture,
  #shopify-section-{{ section.id }} .benefits__bg img { width: 100%; height: 100%; object-fit: cover; display: block; }
  #shopify-section-{{ section.id }} .benefits__inner { position: relative; z-index: 1; max-width: var(--container-max, var(--page-width)); margin: 0 auto; padding: 2rem 1rem; }
  #shopify-section-{{ section.id }} .benefits__inner.page-width { padding-left: 1rem; padding-right: 1rem; }
  @media (min-width: 750px) {
    #shopify-section-{{ section.id }} .benefits__inner { padding: 3rem 1.25rem; }
  }
  #shopify-section-{{ section.id }} .benefits__inner.benefits__inner--custom { max-width: var(--container-max, var(--page-width)); }

  #shopify-section-{{ section.id }} .benefits__grid { display: grid; gap: 1rem; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); }
  @media (min-width: 990px){ #shopify-section-{{ section.id }} .benefits__grid { gap: 1.25rem; } }

  #shopify-section-{{ section.id }} .benefit {
    background: {{ section.settings.block_bg | default: 'transparent' | color_background }};
    border-radius: 12px;
    padding: 1rem 1.25rem;
  }
  #shopify-section-{{ section.id }} .benefit__icon { line-height: 0; margin-bottom: .5rem; }
  #shopify-section-{{ section.id }} .benefit__title { margin: 0 0 .25rem; color: {{ section.settings.heading_color | default: 'inherit' }}; }
  #shopify-section-{{ section.id }} .benefit__text { margin: 0; opacity: .9; }
  #shopify-section-{{ section.id }} .benefit__link { display: inline-block; margin-top: .5rem; color: {{ section.settings.link_color | default: 'inherit' }}; text-decoration: underline; }
  #shopify-section-{{ section.id }} .benefit__link:hover { color: {{ section.settings.link_hover_color | default: section.settings.link_color | default: 'inherit' }}; }

  {% if container_width == 'narrow' %}
    #shopify-section-{{ section.id }} .benefits__inner { --container-max: 90rem; }
  {% elsif container_width == 'wide' %}
    #shopify-section-{{ section.id }} .benefits__inner { --container-max: 110rem; }
  {% endif %}
{% endstyle %}

{% schema %}
{
  "name": "Benefits",
  "max_blocks": 6,
  "settings": [
    { "type": "header", "content": "Runway sticky rail" },
    { "type": "checkbox", "id": "mark_as_rail_end", "label": "Mark this section as rail end", "default": false, "info": "When enabled, the sticky rail will release at this section. Defaults to the section top edge." },
    { "type": "select", "id": "rail_end_edge", "label": "Release at edge", "default": "top", "options": [ {"value":"top","label":"Top"}, {"value":"bottom","label":"Bottom"} ] },
    { "type": "header", "content": "Background image" },
    { "type": "image_picker", "id": "bg_image_desktop", "label": "Background image (desktop)" },
    { "type": "image_picker", "id": "bg_image_mobile", "label": "Background image (mobile)" },

    { "type": "header", "content": "Colors" },
    { "type": "color_scheme", "id": "color_scheme", "label": "Color scheme", "default": "background-1" },
    { "type": "color_background", "id": "section_bg", "label": "Section background" },
    { "type": "color_background", "id": "block_bg", "label": "Block background" },
    { "type": "color", "id": "heading_color", "label": "Heading" },
    { "type": "color", "id": "text_color", "label": "Text" },
    { "type": "color", "id": "link_color", "label": "Link" },
    { "type": "color", "id": "link_hover_color", "label": "Link (hover)" },

    { "type": "header", "content": "Layout" },
    { "type": "select", "id": "container_width", "label": "Container width", "default": "inherit", "options": [
      { "value": "inherit", "label": "Inherit" },
      { "value": "narrow", "label": "Narrow" },
      { "value": "wide", "label": "Wide" }
    ] },
    { "type": "range", "id": "desktop_offset_top", "label": "Desktop offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "desktop_offset_bottom", "label": "Desktop offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_top", "label": "Mobile offset top", "min": 0, "max": 120, "step": 4, "default": 0 },
    { "type": "range", "id": "mobile_offset_bottom", "label": "Mobile offset bottom", "min": 0, "max": 160, "step": 4, "default": 0 }
  ],
  "blocks": [
    {
      "type": "item",
      "name": "Benefit",
      "settings": [
        { "type": "image_picker", "id": "icon", "label": "Icon" },
        { "type": "text", "id": "heading", "label": "Heading" },
        { "type": "richtext", "id": "text", "label": "Text" },
        { "type": "text", "id": "link_label", "label": "Link label" },
        { "type": "url", "id": "link_url", "label": "Link URL" }
      ]
    }
  ],
  "presets": [
    {
      "name": "Benefits",
      "blocks": [
        { "type": "item", "settings": { "heading": "Fast shipping", "text": "<p>2–3 day delivery on all orders.</p>", "link_label": "Learn more" } },
        { "type": "item", "settings": { "heading": "Free returns", "text": "<p>30-day hassle-free returns.</p>", "link_label": "Details" } },
        { "type": "item", "settings": { "heading": "Secure checkout", "text": "<p>256-bit encryption & trusted payments.</p>", "link_label": "How it works" } }
      ]
    }
  ]
}
{% endschema %}
