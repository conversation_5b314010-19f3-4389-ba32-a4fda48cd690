(function(){
  function init(el){
    const track = el.querySelector('.js-snap-track');
    if(!track) return;
    const prev = el.querySelector('.js-snap-prev');
    const next = el.querySelector('.js-snap-next');

    function snapBy(dir){
      const slide = track.querySelector(':scope > *');
      const slideWidth = slide ? slide.getBoundingClientRect().width : 300;
      track.scrollBy({ left: dir * (slideWidth + 16), behavior: 'smooth' });
    }

    if(prev) prev.addEventListener('click', () => snapBy(-1));
    if(next) next.addEventListener('click', () => snapBy(1));

    track.addEventListener('keydown', (e)=>{
      if(e.key === 'ArrowRight'){ e.preventDefault(); snapBy(1); }
      if(e.key === 'ArrowLeft'){ e.preventDefault(); snapBy(-1); }
      if(e.key === 'Home'){ e.preventDefault(); track.scrollTo({ left: 0, behavior:'smooth' }); }
      if(e.key === 'End'){ e.preventDefault(); track.scrollTo({ left: track.scrollWidth, behavior:'smooth' }); }
    });
  }

  function mount(container){
    container.querySelectorAll('.js-snap-slider').forEach(init);
  }

  document.addEventListener('DOMContentLoaded', ()=> mount(document));
  document.addEventListener('shopify:section:load', (e)=> mount(e.target));
  document.addEventListener('shopify:section:select', (e)=> mount(e.target));
})();
