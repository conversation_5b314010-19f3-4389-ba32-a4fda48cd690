{% comment %}
  Runway Track Attributes Snippet
  
  Purpose: Automatically adds data-runway-follow and data-runway-stop attributes
           to sections for the Runway sticky panel track system.
  
  Usage: Include this snippet in layout/theme.liquid before </body>:
         {% render 'runway-track-attributes' %}
  
  Only runs on product pages with Runway layout.
{% endcomment %}

{% if template.name == 'product' %}
<script>
(function(){
  'use strict';
  
  // Only run on desktop
  if (window.innerWidth < 990) return;
  
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
  
  function init() {
    // Find the product section (must have Runway/stack layout)
    const productSection = document.querySelector('.shopify-section:has(.product--layout-runway), .shopify-section:has(.product[data-layout="stack"])');
    if (!productSection) return;
    
    console.log('[Runway Track] Product section found:', productSection.id);
    
    // Configuration: which sections to include/exclude
    const config = {
      // Sections that should extend the sticky track
      follow: [
        '.product-recs',              // Product recommendations
        '[data-recent-container]',    // Recently viewed
        '[data-keep-exploring]',      // Keep exploring
        '.complementary-products',    // Shop the look
        '.related-products',          // Related products
      ],
      // Sections where the panel should release (stop tracking)
      stop: [
        '.footer',                    // Footer
        '.newsletter',                // Newsletter signup
        '.newsletter-section',        // Newsletter section
        '[data-newsletter]',          // Newsletter attribute
        '.promo-banner',              // Promotional banners
      ],
      // Maximum number of sections to include (safety limit)
      maxFollow: 4
    };
    
    let followCount = 0;
    let currentSection = productSection.nextElementSibling;
    
    while (currentSection && currentSection.classList.contains('shopify-section')) {
      // Check if this section should stop the track
      const shouldStop = config.stop.some(selector => currentSection.querySelector(selector));
      
      if (shouldStop) {
        console.log('[Runway Track] Stop point found:', currentSection.id);
        currentSection.setAttribute('data-runway-stop', '');
        break; // Stop processing after the stop point
      }
      
      // Check if this section should be followed
      const shouldFollow = config.follow.some(selector => currentSection.querySelector(selector));
      
      if (shouldFollow && followCount < config.maxFollow) {
        console.log('[Runway Track] Follow section found:', currentSection.id);
        currentSection.setAttribute('data-runway-follow', '');
        followCount++;
      }
      
      currentSection = currentSection.nextElementSibling;
    }
    
    console.log('[Runway Track] Setup complete. Following', followCount, 'sections.');
    
    // Trigger a recalculation if the track script is already loaded
    const productRoot = productSection.querySelector('.product');
    if (productRoot) {
      // Dispatch a custom event that the track script can listen for
      window.dispatchEvent(new CustomEvent('runway:track-attributes-ready'));
    }
  }
})();
</script>
{% endif %}

