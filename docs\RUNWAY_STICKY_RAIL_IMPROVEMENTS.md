# Runway Sticky Rail — Hardening Plan (Marketplace‑grade)

## Overview
This document captures the follow‑ups to make the sticky rail in `sections/product.liquid` deterministic, fast, and integration‑safe for a marketplace theme.

## Goals
- Predictable release exactly at the intended content edge
- No layout thrash on scroll; smooth at 60fps
- No cross‑section CSS bleed; minimal `!important`
- Safe in Theme Editor (attach once, tear down cleanly)
- Explicit merchant controls with sensible defaults
- Accessible, resilient fallbacks when JS is unavailable

## Changes Already Implemented
- Measure from product root (not wrapper) for release math
- Synchronize height: set `--runway-slider-h` in px from JS
- Use outer rail’s natural height for timing (no inner scroll)
- Prefer inner content anchors (e.g., `.product-recs__inner`, `.benefits__inner`)
- Support `[data-rail-end]`, `[data-rail-bottom]`, `data-rail-edge="top|bottom"`
- Bias rounding via `Math.floor` to avoid 1px gaps
- Remove bottom margin when released to eliminate visible sliver
- Remove left‑anchoring override for `.page-width` containers
- Avoid left viewport gutter by switching to `scrollbar-gutter: stable`

## High‑Impact Improvements
1) Cache geometry; avoid measuring on scroll
- Task: Split `computeRelease()` into `recalcRelease()` (measures) and `updateMode()` (reads cache).
- Trigger recalc on: `resize`, `orientationchange`, `visualViewport` changes, `runway:header-height`, and mutations on the anchor/mates.
- Accept: Scroll handler does O(1) work, no layout reads.

2) Scope marker resolution to this product
- Task: Replace `document.querySelector('[data-rail-end]')` with a search among this product’s sibling `.shopify-section`s only (including itself).
- Accept: Markers on other parts of the page don’t interfere.

3) Guard against double‑binding + add teardown
- Task: Set a section flag (e.g., `data-runway-rail`); bail if present.
- Task: On `shopify:section:unload`, remove all listeners/observers created by the rail.
- Accept: Re‑renders in the Theme Editor don’t multiply listeners.

4) Reduce MutationObserver scope
- Task: Observe only the resolved anchor and its immediate siblings (or `[data-rail-end]` + inner target), not `document.documentElement`.
- Task: Debounce recalc with `requestAnimationFrame` + short `setTimeout` (e.g., 50–100ms).
- Accept: Less chatter; stable measurements.

5) Minimize CSS bleed
- Task: Prefix critical selectors with `#product-{{ section.id }}` instead of broad `.product--layout-runway` where possible.
- Task: Replace some `!important` with specificity via the section id.
- Accept: Other sections/apps are unaffected.

6) Optional nudge for brand precision
- Task: Support `--rail-nudge-y` (default `0px`) and/or `data-rail-nudge="<px>"`; add to release computation.
- Accept: Teams can micro‑tune for anti‑aliasing/rounding across devices.

7) Avoid DOM reparenting where possible
- Task: Try to keep `.product__info` under the section root without moving nodes; ensure the section root is the containing block (`position: relative`) so absolute/fixed work.
- If reparenting remains necessary:
  - Gate with a feature flag; ensure ARIA/focus order remain sensible.
  - Document the behavior for app integrations.
- Accept: Semantics and integrations remain stable.

8) Fallback behavior
- Task: If JS fails or features are absent, degrade to a non‑sticky product info or native `position: sticky` scope.
- Accept: Page remains usable without the advanced rail.

## Secondary Improvements
- Slider inertia: wrap momentum in `prefers-reduced-motion` check.
- Consolidate all event wiring into a single module scope; expose a tiny internal bus if needed.
- Emit a `runway:rail-updated` custom event after each recalc to help third‑party sections synchronize.

## QA / Performance Matrix
- Breakpoints: 990px boundary, large desktop, zoomed (125%), HiDPI (DPR 2+).
- OS/UI: Windows (scrollbar visible), macOS (overlay scrollbars), iPadOS Safari.
- Header states: natural, sticky, overlay; dynamic announcements.
- Content dynamics: recommendations loading, accordions expanding, validation errors.
- Performance: ensure scroll handler does not read layout; confirm via DevTools performance trace.

## Acceptance Criteria
- Rail bottom aligns with the chosen content edge within ±1px across platforms.
- No scroll‑time layout reflow from rail logic (measurements happen on recalc only).
- No duplicate listeners after repeated Theme Editor reloads.
- `theme-check` passes; no regressions to `.page-width` gutters.
- Degrades gracefully when JS is disabled or errors.

## Implementation Notes
- Primary code paths: sections/product.liquid (rail geometry, state toggles, listeners).
- Anchor targets recognized: `.product-recs__inner`, `.benefits__inner`, `.shop-the-look__inner`; explicit `[data-rail-bottom]` takes precedence.
- Public attributes/events: `[data-rail-end]`, `data-rail-edge`, `[data-rail-bottom]`, `runway:header-height` (input), `runway:rail-updated` (proposed output).

## Task Checklist
- [ ] Cache release geometry (no measuring in scroll path)
- [ ] Scope marker search to this product’s siblings
- [ ] Add `data-runway-rail` guard + full teardown on `shopify:section:unload`
- [ ] Narrow MutationObserver scope + debounce
- [ ] Section‑scope CSS, reduce `!important`
- [ ] Implement `--rail-nudge-y` / `data-rail-nudge`
- [ ] Minimize or gate DOM reparenting, preserve ARIA/focus
- [ ] Add no‑JS fallback (native sticky / natural flow)
- [ ] `prefers-reduced-motion` handling for slider inertia
- [ ] Expand docs (Quick Reference + Implementation) with new controls
- [ ] QA pass per matrix above; attach screenshots in PR

